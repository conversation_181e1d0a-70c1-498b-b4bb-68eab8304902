import layoutMenu from '@/components/layoutMenu/index.vue'


export default [
  {
    path: "/terminal/yw",
    name: "terminalyw",
    meta: {
      title: "终端运维",
    },
    component: layoutMenu,
    redirect: "/terminal/yw/terminalOperationUpdate",
    children: [
      {
        meta: {
          title: "终端运维首页",
        },
        path: "terminalOperationHome",
        name: "terminalOperationHome",
        sider: true,
        bread: true,
        component: () => import("@/view/terminal/terminal-operation/operation-home.vue"),
      },
      {
        meta: {
          title: "终端运维更新",
        },
        path: "terminalOperationUpdate",
        name: "terminalOperationUpdate",
        sider: true,
        bread: true,
        component: () => import("@/view/terminal/version/index.vue"),
      },
      {
        meta: {
          title: "终端运维台账",
        },
        path: "terminalOperationRecord",
        name: "terminalOperationRecord",
        sider: true,
        bread: true,
        component: () => import("@/view/terminal/terminal-operation/operation-record.vue"),
      },
    ],
  },
];
