import {
  getBreadCrumbList,
  setTagNavListInLocalstorage,
  setAppTagNavListInLocalstorage,
  getMenuByRouter,
  getTagNavListFromLocalstorage,
  getAppTagNavListFromLocalstorage,
  getHomeRoute,
  getNextRoute,
  getNextAppRoute,
  routeHasExist,
  routeEqual,
  routeEqualNew,
  routeHasExistNew,
  getRouteTitleHandled,
  localSave,
  localRead
} from '@/libs/util'
// import { saveErrorLogger } from '@/api/data'
import router from '@/router'
import config from '@/config'
const {
  homeName
} = config

const closePage = (state, route) => {
  const nextRoute = getNextRoute(state.tagNavList, route)
  state.tagNavList = state.tagNavList.filter(item => {
    return !routeEqual(item, route)
  })
  router.push(nextRoute)
}
const closeAppPage = (state, {
  route,
  key,
  callback
}) => {
  const nextRoute = getNextAppRoute(state.appTagNavList[key], route)
  state.appTagNavList[key] = state.appTagNavList[key].filter(item => {
    return !routeEqualNew(item, route)
  })
  if ((nextRoute.path.indexOf('isTurnFrame_') > -1) && callback) {
    callback(nextRoute)
    return
  }
  router.push(nextRoute)
}
export default {
  state: {
    breadCrumbList: [],
    tagNavList: [],
    appTagNavList: {},
    homeRoute: {},
    local: localRead('local'),
    errorList: [],
    hasReadErrorPage: false,
    manageTag: true,
  },
  getters: {
    menuList: (state, getters, rootState) => getMenuByRouter(rootState.common.routerList, []),
    errorCount: state => state.errorList.length
  },
  mutations: {
    setManageTag(state, payload) {
      state.manageTag = payload
    },
    setBreadCrumb(state, route) {
      state.breadCrumbList = getBreadCrumbList(route, state.homeRoute)
    },
    setHomeRoute(state, routes) {
      state.homeRoute = getHomeRoute(routes, homeName)
    },
    setTagNavList(state, list) {
      console.log(state, list, 'state, list')
      let tagList = []
      if (list) {
        tagList = [...list]
      } else {
        tagList = getTagNavListFromLocalstorage() || []
      }
      tagList = tagList.filter((item, index) =>
        tagList.findIndex(i => i.path === item.path) === index);
      console.log(tagList, 'tagListtagList', list, 'listgetTagNavListFromLocalstorage')
      // if (tagList[0] && tagList[0].path !== homeName) tagList.shift()
      // let homeTagIndex = tagList.findIndex(item => item.path === homeName)
      // if (homeTagIndex > 0) {
      //   let homeTag = tagList.splice(homeTagIndex, 1)[0]
      //   tagList.unshift(homeTag)
      // }
      state.tagNavList = tagList
      setTagNavListInLocalstorage([...tagList])
    },
    clearTagNavList(state) {
      state.tagNavList = []
      setTagNavListInLocalstorage([])
    },
    setAppTagNavList(state, {
      list,
      key
    }) {
      let tagList = []
      if (list) {
        tagList = [...list]
      } else {
        tagList = getAppTagNavListFromLocalstorage(key) || []
      }
      state.appTagNavList[key] = tagList
      setAppTagNavListInLocalstorage(key, tagList)
    },
    closeTag(state, route) {
      let tag = state.tagNavList.filter(item => routeEqual(item, route))
      route = tag[0] ? tag[0] : null
      if (!route) return
      closePage(state, route)
    },
    closeAppTag(state, {
      route,
      key,
      callback
    }) {
      let tag = state.appTagNavList[key].filter(item => routeEqualNew(item, route))
      route = tag[0] ? tag[0] : null
      if (!route) return
      closeAppPage(state, {
        route,
        key,
        callback
      })
    },
    addTag(state, {
      route,
      type = 'unshift'
    }) {
      let router = getRouteTitleHandled(route)
      if (!routeHasExist(state.tagNavList, router)) {
        if (type === 'push') state.tagNavList.push(router)
        else {
          if (router.path === homeName) state.tagNavList.unshift(router)
          else state.tagNavList.splice(1, 0, router)
        }
        setTagNavListInLocalstorage([...state.tagNavList])
      }
    },
    addAppTag(state, {
      route,
      appKey
    }) {
      let router = getRouteTitleHandled(route)
      let tagNavList = state.appTagNavList[appKey]
      if (!tagNavList) {
        tagNavList = []
        state.appTagNavList[appKey] = tagNavList
      }
      if (!routeHasExistNew(tagNavList, router)) {
        tagNavList.push(router)
        setAppTagNavListInLocalstorage(appKey, tagNavList)
      }
    },
    setLocal(state, lang) {
      localSave('local', lang)
      state.local = lang
    },
    addError(state, error) {
      state.errorList.push(error)
    },
    setHasReadErrorLoggerStatus(state, status = true) {
      state.hasReadErrorPage = status
    },
    toConfirmPage(state) {
      router.replace({
        name: 'confirm'
      })
    }
  },
  actions: {
    addErrorLog({
      commit,
      rootState
    }, info) {
      if (!window.location.href.includes('error_logger_page')) commit('setHasReadErrorLoggerStatus', false)
      const {
        user: {
          token,
          userId,
          userName
        }
      } = rootState
      let data = {
        ...info,
        time: Date.parse(new Date()),
        token,
        userId,
        userName
      }
      saveErrorLogger(info).then(() => {
        commit('addError', data)
      })
    }
  }
}
