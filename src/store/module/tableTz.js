
export default {
  state: {
    dxt: {},
    fxt: {},
    jdt: {},
    RadioSelected: {},
    CheckboxSelected: {},
    SimpleSelected: {},
    // preSimRadioSelect: {},
    // preSimCheckboxSelect: {},
    // preSimSimpleSelect: {}
  },
  mutations: {
    setDxt(state, payload) {        
        state.dxt = payload
    },
    setFxt(state, payload) {
        state.fxt = payload
    },
    setJdt(state, payload) {
        state.jdt = payload
    },
    setRadioSelected(state, payload) {
        state.RadioSelected = payload
    },
    setCheckboxSelected(state, payload) {
        state.CheckboxSelected = payload
    },
    setSimpleSelected(state, payload) {
        state.SimpleSelected = payload
    },
    // setPreSimRadioSelect(state, payload) {
    //   state.preSimRadioSelect = payload
    // },
    // setPreSimCheckboxSelect(state, payload) {
    //   state.preSimCheckboxSelect = payload
    // },
    // setPreSimSimpleSelect(state, payload) {
    //   state.preSimSimpleSelect = payload
    // }
  },
  getters: {
  },
  actions: {
    // 下载请求方式，需传入返回类
  }
}
