<?xml version="1.0" encoding="UTF-8"?>
<svg width="68px" height="68px" viewBox="0 0 68 68" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FE8F8F" offset="0%"></stop>
            <stop stop-color="#FE4E4E" offset="98.535327%"></stop>
        </linearGradient>
    </defs>
    <g id="智慧医疗子系统" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="所内就医/病历模板" transform="translate(-1518.000000, -128.000000)">
            <g id="数据统计" transform="translate(296.000000, 112.000000)">
                <g id="卡片4" transform="translate(1206.000000, 0.000000)">
                    <g id="icon/病历模板/新增病历模板" transform="translate(16.000000, 16.000000)">
                        <rect id="矩形" fill="#FE8A8A" opacity="0.0824105399" x="0" y="0" width="68" height="68" rx="4"></rect>
                        <rect id="矩形" fill="url(#linearGradient-1)" x="14" y="14" width="40" height="40" rx="3"></rect>
                        <rect id="矩形" fill="#FFFFFF" x="20" y="32" width="28" height="5" rx="1"></rect>
                        <rect id="矩形" fill="#FFFFFF" x="32" y="20" width="5" height="28" rx="1"></rect>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>