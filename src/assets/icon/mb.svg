<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <g id="智慧医疗子系统" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="所内就医/报病开嘱/远程问诊" transform="translate(-1584.000000, -435.000000)">
            <g id="短期医嘱登记" transform="translate(316.000000, 335.000000)">
                <g id="编组-11" transform="translate(32.000000, 91.000000)">
                    <g id="编组-5" transform="translate(80.000000, 0.000000)">
                        <g id="按钮/次按钮+图标" transform="translate(1140.000000, 0.000000)">
                            <g id="icon/按钮图标/历史病历" transform="translate(16.000000, 9.000000)">
                                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                                <path d="M5.615,15.385 L13.307,15.385 C13.732,15.385 14.077,15.729 14.077,16.154 C14.077,16.578 13.732,16.923 13.307,16.923 L5.615,16.923 C5.19,16.923 4.845,16.578 4.845,16.154 C4.845,15.729 5.19,15.385 5.615,15.385 L5.615,15.385 Z M5.615,12.308 L10.23,12.308 C10.655,12.308 11,12.653 11,13.077 C11,13.502 10.655,13.846 10.23,13.846 L5.615,13.846 C5.19,13.846 4.845,13.502 4.845,13.077 C4.845,12.653 5.19,12.308 5.615,12.308 L5.615,12.308 Z M6.385,7.693 L6.385,6.923 C6.385,6.498 6.728,6.154 7.153,6.154 C7.578,6.154 7.923,6.498 7.923,6.923 L7.923,7.693 L8.692,7.693 C9.117,7.693 9.462,8.036 9.462,8.461 C9.462,8.886 9.117,9.231 8.692,9.231 L7.923,9.231 L7.923,10 C7.923,10.425 7.578,10.769 7.153,10.769 C6.728,10.769 6.385,10.425 6.385,10 L6.385,9.231 L5.615,9.231 C5.19,9.231 4.845,8.886 4.845,8.461 C4.845,8.036 5.19,7.693 5.615,7.693 L6.385,7.693 Z" id="形状结合" fill="#00AFB8"></path>
                                <g id="编组-2" transform="translate(2.000000, 1.000000)" stroke="#00AFB8" stroke-width="1.5">
                                    <polyline id="路径" points="11.7945326 1 16 1 16 18 0 18 0 1 4.00502351 1"></polyline>
                                    <rect id="矩形" x="4" y="0" width="8" height="3"></rect>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>