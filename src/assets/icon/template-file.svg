<?xml version="1.0" encoding="UTF-8"?>
<svg width="69px" height="68px" viewBox="0 0 69 68" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#09E2E8" offset="0%"></stop>
            <stop stop-color="#00AFB8" offset="99.9259042%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="99.0131938%" id="linearGradient-2">
            <stop stop-color="#BDFDFF" offset="0%"></stop>
            <stop stop-color="#B9FBFF" stop-opacity="0.0905949519" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="智慧医疗子系统" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="所内就医/病历模板" transform="translate(-312.000000, -128.000000)">
            <g id="数据统计" transform="translate(296.000000, 112.000000)">
                <g id="icon/统计/待就诊人数" transform="translate(16.323816, 16.000000)">
                    <rect id="矩形" fill="#04C8CD" opacity="0.0824105399" x="0" y="0" width="68" height="68" rx="4"></rect>
                    <g id="icon" transform="translate(15.000000, 13.000000)">
                        <path d="M6,3 L6,6 C6,7.0543618 6.81587779,7.91816512 7.85073766,7.99451426 L8,8 L31,8 C32.0543618,8 32.9181651,7.18412221 32.9945143,6.14926234 L33,6 L33,3 L37,3 C38.1045695,3 39,3.8954305 39,5 L39,41 C39,42.1045695 38.1045695,43 37,43 L2,43 C0.8954305,43 1.3527075e-16,42.1045695 0,41 L0,5 C-1.3527075e-16,3.8954305 0.8954305,3 2,3 L6,3 Z M31,0 L31,6 L8,6 L8,0 L31,0 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        <path d="M13,12 L13,16 L17,16 L17,19 L13,19 L13,23 L10,23 L10,19 L6,19 L6,16 L10,16 L10,12 L13,12 Z" id="形状结合" fill="#CCFEFF"></path>
                        <rect id="矩形" fill="#CCFEFF" x="6" y="25" width="29" height="3"></rect>
                        <rect id="矩形" fill="#CCFEFF" x="6" y="31" width="29" height="3"></rect>
                    </g>
                    <g id="状态" transform="translate(35.000000, 34.000000)">
                        <circle id="椭圆形" stroke="url(#linearGradient-2)" fill-opacity="0.4" fill="#A5E9EB" cx="12.3737118" cy="12.3737118" r="11.8737118"></circle>
                        <path d="M6,7 L8,7 L8,9 L6,9 L6,7 Z M10,7 L18,7 L18,9 L10,9 L10,7 Z M6,11 L8,11 L8,13 L6,13 L6,11 Z M10,11 L18,11 L18,13 L10,13 L10,11 Z M6,15 L8,15 L8,17 L6,17 L6,15 Z M10,15 L18,15 L18,17 L10,17 L10,15 Z" id="形状结合" fill="#FFFFFF"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>