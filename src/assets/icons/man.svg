<svg id="男监室" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="50" height="50" viewBox="0 0 50 50">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#76c3ff"/>
      <stop offset="1" stop-color="#409dfb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.502"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0.702"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.8"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
  </defs>
  <g id="编组_2" data-name="编组 2">
    <g id="编组_11" data-name="编组 11">
      <rect id="矩形备份_3" data-name="矩形备份 3" width="50" height="50" rx="4" fill="url(#linear-gradient)"/>
    </g>
    <g id="编组" transform="translate(8.591 10.802)">
      <path id="形状结合" d="M27.351,28.351h-.576a11.134,11.134,0,0,0,1.576-1.734v.734A1,1,0,0,1,27.351,28.351Zm-15.272,0H1a1,1,0,0,1-1-1V24.8a1,1,0,0,1,1-1H2.363V1a1,1,0,0,1,1-1H24.989a1,1,0,0,1,1,1V5.457H23.06V3.93H20.428V5.457H18.014V3.93H15.383V9.8a11.035,11.035,0,0,0-2.415,1.319V3.93H10.336V13.8a11.057,11.057,0,0,0,1.741,14.549ZM5.291,3.93V25H7.923V3.93Z" fill="url(#linear-gradient-2)"/>
      <path id="形状结合-2" data-name="形状结合" d="M0,12.4a8.3,8.3,0,1,1,8.3,8.3A8.313,8.313,0,0,1,0,12.4Zm2.5,0A5.8,5.8,0,1,0,8.3,6.6,5.811,5.811,0,0,0,2.5,12.4Zm16.208-1.268a.5.5,0,0,1-.5-.5V3a.5.5,0,0,0-.5-.5H10.072a.5.5,0,0,1-.5-.5V.5a.5.5,0,0,1,.5-.5H20.209a.5.5,0,0,1,.5.5V10.636a.5.5,0,0,1-.5.5Z" transform="translate(11.123 7.688)" fill="url(#linear-gradient-3)"/>
    </g>
  </g>
</svg>
