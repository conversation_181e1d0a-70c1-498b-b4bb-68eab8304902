/*
 * usb高拍仪-依赖高新兴开发的gosuncnSocket控件
 * @Author: liangchaoping 
 * @Date: 2020-11-18 09:05:32 
 * @Last Modified by: liangchaoping
 * @Last Modified time: 2020-11-18 10:15:27
 */
import device from './../device-socket'
import { Message } from 'iview'
export default{
    //ws容器
    linkWs: null,
    //图像画布容器
    canvas: null,
    //画布内容对象
    ctx: null,
    //当前调取的摄像头
    currentCamera: 'VSKY',
    //默认分辨率
    resolution: "640x480",
    //播放流的ws
    playWs: null,
    //初始化控件
    openWs(params){
        try {
            let self = this; 
            this.linkWs = device.init(3, function (type, ret) {
                switch (type) {
                    case "onopen":
                        self.canvas = document.getElementById(params.canvasId);
                        self.ctx = self.canvas.getContext("2d");
                        self.getCamList(self.linkWs);
                        break;
                    case "onmessage":
                        self.messageCallback(ret, params);
                        break;
                    case "onclose":
                        break;
                    case "onerror":
                        Message.warning('连接usb高拍仪失败，请检查谷歌外设服务。')
                        break;
                }
            })
        } catch (e) {
            Message.warning('连接usb高拍仪服务失败!')
        }
        
    },
    //触发拍照
    evtCapture(){
        this.capture(this.linkWs, this.currentCamera);
    },
    /**
     * 消息接收
     * @param {string} ret 
     * @param {object} params 
     */
    messageCallback(ret, params) {
        var self = this;
        var result = JSON.parse(ret.data);
        if (result.code != 0) {
            self.errorHandleTip(ret.data);
            params.errorCallback && params.errorCallback(result);
            return;
        }
        switch (result.action) {
            case "open":
                self.currentCamera = self.getCam(result.data);
                self.play(params);
                break;
            case "preview":
                self.getStream(ret.data, function (type, data) {
                    switch (type) {
                        case "onopen":
                            self.playWs = data;
                            break;
                        case "onmessage":
                            var img = new Image();
                            img.src = data;
                            img.onload = function () {
                                self.ctx.drawImage(img, 0, 0, self.canvas.width, self.canvas.height);
                            };
                            break;
                        case "onerror":
                            Message.warning('打开播放流连接失败!')
                            break;
                    }
                });
                break;
            case "sizes":
                //self.getResolution(ret.data);
                break;
            case "capture":
                params.captureCallback && params.captureCallback(ret.data);
                break;
            case "close":
                self.errorHandleTip(ret.data);
                break;
        }
    },
    //开始播放
    play(params) {
        var self = this;
        self.startStream(self.linkWs, self.currentCamera, self.resolution);
        params.playCallback && params.playCallback(params)
    },
    //错误提示处理
    errorHandleTip(ret){
        var res = JSON.parse(ret); console.log(res, res.message)
        if (res && res.message && res.code != 0) {
            Message.warning(res.message)
        }
    },
    //获取比对后的硬件设备id
    getCam(data) {
        var current = '';
        data.forEach(function (item) {
            if (item.name == "VSKY") {
                current = item.id;
            }
        })
        return current;
    },
    //销毁
    destroyed() {
        if (this.linkWs && this.currentCamera) {
            this.closeStream(this.linkWs, this.playWs, this.currentCamera);
            this.linkWs = null;
            this.canvas = null;
            this.ctx = null;
        }
    },
    /**
     * 获取流（base64）
     * 
     * @param {any} streamUrl 播放流websoket地址
     * @param {any} callback 获取流回调
     */
    getStream: function (data, callback) {
        var streamUrl = JSON.parse(data).data;
        var ws = new WebSocket(streamUrl);
        ws.onopen = function (e) {
            callback && callback("onopen", ws);
        };
        ws.onmessage = function (ret) {
            var src = "data:image/png;base64," + ret.data;
            callback && callback("onmessage", src);
        };
        ws.onclose = function (e) {
            callback && callback("onclose", e);
        };
        ws.onerror = function (e) {
            callback && callback("onerror", e);
        };
    },
    /**
     * 播放流
     * 
     * @param {any} ws WebSocket对象
     * @param {any} cammera 当前要播放的usb设备名
     * @param {any} resolution 分辨率
     */
    startStream: function (ws, cammera, resolution) {
        ws.send(JSON.stringify({ action: "preview", id: cammera, resolution: resolution }));
    },
    /**
     * 获取camera列表
     * 
     * @param {any} ws WebSocket对象
     */
    getCamList: function (ws) {
        ws.send(JSON.stringify({ action: "open" }));
    },
    /**
     * 获取设备所支持的分辨率
     * 
     * @param {any} ws WebSocket对象
     * @param {any} cammera 当前要播放的usb设备名
     */
    getResolution: function (ws, cammera) {
        ws.send(JSON.stringify({ action: "sizes", id: cammera }));
    },
    /**
     * 抓拍
     * 
     * @param {any} ws WebSocket对象
     * @param {any} cammera 当前要播放的usb设备名
     */
    capture: function (ws, cammera) {
        ws.send(JSON.stringify({ action: "capture", id: cammera }));
    },
    /**
     * 停止播放流
     * 
     * @param {any} playWs 打开usb设备ws对象
     * @param {any} openWs playWs 播放流ws对象
     * @param {any} cammera 当前要播放的usb设备名
     */
    closeStream: function (openWs, playWs, cammera) {
        if (playWs) {
            playWs.close();
        }
        openWs.send(JSON.stringify({ action: "close", id: cammera }));
    }
}