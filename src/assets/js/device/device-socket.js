
export default{
    fingerUrl: "ws://127.0.0.1:8899/finger",
    pensignUrl: "ws://127.0.0.1:8899/pensign",
    mapleUrl: "ws://127.0.0.1:18888",
    webcamUrl:'ws://127.0.0.1:8899/webcam',
    infoUrl: "ws://127.0.0.1:8899/info",
    videoUrl: "ws://127.0.0.1:8899/video",
    /**
     * 初始化
     * 
     * @param {any} type 0:指纹仪 1:手写板  2:枫林高拍仪 3:usb高拍仪 4:外设服务信息 5:高新兴视频播放
     * @param {fun} callback websoket回调
     * @returns {any} WebSocket对象
     */
    init: function (type, callback) {
        var url = "", _self = this;
        switch (type) {
            case 0:
                url = _self.fingerUrl;
                break;
            case 1:
                url = _self.pensignUrl;
                break;
            case 2:
                url = _self.mapleUrl;
                break;
            case 3:
                url = _self.webcamUrl;
                break;
            case 4:
                url = _self.infoUrl;
                break;
            case 5:
                url = _self.videoUrl;
                break;
            case 7:
                url = _self.videoUrl;
                break;
        }
        var ws = null;
        try {
            ws = new WebSocket(url);
        } catch (e) {
            // console.log(e);
            return ws;
        }
        ws.onopen = function (e) {
            callback && callback("onopen", e);
        };
        ws.onmessage = function (ret) {
            callback && callback("onmessage", ret);
        };
        ws.onclose = function (e) {
            callback && callback("onclose", e);
        };
        ws.onerror = function (e) {
            callback && callback("onerror", e);
        };
        return ws;
    },
    open: function (ws) {
        ws.send("open");
    },
    send: function (ws, param) {
        ws.send(param);
    },
    read: function (ws) {
        ws.send("read");
    },
    stop: function (ws) {
        ws.send("close");
    },
    close: function (ws) {
        ws.close();
    }
}