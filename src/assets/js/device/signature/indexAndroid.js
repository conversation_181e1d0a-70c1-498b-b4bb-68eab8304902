/*
 * Android签名捺印二合一硬件对接
 * @Author: jinyang 
 * @Date: 2021-06-21 11:20:01 
 * @Last Modified by: jinyang
 * @Last Modified time: 2021-06-21 11:20:01 
 */
import { Message,Spin } from 'iview'
import { getSignAndroidUrl } from "@/libs/util";
import Cookies from 'js-cookie';
export default{
    linkWs: null,
    openWs: function (params) {
      var _that=this
      var socket;
      // var host = getSignAndroidUrl();
      var host = params.host
      var flag=false;
      socket = new WebSocket(host);
      Spin.show({
        render: h => {
          return h('div', [
            h('Icon', {
              'class': 'spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 40,
              }
            }),
            h('div', '连接签字板中...')
          ])
        }
      })
      try {
        socket.onopen = function(msg) {
            Spin.hide()
            Cookies.set('SignAndroidUrl',host)
            flag=true;
            _that.OpenSignPage(socket);
        };

        socket.onmessage = function(msg) {
          if (typeof msg.data == "string") {
              var message = JSON.parse(msg.data);
              if ('p_translate' == message['p_cmd'])
							{
                var p_msg=JSON.parse(message['p_msg'])
                if(p_msg['parameters']!=undefined && p_msg['parameters'].length>0){
                  var p_signinfo=p_msg['parameters'][0]['p_signinfo']['p_filepath'] //签名图片
                  var p_fingerinfo=p_msg['parameters'][0]['p_fingerinfo']['p_filepath']//手印图片
                  
                  if(p_signinfo!=undefined&& p_signinfo!="" &&(p_fingerinfo==undefined|| p_fingerinfo=="")){
                    //只有手写签名
                    params.callback && params.callback(p_signinfo);
                  }else  if((p_signinfo==undefined|| p_signinfo=="") &&p_fingerinfo!=undefined&& p_fingerinfo!=""){
                     //只有捺印
                    params.callback && params.callback(p_fingerinfo);
                  }else{
                    //两张图片合成一张展示
                    _that.drawProdPicture(p_signinfo,p_fingerinfo,params)
                  }
                }
							}
          }
        };
        socket.onerror = function(error){
            Spin.hide()
            Message.warning('连接签名板失败，请检查谷歌外设服务。')
            return
        };
        
      } catch (ex) {
        Spin.hide()
        Message.warning('连接签名板服务失败!')
        return
      }

    },
    OpenSignPage(socket)
		{
      var  p_msg={
        "cmdtype":	"request",
        "cmd":	"p_sign",
        "parameters":	{
          "p_signtype":	2,
          "p_tips":	"请进行签名和捺印",
          "p_srcfile":	"",
          "p_destfile":	"",
          "p_multisignlocation":	"",
          "p_sm4key":	"",
          "p_sm4iv":	"",
          "p_pdfmd5":	""
        }
    };
      var params={
        "p_version": "1.0.0",
        "p_type": "request",
        "p_cmd": "p_translate",
        "p_sn": 27,			//会话ID第四点中会话ID对应
        "p_msg": JSON.stringify(p_msg)
      };
      
      socket.send(JSON.stringify(params));
		},


    drawProdPicture (p_signinfo,p_fingerinfo,params) {
      let img1 = new Image()
      img1.src = 'data:image/png;base64,'+p_fingerinfo
      img1.width = 350
      img1.height = 350
      img1.setAttribute('crossOrigin', 'anonymous')
      let canvas = document.createElement("canvas")
      let context = canvas.getContext("2d")
      canvas.width = 350
      canvas.height = 350
      let img2 = new Image()
      let flag = true
      var makePic='';
      // 将 img1 加入画布
      img1.onload = () => {
        context.drawImage(img1, 50, 50, 200, 200)
        img2.src = 'data:image/png;base64,'+ p_signinfo
        img2.setAttribute('crossOrigin', 'anonymous')
        img2.width = 350
        img2.height = 350
        if (flag) {
          flag = false
        } else {
          let src = canvas.toDataURL()
          makePic = src
        }
      }
      // 将 img2 加入画布
      img2.onload = () => {
        context.drawImage(img2, 0, 0, 350, 350)
        if (flag) {
          flag = false
        } else {
          let src = canvas.toDataURL('image/png')
          makePic = src
          makePic=makePic.split(',')[1];
          params.callback && params.callback(makePic);
          return makePic;
        }
      }
      
    }

    
}