/*
 * 签名捺印二合一硬件对接
 * @Author: lian<PERSON><PERSON><PERSON> 
 * @Date: 2020-11-18 11:20:01 
 * @Last Modified by: liangchaoping
 * @Last Modified time: 2020-11-18 11:24:28
 */
import device from './../device-socket'
import { Message,Spin } from 'iview'
export default{
    linkWs: null,
    openWs: function (params) {
        var self = this;
        Spin.show({
            render: h => {
              return h('div', [
                h('Icon', {
                  'class': 'spin-icon-load',
                  props: {
                    type: 'ios-loading',
                    size: 40,
                  }
                }),
                h('div', '连接签字板中...')
              ])
            }
          })
        try {
            self.linkWs = device.init(1, function (type, ret) {
                switch (type) {
                    case "onopen":
                        device.open(self.linkWs);
                        Spin.hide()
                        break;
                    case "onmessage":
                        self.signCallback(ret, params);
                        break;
                    case "onerror":
                        Spin.hide()
                        Message.warning('连接签名板失败，请检查谷歌外设服务。')
                        break;
                }
            });
        } catch (e) {
            Spin.hide()
            Message.warning('连接签名板服务失败!')
        }
    },
    signCallback: function (ret, params) {
        var result = JSON.parse(ret.data);
        if (result.code != 0) {
            Message.warning(result.message)
            return;
        }
        if (result.data) {
            params.callback && params.callback(result.data);
        }
    },
    destroy: function () {
        var self = this;
        if (self.linkWs) {
            self.linkWs.close();
        }
    }
}