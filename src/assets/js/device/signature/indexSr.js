/*
 * Android签名捺印二合一硬件对接
 * @Author: liangchaoping 
 * @Date: 2020-11-18 11:20:01 
 * @Last Modified by: liangchaoping
 * @Last Modified time: 2020-11-18 11:24:28
 */
import { Message,Spin } from 'iview'
export default{
    linkWs: null,
    openWs: function (params) {
      var _that=this
      var socket;
      var host = "ws://127.0.0.1:37281";
      var flag=false;
      socket = new WebSocket(host);
      Spin.show({
        render: h => {
          return h('div', [
            h('Icon', {
              'class': 'spin-icon-load',
              props: {
                type: 'ios-loading',
                size: 40,
              }
            }),
            h('div', '连接签字板中...')
          ])
        }
      })
      var p_signinfo="";
      var p_fingerinfo="";
      try {
        socket.onopen = function(msg) {
            Spin.hide()
            flag=true;
            //打开签名
            _that.OpenSignPage(socket);
           
        };

        socket.onmessage = function(msg) {
          if (typeof msg.data == "string") {
            var message = JSON.parse(msg.data);
            if ("close_signpage_confirm" == message['func'])
						{
              //关闭签名
              _that.CloseSignPage(socket);
               //打开捺印
              _that.OpenFpCollector(socket);
              p_signinfo=message['info']['image_base64'];
              
						}
            
            if ("auto_read_fingerprint" == message['func'])
						{
              //关闭捺印
              _that.CloseFpCollector(socket);
              setTimeout( function() {
                _that.disconnect(socket);
              }, 300);

              // _that.disconnect(socket);

              p_fingerinfo=message['info']['image_base64'];
              
              if(p_signinfo!=undefined&& p_signinfo!="" &&(p_fingerinfo==undefined|| p_fingerinfo=="")){
                //只有手写签名
                params.callback && params.callback(p_signinfo);
              }else  if((p_signinfo==undefined|| p_signinfo=="") &&p_fingerinfo!=undefined&& p_fingerinfo!=""){
                 //只有捺印
                params.callback && params.callback(p_fingerinfo);
              }else{
                //两张图片合成一张展示
                _that.drawProdPicture(p_signinfo,p_fingerinfo,params)
              }

						}

          }
        };
        socket.onerror = function(error){
            Spin.hide()
            Message.warning('连接签名板失败，请检查谷歌外设服务。')
            return
        };
        
      } catch (ex) {
        Spin.hide()
        Message.warning('连接签名板服务失败!')
        return
      }

    },
    //打开签名
    OpenSignPage(socket)
		{
      socket.send(JSON.stringify({
				'func':'open_signpage',
				'param':{'get_process':'1', 'prompt_text':'请您签字', 'prompt_sound_file':'1.aac'}
			}));

		},
    //关闭签名
    CloseSignPage(socket){
      socket.send(JSON.stringify({
				'func':'close_signpage'
			}));
    },

     //打开捺印
     OpenFpCollector(socket){
      socket.send(JSON.stringify({
				'func':'open_fpcollector',
				'param':{'auto_read':'1', 'prompt_text':'请按指纹', 'prompt_sound_file':'1.aac'}
			}));
    },

    //关闭捺印
    CloseFpCollector(socket){
      socket.send(JSON.stringify({
				'func':'close_fpcollector'
			}));
    },

    disconnect(socket){
      socket.close();
      socket = null;
    },

    drawProdPicture (p_signinfo,p_fingerinfo,params) {
      let img1 = new Image()
      img1.src = 'data:image/png;base64,'+p_fingerinfo
      img1.width = 350
      img1.height = 350
      img1.setAttribute('crossOrigin', 'anonymous')
      let canvas = document.createElement("canvas")
      let context = canvas.getContext("2d")
      canvas.width = 350
      canvas.height = 350
      let img2 = new Image()
      let flag = true
      var makePic='';
      // 将 img1 加入画布
      img1.onload = () => {
        context.drawImage(img1, 50, 50, 200, 200)
        img2.src = 'data:image/png;base64,'+ p_signinfo
        img2.setAttribute('crossOrigin', 'anonymous')
        img2.width = 350
        img2.height = 350
        if (flag) {
          flag = false
        } else {
          let src = canvas.toDataURL()
          makePic = src
        }
      }
      // 将 img2 加入画布
      img2.onload = () => {
        context.drawImage(img2, 0, 0, 350, 350)
        if (flag) {
          flag = false
        } else {
          let src = canvas.toDataURL('image/png')
          makePic = src
          makePic=makePic.split(',')[1];
          params.callback && params.callback(makePic);
          return makePic;
        }
      }
      
    }

    
}