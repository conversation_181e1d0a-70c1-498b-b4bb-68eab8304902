/*
 * @Author: liangchaoping 
 * @Date: 2021-01-20 11:03:01 
 * @Last Modified by: liangchaoping
 * @Last Modified time: 2021-01-20 17:57:32
 */
// import Vue from 'vue'
// import device from './../device-socket'
// import store from '@/store'

import { getCenter } from '@/libs/util'
import Vue from 'vue'
import { Message } from 'iview'
import device from './../device-socket'
import store from '@/store'
import API from '../../../../api/index'
import { $Post, $Get,$Post_RP } from '../../../../api/fetch'
export default{
    
    /**
     * 获取BMS loginUrl（promise）
     */
    getBmsUrl: function () {
        return new Promise((resolve, reject) => {
            store.dispatch('postRequest', {
                url: Vue.path.get_bms_login_params,
                params: {
                    pageSize: 10,
                    pageNo: 1,
                    params: JSON.stringify({ 
                        "name": "", 
                        "mark": "BMS_LOGIN", 
                        "orderCondi": { "add_time": "desc" } 
                    })
                }
            }).then(ret=>{
                if (ret.success) {
                    resolve(ret.rows||[])
                } else {
                    reject(ret)
                }
            },
            error=>{
                reject(error)
            })
        })
    },
    /**
     * 获取本地播放器状态（promise 0：关闭 1：开启）
     */
    getLocalPlayerState: function () {
        return new Promise((resolve, reject) => {
            var ws = device.init(3, function (type, ret) {
                switch (type) {
                    case "onopen":
                        device.send(ws, JSON.stringify({
                            action: "localPlayer"
                        }));
                        break;
                    case "onmessage":
                        var result = JSON.parse(ret.data);
                        if (result.action == "localPlayer") {
                            if (result.code == 0) {
                                if (result.data && result.data == 1) {
                                    resolve(1);
                                } else {
                                    resolve(0);
                                }
                            } else {
                                reject(result.message);
                            }
                            ws.close();
                        }
                        break;
                    case "onclose": break;
                    case "onerror":
                        alert("连接视频服务失败,请下载安装谷歌外设服务！");
                        reject();
                        break;
                }
            });
        })
    },
    /** 
     * 根据IpcMagicId获取通道信息（ID和名称）
    */
    getSzNodeInfoByIpcMagicId: function (ipcMagicId) {
        return new Promise((resolve, reject) => {

        })
        /* var deferred = $.Deferred();
        Models.Ipc.one("info/" + ipcMagicId).get().then(function (ret) {
            if (commonUtil.checkCode(ret.state) && ret.data) {
                deferred.resolve(ret.data);
            } else {
                deferred.reject("获取通道ID失败！");
            }
        }, function () {
            deferred.reject("获取通道ID服务出错！");
        });
        return deferred.promise(); */
    },
    openPlay: function (params) {
        let self = this;
        let ws = device.init(5, function (type, ret) {
            switch (type) {
                case "onopen":
                    self.loginBms(ws,params)
                    break;
                case "onmessage":
                    self.wsMessage(ws, params,ret);
                    break;
                case "onerror":
                    alert("连接视频服务失败,请下载安装谷歌外设服务！")
                    break;
            }
        });
        return ws;
    },
    openPlayBy7: function (params) {
        let self = this;
        let ws = device.init(7, function (type, ret) {
            switch (type) {
                case "onopen":
                    self.loginBms(ws,params)
                    break;
                case "onmessage":
                    self.wsMessage7(ws, params,ret);
                    break;
                case "onerror":
                    alert("连接视频服务失败,请下载安装谷歌外设服务！")
                    break;
            }
        });
        return ws;
    },
    loginBms(ws,params){
        let bmsLog = {
            sIP: '',
            nPort: '',
            sUserName: "",
            sPassword: "",
            action: "login"
        }
        bmsLog.sPassword = params.data.sPassword;
        bmsLog.sUserName = params.data.sUserName;
        bmsLog.nPort = params.data.nPort;
        bmsLog.sIP = params.data.sIP;
        ws.send(JSON.stringify(bmsLog));
    },
    sendRtmp: function (ws, option) {
        var defaultOpt = {
            action: "stream",
            szNodeID: "",
            CameraName: '',
            nVideoReqType: 101,
            nStreamType: 2,
            deviceConnectType: 0,
            streamAgentType: 5,
            LocalPlayer: false,
            Display: false,
            nIndex: -1
        };
        var theOpt = Object.assign(defaultOpt, option);
        ws.send(JSON.stringify(theOpt));
    },
    sendRtmp7: function (ws, option) {
        console.log('sendRtmp7')
        var defaultOpt = {
            action: "stream",
            szNodeID: "",
            CameraName: '',
            nVideoReqType: 101,
            nStreamType: 2,
            deviceConnectType: 0,
            streamAgentType: 7,
            LocalPlayer: false,
            Display: false,
            nIndex: -1
        };
        var theOpt = Object.assign(defaultOpt, option);
        ws.send(JSON.stringify(theOpt));
    },
    /**
     * 调启gosuncnSocket回调
     * @param {object} ws webscoket对象
     * @param {object} params 视频参数对象
     * @param {string} params.szNodeID 摄像头id
     * @param {string} params.cameraName 摄像头名称
     * @param {function} params.callbackVideo 播放器初始化入口
     * @param {object} ret websocket消息返回对象
     */
    wsMessage: function (ws, params, ret) {
        var result = JSON.parse(ret.data);
        // console.log(result);
        if (result.code != 0) {
            alert(result.message);
            return;
        }
        if (result.action == "login") {
            //"52_#_51000010_1_101"
            this.sendRtmp(ws, params.data);
        }
        if (result.action == "stream"|| result.action == "record") {
            params.callbackVideo && params.callbackVideo(result.data)
        }
    },
    wsMessage7: function (ws, params, ret) {
        var result = JSON.parse(ret.data);
        if (result.code != 0) {
            alert(result.message);
            return;
        }
        if (result.action == "login") {
            //"52_#_51000010_1_101"
            this.sendRtmp7(ws, params.data);
        }
        if (result.action == "stream"|| result.action == "record") {
            params.callbackVideo && params.callbackVideo(result.data)
        }
    },
    
}
