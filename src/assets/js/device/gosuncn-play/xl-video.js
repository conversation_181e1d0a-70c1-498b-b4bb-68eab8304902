/*
 * @Author: liangchaoping 
 * @Date: 2021-01-20 15:22:50 
 * @Last Modified by: liangchaoping
 * @Last Modified time: 2021-01-20 19:01:22
 */
import flvjs from 'flv.js/dist/flv.min'
export function initFlvPlayer (videoEl, url, type, fullscreenContainer) {
    //flv.js 
    if (flvjs.isSupported() && url) {
        var options = {
            type: 'flv',
            isLive: true,
            url: url,
            controls: type == 1 ? false : true,
            autoplay: true,
            poster: ""
        }
        var videoElement = document.getElementById(videoEl);
        videoElement.controls = options.controls;
        videoElement.setAttribute('poster', options.poster);
        if (fullscreenContainer) {
            var fullarea = document.getElementById(fullscreenContainer);
        }
        var flvPlayer = flvjs.createPlayer(options);
        flvPlayer.attachMediaElement(videoElement);
        flvPlayer.load();
        if (options.autoplay) {
            flvPlayer.play();
        }
        flvPlayer.requestFullscreen = function () {
            if (!options.controls) {
                // videoElement.requestFullscreen();
                if (fullarea.requestFullscreen) {
                    fullarea.requestFullscreen();
                } else if (fullarea.webkitRequestFullScreen) {
                    fullarea.webkitRequestFullScreen();
                } else if (fullarea.mozRequestFullScreen) {
                    fullarea.mozRequestFullScreen();
                } else if (fullarea.msRequestFullscreen) {
                    // IE11
                    fullarea.msRequestFullscreen();
                }
            }
        };
        flvPlayer.exitFullscreen = function () {
            // videoElement.exitFullscreen();
            if (!options.controls) {
                if (document.fullscreenElement && document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitFullscreenElement && document.webkitCancelFullScreen) {
                    document.webkitCancelFullScreen();
                } else if (document.mozFullScreenElement && document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        };
        flvPlayer.dispose = function () {
            if (flvPlayer) {
                flvPlayer.pause();
                flvPlayer.unload();
                flvPlayer.detachMediaElement();
                flvPlayer.destroy();
                if (flvPlayer._timer) {
                    clearInterval(flvPlayer._timer);
                }
                flvPlayer = null;
            }
        };
        return flvPlayer;
    }
}