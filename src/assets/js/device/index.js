
import maple from './maple-high/index.js'
import webcam from './usb-high/index.js'
import sign from './signature/index.js'

//枫林高拍仪
let mapleHigh = {
    open(params) {
        maple.openWs(params)
    },
    capture(fn) {
        maple.capture(fn)
    },
    close() {
        maple.destroy();
    }
}
//usb高拍仪
let usbHigh = {
    open(params) {
        webcam.openWs(params)
    },
    capture() {
        webcam.evtCapture()
    },
    close() {
        webcam.destroyed();
    }
}
let signature = {
    /**
     * 签名版
     * 
     * @param {Object} params 参数对象
     * @param {Function} params.callback 回调
     */
    open(params) {
        sign.openWs(params);
    },
}

export default{
    mapleHigh,
    usbHigh,
    signature
}