import Router from 'vue-router';
export default {
    replaceQuotation(str) {
        String.prototype.replaceCharAt = function(n, c) {
                return this.substr(0, n) + c + this.substr(n + 1, this.length - 1 - n);
            }
            // 替换单引号(英文单引号转为中文单引号)
        var Indexs = []
        var count = 0

        for (let i = 0; i < str.length; i++) {

            if (str[i] == "'") {
                count++
                Indexs.push({ i, count })
            }
        }
        for (let i = 0; i < str.length; i++) {
            Indexs.forEach(item => {
                if (item.i == i) {
                    if (item.count % 2 == 0) {
                        str = str.replaceCharAt(i, "’")
                    } else {
                        str = str.replaceCharAt(i, '‘')
                    }
                }
            })
        }

        //替换双引号(英文双引号转为中文双引号)
        var Indexs = []
        var count = 0

        for (let i = 0; i < str.length; i++) {

            if (str[i] == '"') {
                count++
                Indexs.push({ i, count })
            }
        }
        for (let i = 0; i < str.length; i++) {
            Indexs.forEach(item => {
                if (item.i == i) {
                    if (item.count % 2 == 0) {
                        str = str.replaceCharAt(i, "”")
                    } else {
                        str = str.replaceCharAt(i, '“')
                    }
                }
            })
        }
        // "\s"匹配任何不可见字符，包括空格、制表符、换页符等等。等价于[ \f\n\r\t\v]，/g全局替换
        str = str.replace(/\s+/g, '')
        return str
    },
    dateFormat(time) {
        var date = time !== undefined ? new Date(time) : new Date();
        var year = date.getFullYear()
            /* 在日期格式中，月份是从0开始的，因此要加0
                              使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05 */
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
            // 拼接
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    },
    dateAddDaysFormat(time, value) {
        var curDate = new Date(time)
        var date = new Date(curDate.getTime() + value * 24 * 60 * 60 * 1000);
        return this.dateFormat(date);
        // var Y = date.getFullYear() + '-';
        // var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
        // var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
        // var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        // var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        // var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        // return Y + M + D + h + m + s;
    },
    dateSubDaysFormat(time, value) {
        var curDate = new Date(time)
        var date = new Date(curDate.getTime() - value * 24 * 60 * 60 * 1000);
        return this.dateFormat(date);
        // var Y = date.getFullYear() + '-';
        // var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
        // var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
        // var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        // var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
        // var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
        // return Y + M + D + h + m + s;
    },
    endDateFormat(time) {
        var date = time !== undefined ? new Date(time) : new Date();
        var year = date.getFullYear()
            /* 在日期格式中，月份是从0开始的，因此要加0
                              使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05 */
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        var hours = 23
        var minutes = 59
        var seconds = 59
            // 拼接
        return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
    },
    dateFormatDay(time) {
        var date = time !== undefined ? new Date(time) : new Date();
        var year = date.getFullYear()
            /* 在日期格式中，月份是从0开始的，因此要加0
                              使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05 */
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
            // 拼接
        return year + '-' + month + '-' + day
    },
    dateFormatToChar(time) {
        var date = time !== undefined ? new Date(time) : new Date();
        var year = date.getFullYear() + ''
            /* 在日期格式中，月份是从0开始的，因此要加0
                              使用三元表达式在小于10的前面加0，以达到格式统一  如 09:11:05 */
        var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1 + ''
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate() + ''
        var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours() + ''
        var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes() + ''
        var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds() + ''
            // 拼接
        return year + month + day + hours + minutes + seconds
    },
    getbefore7Days() {
        let dateList = []
        for (var i = 6; i >= 0; i--) {
            let date = new Date(Date.now() - 86400000 * i)
            let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
            let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
            dateList.push(month + "-" + day)
        }
        return dateList
    },
    // theadName:表格标题，theadKey:标题对应的key值，itemWidth:表格每列的宽度，
    // fixedArr:需要固定的列的配置项，slotArr:需要在页面上添加新的标签元素配置项 sortArr:需要进行升降序排序
    renderThead(theadName, theadKey, itemWidth, fixedArr, slotArr, sortArr) {
        fixedArr = fixedArr || [];
        slotArr = slotArr || [];
        sortArr = sortArr || [];
        let arr = [];
        theadName.forEach((item, index) => {
            if (item == 'selection') {
                var obj = {
                    type: item,
                    minWidth: itemWidth[index],
                    align: 'center',
                }
            } else if (theadKey[index] == 'xh') {
                var obj = {
                    title: item,
                    type: 'index',
                    minWidth: itemWidth[index],
                    align: 'center',
                }
            } else if (item == '操作') {
                var obj = {
                    title: item,
                    minWidth: itemWidth[index],
                    slot: theadKey[index],
                    align: 'center'
                }
            } else if (theadKey[index] == 'Radio') {
                var obj = {
                    title: item,
                    minWidth: itemWidth[index],
                    slot: theadKey[index],
                    align: 'center',
                }
            } else {
                var obj = {
                    title: item,
                    ellipsis: true,
                    tooltip: true,
                    key: theadKey[index],
                    minWidth: itemWidth[index],
                    align: 'center'
                }
            }
            if (fixedArr.length > 0) {
                fixedArr.forEach(item2 => {
                    if (item2.key == theadKey[index]) {
                        obj.fixed = item2.value
                    }
                })

            }
            if (slotArr.length > 0) {
                slotArr.forEach(item2 => {
                    if (item2 == theadKey[index]) {
                        obj.slot = item2;
                        obj.tooltip = true;
                    }
                })
            }
            if (sortArr.length > 0) {
                sortArr.forEach(item2 => {
                    if (item2 == theadKey[index]) {
                        obj.sortable = 'custom'
                    }
                })
            }
            arr.push(obj)
        });
        theadName = [];
        return arr;
    },
    // 无限轮播
    UnlimitedCarousel(elem) {
        function animateFun() {　　
            var firstDom = $(elem).children(':first');
            var h = $(firstDom).height();
            $(elem).animate({ 'margin-top': -h }, 3000, 'linear', function callback() {
                $(firstDom).remove();
                $(this).css({ 'margin-top': 0 })
                $(this).append($(firstDom));
                animateFun();
            });
        }
        animateFun();
        $(elem).mouseenter(function() {
            $(this).stop();
        })
        $(elem).mouseleave(function() {
            animateFun();
        })
    },

    toTree(data) {
        var ch = [];
        if (data && data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                var item = data[i];
                if (!item.parentId) {
                    data.splice(i, 1);
                    var children = this.toChildrenTree(item.id, data);
                    if (children) {
                        item.children = children;
                    }
                    i = -1;
                    ch.push(item);
                }
            }
        }
        ch = ch.sort(this.compare('orderId'));
        return ch;
    },
    toChildrenTree(parentId, data) {
        var ch = [];
        if (data.length > 0) {
            for (let i = 0; i < data.length; i++) {
                var item = data[i];
                if (item.parentId && item.parentId == parentId) {
                    data.splice(i, 1);
                    var children = this.toChildrenTree(item.id, data);
                    if (children) {
                        children = children.sort(this.compare('orderId'));
                        item.children = children;
                    }
                    i = -1;
                    ch.push(item);
                }
            }
        }
        ch = ch.sort(this.compare('orderId'));
        return ch;
    },
    compare(arg) {
        return function(a, b) {
            return a[arg] - b[arg];
        }
    },
    getChildrenMenu(parentId, data) {
        var arr = [];
        if (data.length > 0) {
            data.forEach(item => {
                if (item.id == parentId) {
                    arr = item.children;
                    return arr;
                } else if (item.children) {
                    var ch = this.getChildrenMenu(parentId, item.children);
                    if (ch.length > 0) {
                        arr = ch;
                        return arr;
                    }
                }
            })
        }
        return arr;
    },

    //获取当前时间指定前后时间的日期
    getAppointDay(days) {
        var now = new Date();
        now.setDate(now.getDate() + days);
        return now.getFullYear() + "-" + this.doHandleMonth(now.getMonth() + 1) + "-" + this.doHandleDay(now.getDate());
    },

    doHandleMonth(month) {
        if (month.toString().length <= 1) {
            month = "0" + month;
        }
        return month;
    },
    doHandleDay(day) {
        if (day.toString().length <= 1) {
            day = "0" + day;
        }
        return day;
    },


    FullScreen(domId) { // 全屏
        var el = document.querySelector(domId);
        var isFullscreen = document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen;
        if (!isFullscreen) { //进入全屏,多重短路表达式
            (el.requestFullscreen && el.requestFullscreen()) ||
            (el.mozRequestFullScreen && el.mozRequestFullScreen()) ||
            (el.webkitRequestFullscreen && el.webkitRequestFullscreen()) ||
            (el.msRequestFullscreen && el.msRequestFullscreen());

        } else { //退出全屏,三目运算符
            document.exitFullscreen ? document.exitFullscreen() :
                document.mozCancelFullScreen ? document.mozCancelFullScreen() :
                document.webkitExitFullscreen ? document.webkitExitFullscreen() : '';
            document.querySelector('.case_rt_znyj').style.height = 'calc(100% - 0.59rem)';
        }
    },
    updateCaseCenterData(authObj, center) {
        authObj.centerName = center.name;
        authObj.centerId = center.id;
        authObj.appName = center.appName;
        authObj.copyright = center.copyright;

        authObj.groupId = center.groupId;
        authObj.orgBh = center.orgBh;
        authObj.enableGosuncnDevice = center.enableGosuncnDevice;
        authObj.cameraType = center.cameraType;
        authObj.scanLayer = center.scanLayer;

        localStorage.setItem('coreData', JSON.stringify(authObj));
    },
    // 图片转成base64
    // 调用方法
    // this.getBase64Image(url, function(base64Img){  
    //       });
    getBase64Image(url, callback) {
        var canvas = document.createElement('canvas'),
            ctx = canvas.getContext('2d'),
            img = new Image();
        //为了解决跨域，可以直接img.crossOrigin=''就能解决图片跨域问题
        img.crossOrigin = 'xes';
        img.onload = function() {
            canvas.height = img.height;
            canvas.width = img.width;
            ctx.drawImage(img, 0, 0);
            var dataURL = canvas.toDataURL('image/png');
            callback.call(this, dataURL);
            canvas = null;
        }
        img.src = url;
    },
    treeSearchEvent(nodes, searchText) { // 目录树搜索功能
        var searchArr = [];
        nodes.forEach(item => {
            if (item.name.indexOf(searchText) !== -1) {
                searchArr.push(item);
            }
        })

        deep(searchArr);

        function deep(searchArr) {
            nodes.forEach(item => {
                searchArr.forEach(item2 => {
                    if (!item2.isParent) {
                        if (item.id == item2.id) {
                            item2.isSea = true;
                            var parentNode = item2.getParentNode();
                            if (parentNode) {
                                deep([parentNode]);
                            }
                        }
                    } else {
                        if (item.id == item2.id) {
                            item.isSea = true;
                            var parentNode = item2.getParentNode();
                            if (parentNode) {
                                deep([parentNode]);
                            }
                            deep2(item);

                            function deep2(item2) {
                                item2.children.forEach(item3 => {
                                    if (item3.name.indexOf(searchText) !== -1) {
                                        if (!item3.isParent) {
                                            item3.isSea = true;
                                        } else {
                                            item3.isSea = true;
                                            deep2(item3)
                                        }

                                    } else {
                                        var flag = false;
                                        item2.children.forEach(item4 => {
                                            if (item4.isSea) {
                                                flag = true;
                                            }
                                        })
                                        if (!flag) {

                                            item2.children.forEach(item4 => {
                                                if (!item4.isParent) {
                                                    item4.isSea = true;
                                                } else {
                                                    item4.isSea = true;
                                                    deep2(item4)
                                                }
                                            })

                                        }
                                    }

                                })
                            }
                        }
                    }
                })
            })
        }
        return nodes;
    },
    getUploadFileNmae(url) {
        if (url) {
            let arr = url.split('/');
            let fileName = arr[arr.length - 1];
            fileName = fileName.replace(/\s*/g, "");
            return fileName;
        }
    },
    MouseCheckEvent(checkedArr) { // 鼠标复选方法
        checkedArr.forEach(item => {
            console.log(item)
        })
    },
    removeHead() {
        document.querySelector('.znaj_home_head').remove()
    },
    splitVueTemplate(sourceCode, type) {
        let sIndex = sourceCode.indexOf('<' + type + '>') + type.length + 2,
            eIndex = sourceCode.indexOf('</' + type + '>');
        return sourceCode.substring(sIndex, eIndex)
    },
    stringMatchEvent(stringArr_1, stringArr_2, matchNum) {
        let resultArr = [];
        stringArr_1.forEach(item => {
            stringArr_2.forEach(item2 => {
                let num = 0
                for (var i = 0; i < item2.name.length; i++) {
                    if (item.name.indexOf(item2.name[i]) != -1) {
                        num++
                    }
                }
                if ((num / item.name.length * 100) >= matchNum) {
                    let obj = {
                        Obj1: item,
                        obj2: item2
                    }
                    resultArr.push(obj)
                }
            })
        })
        return resultArr;
    },
    setNodesToZtreeNodes(nodes) {
        let ztreeNodesArr = []
        nodes.forEach(item => {
            nodes.forEach(item2 => {
                if (item.dm == item2.fjdm) {
                    item2.parentId = item.id
                }
            })
        })
        nodes.forEach(item => {
            let obj = {
                id: item.id,
                name: item.mc,
                document: "[" + item.mc + "]",
                orderId: item.orderId,
            }
            if (!item.fjdm) {
                obj.parentId = "0"
                obj.isParent = true
                obj.level = '0'
                obj.type = '1'
            } else if (item.fjdm && !item.sfxtws) {
                obj.isParent = true
                obj.level = '1'
                obj.type = '2'
            } else {
                obj.isParent = false
                obj.level = '2'
                obj.type = '3'
            }
            obj = Object.assign(obj, item)
            ztreeNodesArr.push(obj)
        })
        return ztreeNodesArr
    }
}