.fm-content-info .ivu-form-inline .ivu-form-item {
    margin: 0 !important;
    display: flex;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #fff;
}


.fm-content-info .fm-content-item .fm-content-wrap-title {
    margin: 0
}

.fm-content-info .fm-content-item .fm-content-inner {
    padding: 16px;
}

.fm-content-info .ivu-form-inline .ivu-form-item .ivu-form-item-label {
    min-height: 46px;
    background: #eff5fc;
    justify-content: flex-start;
    border-right: 2px solid #fff;
    border-left: 2px solid #fff;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    color: #616f6f;
    white-space: pre-wrap;
    padding-left: 5px;
}

.fm-content-info .ivu-form-inline .ivu-form-item .ivu-form-item-content {
    flex: 1;
    padding: 4px;
    background: #f4f7ff;
    min-height: 46px;
    color: #272c2c;
    font-size: 16px;
    margin: 0 !important;
    display: inline-flex;
    align-items: center;
    overflow: hidden;
}

.fm-content-info .ivu-form-inline .ivu-form-item .ivu-form-item-content .row-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fm-content-info .ivu-form-inline .ivu-form-item .ivu-form-item-content .row-inline .ivu-tooltip-rel {
    display: flex;
}

.fm-content-info .ivu-form-inline .ivu-form-item .ivu-form-item-content .ivu-date-picker {
    width: 100%;
}