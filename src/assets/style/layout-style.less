/**
 * 通用布局样式
 * 用于各种左右分栏、上下分栏等常见布局场景
 */

@import './variables.less';

/* ==================== 左右分栏布局 ==================== */

/**
 * 基础左右分栏容器
 * 使用场景：需要左右分栏的页面布局
 */
.bsp-layout-horizontal {
  width: 100%;
  display: flex;
  height: 100%;
  
  &-left {
    flex-shrink: 0; /* 防止左侧被压缩 */
    border-right: 1px solid @border-color-base;
    background: @background-color-base;
  }

  &-right {
    flex: 1; /* 右侧自适应剩余空间 */
    background: @background-color-base;
    overflow: hidden; /* 防止内容溢出 */
  }
}

/**
 * 带固定宽度的左右分栏
 * 使用场景：左侧宽度固定，右侧自适应
 */
.bsp-layout-horizontal-fixed {
  width: 100%;
  display: flex;
  height: 100%;
  
  &-left {
    width: @layout-left-width-default;
    flex-shrink: 0;
    padding: @padding-sm @padding-md;
    border-right: 1px solid @border-color-base;
    background: @background-color-base;
  }

  &-right {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;

    .form-box {
      border: 1px solid @border-color-base;
      margin-left: @margin-sm;
      border-radius: @border-radius-lg;
    }
  }
}

/**
 * 响应式左右分栏
 * 使用场景：需要在不同屏幕尺寸下调整布局
 */
.bsp-layout-responsive {
  width: 100%;
  display: flex;
  height: 100%;
  
  &-left {
    width: 25%;
    min-width: @layout-left-width-small;
    max-width: @layout-left-width-large;
    flex-shrink: 0;
    padding: @padding-sm @padding-md;
    border-right: 1px solid @border-color-base;
    background: @background-color-base;
  }

  &-right {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;
  }
}

/* ==================== 上下分栏布局 ==================== */

/**
 * 基础上下分栏容器
 * 使用场景：需要上下分栏的页面布局
 */
.bsp-layout-vertical {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &-top {
    flex-shrink: 0;
    border-bottom: 1px solid @border-color-base;
    background: @background-color-base;
  }

  &-bottom {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;
  }
}

/* ==================== 三栏布局 ==================== */

/**
 * 左中右三栏布局
 * 使用场景：需要三栏布局的复杂页面
 */
.bsp-layout-three-column {
  width: 100%;
  height: 100%;
  display: flex;
  
  &-left {
    width: @layout-three-left-width;
    flex-shrink: 0;
    border-right: 1px solid @border-color-base;
    background: @background-color-base;
  }

  &-center {
    flex: 1;
    border-right: 1px solid @border-color-base;
    background: @background-color-base;
    overflow: hidden;
  }

  &-right {
    width: @layout-three-right-width;
    flex-shrink: 0;
    background: @background-color-base;
    overflow: hidden;
  }
}

/* ==================== 工具类样式 ==================== */

/**
 * 内容区域通用样式
 */
.bsp-layout-content {
  padding: @padding-md;
  height: 100%;
  overflow: auto;

  &-no-padding {
    padding: 0;
  }

  &-small-padding {
    padding: @padding-sm;
  }

  &-large-padding {
    padding: @padding-lg;
  }
}

/**
 * 分割线样式
 */
.bsp-layout-divider {
  &-vertical {
    width: 1px;
    background: @border-color-base;
    flex-shrink: 0;
  }

  &-horizontal {
    height: 1px;
    background: @border-color-base;
    flex-shrink: 0;
  }
}

/**
 * 响应式断点
 */
@media (max-width: @screen-sm) {
  .bsp-layout-responsive {
    flex-direction: column;

    &-left {
      width: 100%;
      min-width: auto;
      max-width: none;
      border-right: none;
      border-bottom: 1px solid @border-color-base;
    }
  }
}

/* ==================== 特殊场景样式 ==================== */

/**
 * 人员选择页面专用布局
 * 使用场景：人员选择、设备选择等选择类页面
 */
.bsp-selection-layout {
  width: 100%;
  display: flex;
  height: 100%;

  &-left {
    width: @selection-left-width;
    flex-shrink: 0;
    background: @background-color-base;
    border-right: 1px solid @border-color-base;
  }

  &-right {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;
  }
}

/**
 * 管理页面通用布局
 * 使用场景：各种查询、管理、列表页面
 * 替代原来的 Inquiry-wrap
 */
.bsp-management-layout {
  width: 100%;
  display: flex;
  height: 100%;

  &-left {
    padding-top: @padding-sm;
    width: @layout-left-width-default;
    height: 100%;
    margin-left: @margin-md;
    flex-shrink: 0;
    background: @background-color-base;
  }

  &-right {
    flex: 1;
    border-left: 1px solid @border-color-base;
    background: @background-color-base;
    overflow: hidden;

    .form-box {
      border: 1px solid @border-color-base;
      margin-left: @margin-sm;
      border-radius: @border-radius-lg;
    }
  }
}

/**
 * 查询结果布局
 * 使用场景：搜索页面、查询结果展示
 */
.bsp-search-layout {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;

  &-header {
    flex-shrink: 0;
    padding: @padding-md;
    background: @background-color-light;
    border-bottom: 1px solid @border-color-base;
  }

  &-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    &-left {
      width: @layout-left-width-default;
      flex-shrink: 0;
      background: @background-color-base;
      border-right: 1px solid @border-color-base;
      overflow-y: auto;
    }

    &-right {
      flex: 1;
      background: @background-color-base;
      overflow: hidden;
    }
  }
}

/**
 * 表单详情布局
 * 使用场景：详情页面、表单页面
 */
.bsp-detail-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-header {
    flex-shrink: 0;
    height: 60px;
    padding: 0 @padding-md;
    background: @background-color-base;
    border-bottom: 1px solid @border-color-base;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      color: @text-color-primary;
    }

    &-actions {
      display: flex;
      gap: @spacing-sm;
    }
  }

  &-content {
    flex: 1;
    padding: @padding-md;
    background: @background-color-base;
    overflow-y: auto;
  }

  &-footer {
    flex-shrink: 0;
    height: 60px;
    padding: 0 @padding-md;
    background: @background-color-base;
    border-top: 1px solid @border-color-base;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: @spacing-md;
  }
}

/**
 * 工作台布局
 * 使用场景：仪表板、工作台页面
 */
.bsp-dashboard-layout {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 280px 1fr;
  grid-template-rows: auto 1fr;
  grid-template-areas:
    "sidebar header"
    "sidebar content";
  gap: 1px;
  background: @border-color-base;

  &-sidebar {
    grid-area: sidebar;
    background: @background-color-base;
    overflow-y: auto;
    padding: @padding-md;
  }

  &-header {
    grid-area: header;
    background: @background-color-base;
    padding: @padding-md;
    border-bottom: 1px solid @border-color-base;
  }

  &-content {
    grid-area: content;
    background: @background-color-base;
    padding: @padding-md;
    overflow-y: auto;
  }
}

/**
 * 卡片网格布局
 * 使用场景：卡片列表、网格展示
 */
.bsp-card-grid-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-header {
    flex-shrink: 0;
    padding: @padding-md;
    background: @background-color-base;
    border-bottom: 1px solid @border-color-base;

    &-title {
      font-size: @font-size-lg;
      font-weight: @font-weight-medium;
      margin-bottom: @margin-sm;
    }

    &-filters {
      display: flex;
      gap: @spacing-md;
      align-items: center;
    }
  }

  &-content {
    flex: 1;
    padding: @padding-md;
    overflow-y: auto;

    &-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: @spacing-md;
    }
  }
}

/**
 * 分步表单布局
 * 使用场景：多步骤表单、向导页面
 */
.bsp-wizard-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-steps {
    flex-shrink: 0;
    padding: @padding-md;
    background: @background-color-light;
    border-bottom: 1px solid @border-color-base;
  }

  &-content {
    flex: 1;
    padding: @padding-lg;
    background: @background-color-base;
    overflow-y: auto;
  }

  &-actions {
    flex-shrink: 0;
    padding: @padding-md;
    background: @background-color-base;
    border-top: 1px solid @border-color-base;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/**
 * 主从表布局
 * 使用场景：主从表关系、一对多数据展示
 */
.bsp-master-detail-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  &-master {
    flex-shrink: 0;
    height: 40%;
    background: @background-color-base;
    border-bottom: 1px solid @border-color-base;
    overflow: hidden;
  }

  &-detail {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;

    &-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ivu-tabs-content {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}

/**
 * 聊天布局
 * 使用场景：聊天界面、消息页面
 */
.bsp-chat-layout {
  width: 100%;
  height: 100%;
  display: flex;

  &-sidebar {
    width: 300px;
    flex-shrink: 0;
    background: @background-color-base;
    border-right: 1px solid @border-color-base;
    display: flex;
    flex-direction: column;

    &-header {
      flex-shrink: 0;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
    }

    &-list {
      flex: 1;
      overflow-y: auto;
    }
  }

  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: @background-color-base;

    &-header {
      flex-shrink: 0;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
    }

    &-content {
      flex: 1;
      padding: @padding-md;
      overflow-y: auto;
    }

    &-input {
      flex-shrink: 0;
      padding: @padding-md;
      border-top: 1px solid @border-color-base;
    }
  }
}

/* ==================== 嵌套布局样式 ==================== */

/**
 * 嵌套布局：外层左右分栏，左侧内部上下分栏
 * 使用场景：复杂的管理页面，左侧包含多个功能区域
 */
.bsp-nested-layout {
  width: 100%;
  height: 100%;
  display: flex;

  // 左侧区域（包含上下分栏）
  &-left {
    width: @layout-left-width-default;
    flex-shrink: 0;
    background: @background-color-base;
    border-right: 1px solid @border-color-base;
    display: flex;
    flex-direction: column;

    // 左侧顶部区域
    &-top {
      flex-shrink: 0;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
      background: @background-color-light;
    }

    // 左侧底部区域
    &-bottom {
      flex: 1;
      padding: @padding-md;
      overflow-y: auto;
      background: @background-color-base;
    }
  }

  // 右侧主要内容区域
  &-right {
    flex: 1;
    background: @background-color-base;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    // 右侧头部
    &-header {
      flex-shrink: 0;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
      background: @background-color-base;
    }

    // 右侧内容
    &-content {
      flex: 1;
      padding: @padding-md;
      overflow-y: auto;
      background: @background-color-base;
    }
  }
}

/**
 * 三层嵌套布局：外层左右，左侧上下，右侧也上下
 * 使用场景：复杂的数据管理系统
 */
.bsp-complex-nested-layout {
  width: 100%;
  height: 100%;
  display: flex;

  // 左侧复合区域
  &-left {
    width: @layout-left-width-large;
    flex-shrink: 0;
    background: @background-color-base;
    border-right: 1px solid @border-color-base;
    display: flex;
    flex-direction: column;

    // 左上：搜索条件
    &-search {
      flex-shrink: 0;
      height: 200px;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
      background: @background-color-light;
      overflow-y: auto;
    }

    // 左下：树形菜单或列表
    &-tree {
      flex: 1;
      padding: @padding-md;
      overflow-y: auto;
      background: @background-color-base;
    }
  }

  // 右侧复合区域
  &-right {
    flex: 1;
    background: @background-color-base;
    display: flex;
    flex-direction: column;

    // 右上：工具栏和统计
    &-toolbar {
      flex-shrink: 0;
      height: 80px;
      padding: @padding-md;
      border-bottom: 1px solid @border-color-base;
      background: @background-color-light;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    // 右中：主要数据表格
    &-main {
      flex: 2;
      padding: @padding-md;
      overflow: hidden;
      background: @background-color-base;
    }

    // 右下：详情或操作面板
    &-detail {
      flex: 1;
      min-height: 200px;
      padding: @padding-md;
      border-top: 1px solid @border-color-base;
      background: @background-color-light;
      overflow-y: auto;
    }
  }
}

/**
 * 响应式嵌套布局
 */
@media (max-width: @screen-md) {
  .bsp-nested-layout {
    flex-direction: column;

    &-left {
      width: 100%;
      height: 300px;
      border-right: none;
      border-bottom: 1px solid @border-color-base;

      &-top {
        height: 120px;
      }

      &-bottom {
        flex: 1;
      }
    }

    &-right {
      flex: 1;
    }
  }

  .bsp-complex-nested-layout {
    flex-direction: column;

    &-left {
      width: 100%;
      height: 400px;
      border-right: none;
      border-bottom: 1px solid @border-color-base;

      &-search {
        height: 150px;
      }
    }

    &-right {
      flex: 1;

      &-toolbar {
        height: 60px;
      }

      &-detail {
        min-height: 150px;
      }
    }
  }
}
