@assets2: '../images';
/* 重置复选框样式 */
.layui-form-checked i {
	border-color: #4ca9ff !important;
	background-color: #4ca9ff !important;
}

/* 导航栏样式 */
.nav-bar-panel {
	display: inline-block;
	width: 10rem;
	height: 100%;
	box-sizing: border-box;
	.nav-bar {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		overflow: hidden;
		li {
			position: relative;
			flex: 1;
			height: 100%;
			text-align: center;
			color: #7a8699;
			font-size: 0.16rem;
			cursor: pointer;
			position: relative;
			line-height: 0.46rem;
			.Number_box {
				display: inline-flex;
				justify-content: center;
				align-items: center;
				width: 0.28rem;
				height: 0.28rem;
				color: #cee0f0;
				background-color: #ffffff;
				border-radius: 50%;
				border: 2px solid #cee0f0;
				margin: 0.05rem 0.08rem 0 0;
				font-family: Source Han Sans CN;
			}
			.text {
				position: absolute;
				top: 50%;
				right: -0.22rem;
				transform: translateY(-50%);
				color: #cee0f0;
				font-size: 0.34rem;
			}
			&.active {
				color: #2f3648;
				font-size: 0.16rem;
				border-bottom: 2px solid #14dcac;
				.Number_box {
					color: #fff;
					border-color: #0cdaac;
					background: linear-gradient(14deg, #0cdaac 0%, #39e4ad 100%);
					box-shadow: 0px 3px 8px rgba(30, 255, 180, 0.3);
				}
				&:hover {
					color: #14dcac;
				}
			}
			&.done {
				color: #2f3648;
				font-size: 0.16rem;
				.Number_box {
					border-color: #14dcac;
					color: #14dcac;
					background-color: #ffffff;
					box-shadow: 0px 3px 8px rgba(30, 255, 180, 0.3);
				}
				&:hover {
					color: #14dcac;
				}
			}
		}
	}
}

/* 下拉框按钮样式 */
.green-btn {
	background: #2ccd7a;
	padding: 0 0.2rem;
	margin: 0 0.08rem;
	color: #fff;
}
.blue-btn {
	background: #087eff;
	padding: 0 0.4rem 0 0.1rem;
	margin: 0 0.08rem;
	color: #fff;
}
.operating .top_btn {
	display: flex;
	align-items: center;
	padding: 0.03rem 0.1rem;
	justify-content: center;
	border: 1px solid #087eff;
	background-color: #fff;
	color: #087eff;
	margin: 0 0.1rem;
	border-radius: 4px;
	img {
		width: 0.15rem;
		height: 0.15rem;
		margin-right: 0.05rem;
	}
}
.operating .top_btn:hover {
	cursor: pointer;
}
.operating_item {
	display: flex;
	align-items: center;
	height: 0.32rem;
	line-height: 0.32rem;
	box-sizing: border-box;
	font-size: 0.14rem;
	border-radius: 2px;
	cursor: pointer;
	position: relative;
	z-index: 222;
}
.operating_item .layui-icon-triangle-d2 {
	transform: rotate(180deg);
	position: absolute;
	right: 0.1rem;
	top: 0rem;
}
// .operating_item i {
//   position: absolute;
//   right: 0.1rem;
//   top: 0rem;
// }
.operating_item ul {
	width: 100%;
	position: absolute;
	top: 0.4rem;
	left: 0;
	border: 1px solid #329bfc;
	box-shadow: 1px 1px 1px 0px rgba(0, 0, 0, 0.1);
	border-radius: 0.04rem;
	box-sizing: border-box;
	display: none;
}
.operating_item ul li {
	color: #333;
	text-align: center;
	font-size: 0.16rem;
	background-color: #fff;
}
.operating_item ul li:hover {
	background: #329bfc;
	color: #fff;
}

/* 重置树状图样式 */
.ztree {
	position: relative;
}
.ztree li span {
	font-size: 0.16rem;
}

.ztree li {
	min-height: 0.3rem;
	line-height: 0.3rem;
}

.ztree li a {
	height: 0.3rem;
	line-height: 0.3rem;
}

.ztree li span.button.switch,
.ztree li span,
.ztree li span.button.ico_docu,
.ztree li span.button.ico_open,
.ztree li span.button.ico_close {
	height: 0.3rem;
	line-height: 0.3rem;
	display: inline-block;
}

.ztree li span.button.switch,
.ztree li span.button.ico_open {
	width: 0.3rem;
}

.ztree li a.curSelectedNode {
	border: none;
	height: auto;
}

/* 查看图标 */
.view-icon {
	position: absolute;
	top: 25px;
	width: 0.2rem;
	background: url('@{assets2}/dzjz/view.png') no-repeat center;
	background-size: 100%;
	height: 0.2rem;
	left: 0px;
	cursor: pointer;
}
/* 底部悬浮图标样式 */
.position_btm {
	width: 100%;
	height: 0.5rem;
	background-color: #66b5ff;
	left: 0;
	position: absolute;
	bottom: -3px;
	text-align: center;
	display: none;
}
.position_content_box > span {
	display: inline-block;
	width: 0.4rem;
	height: 0.4rem;
	cursor: pointer;
}
.position_content_box > span.shanchu {
	background: url('@{assets2}/dzjz/bmzj/del.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.cut {
	background: url('@{assets2}/dzjz/bmzj/cut.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.shangchuan {
	background: url('@{assets2}/dzjz/bmzj/upload.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.rotate {
	background: url('@{assets2}/dzjz/bmzj/rotate.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.split {
	background: url('@{assets2}/dzjz/bmzj/split.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.move {
	background: url('@{assets2}/dzjz/bmzj/move.png') no-repeat center;
	background-size: 100%;
}
.position_content_box > span.jiuzheng {
	background: url('@{assets2}/dzjz/bmzj/jiuzheng.png') no-repeat center;
	background-size: 50%;
}
.position_content_box > span.flag {
	background: url('@{assets2}/dzjz/bmzj/sign.png') no-repeat center;
	background-size: 50%;
}
.position_content_box > span.catalog {
	background: url('@{assets2}/dzjz/bmzj/more-icon.png') no-repeat center;
	background-size: 50%;
}
.position_content_box > span {
	background-color: #087eff;
}
.edit_index {
	display: block;
	width: 0.16rem;
	height: 0.16rem;
	background: url('@{assets2}/dzjz/bmzj/edit_index.png') no-repeat center;
	background-size: 100%;
	float: left;
	margin-top: 3px;
}

/* 粘贴样式 */
.copy-panel {
	position: absolute;
	bottom: 0.37rem;
	width: 100%;
	z-index: 1;
	background-color: #353232;
	line-height: 30px;
	color: #fff;
	display: none;
	left: 0;
}
.copy-panel .num-panel {
	display: block;
	float: left;
	width: 20px;
	height: 26px;
	text-align: center;
	background-color: #ffa925;
	cursor: pointer;
	margin-right: 2px;
}
.copy-panel .num-panel:hover {
	background-color: #dfa64e;
}
.copy-panel .allCopy {
	display: block;
	float: left;
	padding: 0 5px;
	background-color: rgba(0, 0, 0, 0.8);
	cursor: pointer;
}
.copy-panel .close {
	display: block;
	float: left;
	background-color: rgba(0, 0, 0, 0.8);
	padding: 0 5px;
	cursor: pointer;
}
.copy-panel .allCopy:hover,
.copy-panel .close:hover {
	background-color: #4d4a4a;
}

/* 公共框架样式 */
.dzjz-panel {
	height: 100%;
	.dzjz-content {
		height: 100%;
		overflow: hidden !important;
		.top-title {
			right: 0;
			top: 0.08rem;
			display: flex;
			justify-content: space-between;
			align-items: center;
			a {
				display: inline-block;
				max-width: 450px;
				font-size: 0.3rem;
				font-weight: bold;
				color: #0e6ae1;
				text-decoration: underline;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.operating {
				display: flex;
				align-items: center;
			}
		}
		.dzjz-main {
			height: 100%;
			display: flex;
		}
		.dzjz-left {
			width: 3.4rem;
			background: #fff;
			border-right: 1px solid #cce6ff;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			.title {
				line-height: 0.4rem;
				width: 100%;
				text-align: center;
				color: #333;
				font-size: 0.16rem;
				font-weight: bold;
				border-bottom: 1px solid #cce6ff;
			}
			.tree_box {
				flex: 1;
				overflow: auto;
			}
			.save-template-btn {
				width: 0.96rem;
				height: 0.32rem;
				background-color: #fff;
				color: #087eff;
				border: 1px solid #087eff;
				line-height: 0.32rem;
				border-radius: 0.04rem;
				text-align: center;
				font-size: 0.16rem;
				cursor: pointer;
			}
		}
	}
	.jz-img-container {
		height: calc(~'100% - 0.48rem');
		overflow-y: auto;
		display: flex;
		flex-direction: row;
		background-color: #dde4eb;
		border-top: 0.02rem solid #087eff;
		padding: 0.1rem;
		.left-tab-container {
			width: 3rem;
			border: 0.01rem solid #cce6ff;
			.tab-btn {
				display: block;
				width: 100%;
				height: 0.48rem;
				line-height: 0.48rem;
				border-bottom: 0.01rem solid #d1e4f5;
				color: #2e77e6;
				text-align: center;
				background-color: #fff;
				cursor: pointer;
				&.active {
					background: #329bfc;
					color: #fff;
				}
			}
		}
		.right-img-wrapper {
			padding: 0.2rem 0.2rem 0;
			text-align: center;
			flex: 1;
			height: 100%;
			.img-item {
				img {
					max-width: 96%;
				}
			}
		}
	}
}
