/*
* @description 全局less变量&函数
* @Author: yyy  
* @Date: 2023-03-24 16:41:22  
 * @Last Modified by: yyy
 * @Last Modified time: 2023-03-27 11:14:14
*/

/* form表单布局样式 */
@form_item_content_col_2: 6rem; // 两列布局：form-item-content长度
@form_item_content_col_3: 4rem; // 三列布局：form-item-content长度
@form_item_content_col_4: 3rem; // 四列布局：form-item-content长度

// form-item-content宽度 列表
@col_width_list: 0,@form_item_content_col_2,@form_item_content_col_3,@form_item_content_col_4;

/* 计算独占一行表单宽度函数
 * @formColNum 表单列数
 * @labelWidth label宽度
*/
.item_content_width(@formColNum, @labelWidth) {
    @percentage: percentage(1 - 1/@formColNum);
    width: calc( @percentage ~'+' extract(@col_width_list, @formColNum) ~'+' @labelWidth);
}

@com-color-risk-one: #FA4242;
@com-color-risk-two: #FF7700;
@com-color-risk-three: #FFBD13;