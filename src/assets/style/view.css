.clbd-panel{
    display:flex;
    flex-direction:column;
    height:100%;
}
.clbd-panel .top-panel{
    flex:1;
    display:flex;
    flex-direction:row;
    background: #5e6266;
    min-height:0%;
    height: 90%;
}
.clbd-panel .bottom-panel{
    height: 0.6rem;
    text-align: center;
    padding-left:48%;
}
.clbd-panel .bottom-panel .btn-style{
    margin-top:0.12rem;
}
.clbd-panel .clbd-content{
    flex:1;
    padding:0.1rem;
    height: 100%;
}
.clbd-panel  .left-panel{
    height: 100%;
    width: 1.6rem;
    overflow: auto;
}

.clbd-panel  .left-panel .clbd-list{ 
    height: 100%;
    text-align:center;
    background: #c0c6cc;
}
.clbd-panel  .left-panel .clbd-list img{
    width: 90%;
    margin: 0.1rem auto;
    border: 0.02rem solid transparent;
}
.clbd-panel  .left-panel .clbd-list .img.active{
    border: 0.02rem solid #087eff;
    cursor:pointer;
}
.clbd-panel  .left-panel .clbd-list .img:hover{
    border: 0.02rem solid #087eff;
    cursor:pointer;
}
.clbd-panel .clbd-content .clbd-item{
    width:50%;
    float:left;
    height:100%;
    border:1px solid #cecece;
    box-sizing: border-box;
    position: relative;
    border:3px solid transparent;
}
.clbd-panel .clbd-content .clbd-item.active{
    border:3px solid #66B5FF;
}
.clbd-panel .clbd-content .clbd-item img{
    width:100%;
    height:100%;
}

.zkyx-panel{
    width:100%;
    height:100%;
    border-top: 2px solid #087eff;
}
.zkyx-panel .left{
    width:45%;
}
.zkyx-panel .right{
    width:55%;
}
.zkyx-panel .left,
.zkyx-panel .right{
    height: 100%;
    float: left;
    /* overflow: auto; */
}
.zkyx-panel .left .img-list{
    padding: 0.3rem 0.1rem 0.3rem 0.1rem;
    background: #5e6266;
    height: 100%;
}
.zkyx-panel .left .img{
    width:100%;
    margin:0.1rem auto;
}
.zkyx-panel .right{
    flex-direction:column;
    background:#fff;
}
.zkyx-panel .right .right-content{
    height: 89%;
    min-height:0;
    border-bottom:1px solid #CCCCCC;
    position:relative;
}
.zkyx-panel .right .right-content .edit-content{
    position:absolute;
    width:100%;
    height:100%;
}
.zkyx-panel .right .right-content .edit-content .cke_chrome{
    height:100%;
}
.zkyx-panel .right .right-content .edit-content .cke_chrome{
    height:100%;
}
.zkyx-panel .right .right-content .edit-content .cke_inner{
    height:100%;
    display:flex;
    flex-direction:column;
}
.zkyx-panel .right .right-content .edit-content .cke_contents{
    flex:1;
}
.zkyx-panel .right .right-bottom{
    text-align: center;
    height: 6%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.zkyx-panel .right .right-top .edit-title{
    color:#0E6AE1;
    text-align: center;
    font-weight: bolder;
    line-height:0.4rem;
    font-size:18px;
    background: #dde4eb;
    width:100%;
}
.zkyx-panel .right .right-top .edit-title::-webkit-input-placeholder{
    color: #0E6AE1;
}
.zkyx-panel .right .right-top .edit-title::-moz-placeholder{  
    color: #0E6AE1;        
}
.zkyx-panel .right .right-top .edit-title:-ms-input-placeholder{ 
    color: #0E6AE1;        
}
/* 重置轮播左右切换按钮颜色 */
.layui-carousel-arrow{
    top:auto;
    bottom:0.1rem;
}
.layui-carousel[lay-arrow=always] .layui-carousel-arrow{
    left:25%;
}
.layui-carousel:hover .layui-carousel-arrow[lay-type=add], 
.layui-carousel[lay-arrow=always] .layui-carousel-arrow[lay-type=add]{
    right:25%;
}
/* 右侧tab样式 */
.tab-panel{
    border: solid 1px #e6e6e6;
    box-shadow: 0 0 10px 3px #e0e0e0;
}
.tab-panel .layui-tab{
    display:flex;
    flex-direction: column;
    padding:0;
    margin:0;
    height:100%;
}
.layui-tab-content{
    flex:1;
    overflow:auto;
}
.tab-panel .layui-tab-title{
    display:flex;
    height:0.48rem;
    background: #dce9f5;
}
.tab-panel .layui-tab-title li{
    padding:0;
    margin:0;
    text-align:center;
    line-height: 0.48rem;
    flex:1;
    font-size: 16px;
    border-top:4px solid #dce9f5;
}
.tab-panel .layui-tab-title li.layui-this{
    border-top:4px solid #087eff;
    color: #087eff;
    background:#fff;
}
.tab-panel .compare-panel{
    display:flex;
    justify-content: center;
    align-items: flex-start;
}
.tab-panel .compare-panel img{
    width:0.96rem;
    height:0.72rem;
    margin-right:0.05rem;
}
.tab-panel .compare-panel .compare-des .title{
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color:#666;
    height:0.36rem;
    font-size: 0.12rem;
    margin-bottom: 0.05rem;
}
.tab-panel .compare-panel .compare-des{
    flex:1;
    font-size:12px;
    color: #aaaaaa;
}
.tab-panel .compare-panel .compare-des p,.tab-panel .compare-panel .compare-des .date-panel{
    font-size: 0.12rem;
}
.tab-panel .item-panel{
    padding: 0.05rem;
    border: 1px solid #e1ebf5;
    /* box-shadow: 0px 2px 6px 0px #e5e5e5; */
    margin-bottom: 0.16rem;
}
.tab-panel .item-panel:hover{
    border: 1px solid #66b5ff;
    background: #ebf5ff;
}
.tab-panel .new-btn{
    width: 1.2rem;
    height: 0.32rem;
    line-height:0.32rem;
    background: #f5f9ff;
    border: 1px solid #0394f9;
    border-radius: 2px;
    text-align:center;
    margin:0 auto 0.05rem;
    cursor:pointer;
}
.tab-panel .item-panel .top-panel{
    display: flex;
    cursor: pointer;
    margin-bottom: 0.1rem;
}
.tab-panel .item-panel .num{
    font-size: 12px;
    font-weight: 700;
    text-align: center;
    color: #ffffff;
    line-height: 18px;
    background: #398dee;
    border-radius: 2px;
    display: inline-block;
    margin-right: 0.05rem;
    padding: 0 0.04rem;
}
.tab-panel .item-panel .new-word{
    display:inline-block;
    width: 0.12rem;
    height: 0.15rem;
    background-size: 100%;
    background: url('../images/dzjz/znyj/word.png') no-repeat center;
    margin-right:0.05rem;

}
.tab-panel .item-panel .top-panel p{
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.tab-panel .item-panel .bottom-panel{
    display: flex;
    flex-direction: row;
    color: #aaaaaa;
}
.tab-panel .item-panel .bottom-panel .person-name{
    min-width:0.8rem;
}
.tab-panel .item-panel .bottom-panel  .date-panel{
    flex:1;
}
.intelMarking .btn-panel .icon-btn{
    display:inline-block;
    width: 0.2rem;
    height: 0.2rem;
    background-size: 100%;
}
.tab-panel .item-panel .bottom-panel  .btn-panel .icon-edit{
    background: url('../images/dzjz/znyj/edit.png') no-repeat center;
}
.tab-panel .item-panel .bottom-panel  .btn-panel .icon-edit:hover{
    cursor:pointer;
    background: url('../images/dzjz/znyj/edit-select.png') no-repeat center;
}
.tab-panel .item-panel .bottom-panel  .btn-panel .icon-delete{
    background: url('../images/dzjz/znyj/del.png') no-repeat center;
}
.tab-panel .item-panel .bottom-panel  .btn-panel .icon-delete:hover{
    cursor:pointer;
    background: url('../images/dzjz/znyj/del-select.png') no-repeat center;
}

/* 左侧树样式 */

.ztree li span {
    font-size: 0.16rem;
}

.ztree li {
    min-height: 0.3rem;
    line-height: 0.3rem;
}

.ztree li a {
    height: 0.3rem;
    line-height: 0.3rem;
}

.ztree li span.button.switch,
.ztree li span,
.ztree li span.button.ico_docu,
.ztree li span.button.ico_open,
.ztree li span.button.ico_close {
    height: 0.3rem;
    line-height: 0.3rem;
    display: inline-block;
}

.ztree li span.button.switch,
.ztree li span.button.ico_open {
    /* width: 0.35rem; */
    width: 0.15rem;
}

.ztree li a.curSelectedNode {
    border: none;
    height: auto;
}
.right-btn-panel .btn-icon.active{
    background-color: #a3a8ad !important;
    cursor: pointer;
}
#fb7-footer{ 		 		 
    background: -webkit-linear-gradient(#FFFFFF,#EAEAEA); /* For Safari 5.1 to 6.0 */
    background: -o-linear-gradient(#FFFFFF,#EAEAEA); /* For Opera 11.1 to 12.0 */
    background: -moz-linear-gradient(#FFFFFF,#EAEAEA); /* For Firefox 3.6 to 15 */
    background: linear-gradient(#FFFFFF,#EAEAEA); /* Standard syntax */		  
    border-radius:10px;
    border-bottom: 1px solid #FFFFFF;
    box-shadow: 0 4px 0 #878787;		 		  
    opacity:0.55;
    width: 0.64rem;
    height: 3.5rem;
    position: absolute;
    bottom: calc(50% - 1.75rem);
    right: 15px;
    padding: 0.2rem 0 0.1rem;
    box-sizing: border-box;
}
#fb7 .next {
    display: inline-block;
    width: 0.65rem;
    height: 0.65rem;
    background: url('../images/dzjz/book-right.png') no-repeat center ;
    background-size: 100% 100% ;
    position: absolute;
    top: calc(50% - 0.33rem) ;
    right: -0.8rem ;
}
#fb7 .prev {
    display: inline-block;
    width: 0.65rem;
    height: 0.65rem;
    background: url('../images/dzjz/book-left.png') no-repeat center ;
    background-size: 100% 100% ;
    position: absolute;
    top: calc(50% - 0.33rem);
    left:-0.8rem;
}
#fb7 .prev:hover {
    width: 0.78rem !important;
    height: 0.78rem !important;
    background: url('../images/dzjz/book-checkLeft.png') no-repeat center !important;
    /* background-size: 100% 100% !important; */
    transition: all ease 0.3s;
    top: calc(50% - 0.39rem);
    left:-0.8rem;
}


#fb7 .next:hover {
    width: 0.78rem;
    height: 0.78rem;
    background: url('../images/dzjz/book-checkRight.png') no-repeat center !important;
    background-size: 100% 100% !important;
    transition: all ease 0.3s;
    top: calc(50% - 0.39rem) ;
    right: -0.8rem ;
}
#fb7 .fb7-menu li.fb7-goto input {
    background: #fff;
    width: 0.8rem;
    height: 0.3rem;
    padding: 0 0.2rem 0 0.07rem;
    text-align: center;
    font-size: 0.14rem;
    font-family: Nunito;
    font-weight: 700;
    color: #333;
    display: inline;
    border-radius: 0.15rem;
    box-shadow: inset 1px 2px 3px #ccc;
}
.turnBtn{
    display: inline-block;
    background: #503931;
    position: absolute;
    right: -51px;
    top: 0;
    height: 0.3rem;
    line-height: 0.3rem;
    width: 0.8rem;
    letter-spacing: 3px;
    font-size: 0.14rem;
    border-radius: 0 0.15rem 0.15rem 0;
    color: #fff;
    font-weight: normal;
    text-align: center;
    cursor: pointer;
}