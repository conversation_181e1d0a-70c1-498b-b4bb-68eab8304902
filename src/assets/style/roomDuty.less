@border-color: #E4EAF0;
@com-color-risk-one: #FA4242;
@com-color-risk-two: #FF7700;
@com-color-risk-three: #FFBD13;
.duty-arrange-board {
  border-top: 1px solid @border-color;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  .arrange-box{
    display: flex;
    flex-direction: column;
  }
  .header-box {
    width: 100%;
    text-align: center;
    border-bottom: 1px solid @border-color;
    border-right: 1px solid @border-color;
    background: #FFFFFF;
  }
  .content-box {
    width: 100%;
    border-bottom: 1px solid @border-color;
    border-right: 1px solid @border-color;
  }
  .shift-box{
    .shift-header{
      position: relative;
      background-image:linear-gradient(to bottom left, transparent 49.4% , @border-color 50%, transparent 50.6%);
      span {
        position: absolute;
        font-size: 16px;
        color: #415060;
      }
      .class {
        top: 16px;
        right: 20px;
      }
      .date {
        bottom: 16px;
        left: 20px;
      }
    }
    .shift-content{
      background-position: bottom right ;
      background-repeat: no-repeat;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #FFFFFF;
      padding: 0 10px;
      &.all-day{
        background-color: #ffdc08;
      }
      &.night-box{
        background-color: #7991C8;
        background-image: url("../images/prisonRoomDuty/bg_evening.png");
      }
      &.morning-box{
        background-color: #FEBC24;
        background-image: url("../images/prisonRoomDuty/bg_morning.png");
      }
      &.afternoon-box{
        background-color: #FF923D;
        background-image: url("../images/prisonRoomDuty/bg_afternoon.png");
      }
      &.type0 {
        background: #2390FF;
      }
      &.type1 {
        background: #00C170;
      }
      &.type2 {
        background: #FF980A;
      }
      .shift-name {
        line-height: 21px;
        margin-bottom: 8px;
        font-size: 16px;
        text-align: center;
      }
      .triangle{
        margin: 8px 0 4px 0;
        display: inline-block;
        border: 4px solid transparent;
        border-top: 4px solid #FFFFFF;
      }
      .shift-time {
        font-weight: bold;
        font-size: 18px;
        line-height: 24px;
      }
    }
  }
  .duty-box {
    position: relative;
    .duty-header {
      padding: 16px 0;
      color: #8D99A5;
      font-size: 18px;

      &.current-duty {
        color: #2390FF;
        background: #FFFFFF;
      }

      &.choice-duty {
        color: #FFFFFF;
        background: #2390FF;
      }

      .time {
        font-size: 16px;
      }
    }
    .duty-content {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #FFFFFF;
    }
  }
}
.duty-group {
  padding-top: 24px;
  position: relative;
  width: 100%;
  &:after {
    width: calc(~'100% + 8px');
    height: calc(~'100% - 16px');
    content: "";
    display: inline-block;
    background: rgba(88,100,228,0.4);
    border-radius: 6px;
    border: 2px solid #5864E4;
    position: absolute;
    left: -4px;
    bottom: -4px;
    z-index: 1
  }
  .group-name {
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    position: absolute;
    background: url("../images/prisonRoomDuty/group_bg.png");
    background-size: 100% 100%;
    width: 100%;
    max-width: 136px;
    color: #FFFFFF;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    .close {
      margin-left: 8px;
      background: url("../images/prisonRoomDuty/group_close.png");
      width: 12px;
      height: 13px;
      display: inline-block;
      cursor: pointer;
    }
  }
  .group-person {
    position: relative;
    z-index: 2;
  }
}
.person-tag {
  margin-bottom: 4px;
  font-size: 16px;
  display: inline-block;
  width: 26px;
  height: 21px;
  text-align: center;
  line-height: 19px;
  border-radius: 4px;
  border: 1px solid rgba(255,255,255,0.4);
  color: #FFFFFF;
  &.risk-one{
    background: @com-color-risk-one;
  }
  &.risk-two{
    background: @com-color-risk-two;
  }
  &.risk-three{
    background: @com-color-risk-three;
  }
  &.sick {
    background: #7A7C86;
  }
}