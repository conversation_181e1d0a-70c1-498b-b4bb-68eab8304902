.card-default() {
  box-shadow: 0 2px 6px 0 rgba(0, 34, 84, 0.12);
  &:after {
    border: 1px solid #D9E1EA;
  }
}
.card-hover() {
  box-shadow: 0 2px 8px 0 rgba(21, 131, 243, 0.3);
  &:after {
    border: 1px solid #2390FF;
  }
}
.card-active() {
  box-shadow: 0 2px 6px 0 rgba(0, 34, 84, 0.12);
  &:after {
    border: 2px solid #2390FF;
  }
}

.bsp-common-card {
  background: #FFFFFF;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  .card-default;
  .card-head {
    display: flex;
    width: 100%;
    position: relative;
    > div {
      position: relative;
      z-index: 2;
    }
    &.head-line {
      &:after {
        position: absolute;
        content: "";
        display: inline-block;
        width: 100%;
        height: 1px;
        background:  #E4EAF0;
        bottom: -13px;
        left: 0;
      }
    }
  }
  .card-content {
    position: relative;
    width: 100%;
    z-index: 2;
  }
  .card-checkbox {
    position: absolute;
    left: 4px;
    top: 4px;
    z-index: 3;
  }
  &:after {
    position: absolute;
    content: "";
    display: inline-block;
    width:100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 6px;
    z-index: 1;
  }
  &:not(.card-active):not(.card-disabled):hover{
    .card-hover;
  }
  &.card-active {
    .card-active;
  }
  &.card-disabled{
    cursor: not-allowed;
    .card-head{
      opacity: 0.4;
    }
    .card-content{
      opacity: 0.4;
    }
  }
}