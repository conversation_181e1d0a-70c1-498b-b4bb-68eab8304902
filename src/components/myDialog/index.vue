<template>
	<transition name="fade">
		<div class="my-alert-wrapper" v-show="show">
		<div class="my-alert-box">
			<div class="alert-box-header">
				<i class="alert-box-header-icon" :class="iconName"></i>
				<div class="alert-box-title">{{ title }}</div>
			</div>

			<div class="alert-box-content">
				<div class="alert-box-container" v-html="content"></div>
			</div>

			<div class="alert-box-btns">
				<my-btn v-if="this.showCancelBtn" plain size="message" class="cancel-btn"  @click="handleAction('cancel')">{{ cancelText }}</my-btn>
				<my-btn size="message" class="confirm-btn"  @click="handleAction('confirm')">{{ confirmText }}</my-btn>
			</div>
		</div>
		</div>
	</transition>
</template>

<script>
export default {
	name: 'MyAlert',
	data () {
		return {
			title: '信息提示',
			content: '',
			type: 'success',
			show: false,
			onOk: null,
			onCancel: null,
			showCancelBtn: true,
			cancelText: '取消',
			confirmText: '确定'
		}
	},
	computed: {
		iconName() {
			const name = `alert-icon-${this.type}`
			return name
		}
	},
	methods: {
		handleAction (action) {
			if(action === 'confirm') {
				this.onOk && this.onOk()
			} else {
				this.onCancel && this.onCancel()
			}
			this.destroyVm()
		},
		destroyVm () {
			this.show = false
			setTimeout(() => {
				this.$destroy(true)
				this.$el && this.$el.parentNode.removeChild(this.$el)
			}, 500)
		}
	}
}
</script>

<style lang="less" scoped>
.fade-enter-active, .fade-leave-active {
	transition: opacity .3s;
}
.fade-enter, .fade-leave-to  {
	opacity: 0;
}

.my-alert-wrapper {
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	justify-content: center;
	align-items: center;
	background: rgba(0, 9, 26, 0.6);
	z-index: 2023;
	.my-alert-box {
		// width: 280px;
		padding: 16px;
		font-size: 14px;
		border-radius: 4px;
		background-color: #fff;
		.alert-box-header {
			display: flex;
			align-items: center;
			.alert-box-title {
				font-weight: bold;
				margin-left: 10px;
			}
			.alert-box-header-icon {
				display: block;
				width: 18px;
				height: 18px;
				&.alert-icon-success {
					background: url('../../assets/images/components/alert-success.png');
				}
				&.alert-icon-confirm {
					background: url('../../assets/images/components/alert-confirm.png');
				}
				&.alert-icon-warning {
					background: url('../../assets/images/components/alert-warning.png');
				}
				&.alert-icon-error {
					background: url('../../assets/images/components/alert-fail.png');
				}
			}
		}
		.alert-box-content {
			margin-top: 12px;
			padding-left: 28px;
		}
		.alert-box-btns {
			display: flex;
			justify-content: flex-end;
			margin-top: 16px;
			.my-btn-container:not(:first-child) {
				margin-left: 12px;
			}
		}
	}
}
</style>
