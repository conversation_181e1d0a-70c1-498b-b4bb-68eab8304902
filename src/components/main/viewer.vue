<template>
  <div id="viewer">
    <headerWrap/>
    <router-view :key="$route.path" v-if="isRouterAlive"></router-view>
  </div>
</template>

<script>
import headerWrap from "@/components/main/header.vue"
export default {
  name: 'viewer',
  components:{
    headerWrap
  },
  provide () {
    return {
      reload: this.reload
    }
  },
  data () {
    return {
      isRouterAlive: true
    }
  },
  methods: {
    reload () {
      let that = this
      that.isRouterAlive = false
      that.$nextTick(function () {
        that.isRouterAlive = true
      })
    }
  }
}
</script>

<style lang="less" scoped>
.size{
  width: 100%;
  height: 100%;
}
html,body{
  .size;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
#app ,#viewer{
  .size;
  display: flex;
  flex-direction: column;
}
.main-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;
  padding: 0;
  background-color: #F0F3FA;
}
</style>
<style lang="less">
.main-content {
  & > div {
    padding: 0 0.2rem 0.2rem;
  }
}
</style>
