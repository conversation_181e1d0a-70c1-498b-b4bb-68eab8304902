<template>
  <div class="dbsx">
    <div class="top">
      <div class="title">
        {{ dbsx.title }}（<span class="num">{{ dbsx.num }}</span
        >）
      </div>
      <Tabs v-model="choseTabs" @on-click="getMsg(choseTabs)">
        <TabPane
          v-for="(item, index) in tabList"
          :label="showTabNum(item)"
          :name="item.value"
          :key="index"
        ></TabPane>
      </Tabs>
      <div class="more" @click="showMore">更多＞</div>
    </div>
    <div class="content">
      <div v-for="(item, index) in dbsxList" :key="index" class="content-item">
        <div class="content-item-title">{{ item.title }}</div>

        <Tooltip
          :content="item.content"
          max-width="200"
          :disabled="!isShowTip"
          class="content-item-tooltip"
        >
          <div
            class="content-item-content"
            @mouseenter="checkOverflow(index)"
          >
            {{ item.content }}
          </div>
        </Tooltip>

        <div class="content-item-time">{{ item.time }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'

export default {
  name: "dbsx",
  data() {
    return {
      dbsx: {
        title: "待办事项",
        num: 0,
      },
      choseTabs: "all",
      tabList: [
        {
          value: "all",
          num: 0,
          title: "全部",
        },
        {
          value: "approval",
          num: 0,
          title: "待审核",
        },
        {
          value: "catalog",
          num: 0,
          title: "待编目",
        },
      ],
      dbsxList: [],
      isShowTip: false,
    };
  },
  mounted() {
    this.getMsgCount()
    this.getMsg(this.choseTabs)
  },
  methods: {
    ...mapActions(['authGetRequest', 'authPostRequest', 'postRequest']),
    // 渲染tabs上的数字
    showTabNum(e) {
      return (h) => {
        return h("div", [
          h(
            "div",
            {
              class: "tab-item-style",
            },
            e.title
          ),
          h(
            "div",
            {
              class: "tab-item-style",
            },
            "（" + e.num + "）"
          ),
        ]);
      };
    },
    getMsgCount(){
      this.authGetRequest({
        url: this.$path.msg_get_count_url,
        params: {}
      }).then(res => {
        if (res.success) {
          this.dbsx.num = res.data.allCount
          this.tabList[0].num = res.data.allCount
          this.tabList[1].num = res.data.approvalCount
          this.tabList[2].num = res.data.catalogCount
        } else {
          this.errorModal({ content: res.msg })
        }
      })
    },
    getMsg(type){
      this.authGetRequest({
        url: this.$path.msg_get_url,
        params: {
          type: type
        }
      }).then(res => {
        if (res.success) {
          this.dbsxList = res.data.rows.map(item => {
            return {
              title: item.busTypeName,
              content: item.content,
              time: item.sTime,
              url: item.mobileUrl
            }
          })
        } else {
          this.errorModal({ content: res.msg })
        }
      })
    },
    // 检测是否文字显示提示框
    checkOverflow(index) {
      this.isShowTip = false;
      setTimeout(() => {
        const el = document.querySelectorAll(".content-item-content")[index];
        if (el) {
          this.isShowTip = el.scrollHeight > el.clientHeight;
        }
      }, 100);
    },
    closeTip() {
      console.log(1111);
    },
    showMore() {
      this.$router.push({
        name: "message",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.dbsx {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 10px;
  display: flex;
  flex-direction: column;

  .top {
    position: relative;

    .title {
      font-size: 20px;
      font-weight: bold;
    }

    .num {
      color: red;
    }

    /deep/.tab-item-style {
      text-align: center;
      font-size: 14px;
      font-weight: bold;
    }

    .more {
      position: absolute;
      top: 30%;
      right: 0;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .content-item {
      display: flex;
      background-color: rgb(242, 245, 252);
      height: 70px;
      margin-bottom: 10px;
      cursor: pointer;

      .content-item-title {
        width: 100px;
        height: 40px;
        background-color: rgb(43, 95, 217);
        color: #fff;
        text-align: center;
        font-weight: bold;
        line-height: 40px;
        border-radius: 0 0 20px 0;
      }
      .content-item-tooltip {
        flex: 1;
        overflow: hidden;
        /deep/.ivu-tooltip-rel {
          height: 100%;
        }
      }
      .content-item-content {
        width: 100%;
        height: 100%;
        padding: 4px 10px;
        font-size: 14px;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      .content-item-time {
        height: 70px;
        line-height: 70px;
        font-size: 16px;
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
}
</style>
