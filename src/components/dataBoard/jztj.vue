<template>
  <div class="jztj">
    <div class="top">
      <div class="top-left">
        <div class="title">卷宗统计情况</div>
      </div>
      <div class="top-right">
        <div
          v-for="item in dateList"
          class="top-right-item"
          :class="{ 'top-right-item-active': choseDate == item.value }"
          @click="changeDate(item.value)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div id="myChartBox" class="myChartBox" ref="chartContainer"></div>
    <div class="title titles">卷宗借阅情况</div>
    <div id="jyChartBox" class="jyChartBox" ref="jyChartContainer"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import noDataPic from '@/assets/images/noData.png'
const elementResizeDetectorMaker = require('element-resize-detector')
export default {
  name: 'jztj',
  data () {
    return {
      choseDate: 'days',
      dateList: [
        {
          name: '近七天',
          value: 'days'
        },
        {
          name: '本月',
          value: 'month'
        },
        {
          name: '本年',
          value: 'year'
        }
      ],
      dataList: [
        { value: 10, name: '已登记' },
        { value: 24, name: '已组卷' },
        { value: 6, name: '已归档' }
      ],
      total: 40,
      options: {},
      myChart: null,
      jyOptions: {},
      jyMyChart: null,
      jyXData: [
        '02/01',
        '02/02',
        '02/03',
        '02/04',
        '02/05',
        '02/06',
        '02/07',
        '02/08',
        '02/09',
        '02/10',
        '02/11',
        '02/12'
      ],
      jyYData: [
        '60',
        '40',
        '70',
        '75',
        '64',
        '55',
        '62',
        '68',
        '75',
        '85',
        '30',
        '48'
      ],
      noDataPic
    }
  },
  mounted () {
    this.init()
    // 监听myChartBox元素宽度的变化 让图表自适应相应
    let erd = elementResizeDetectorMaker()
    let that = this
    erd.listenTo(document.getElementsByClassName('jztj')[0], (ele) => {
      if (that.myChart) {
        that.myChart.resize()
      }
      if (that.jyMyChart) {
        that.jyMyChart.resize()
      }
    })
  },
  methods: {
    changeDate (e) {
      this.choseDate = e
    },
    createChart () {
      let _this = this
      this.options = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '10%',
          top: 'center',
          itemGap: 20,
          itemWidth: 20,
          formatter: function (name) {
            var total = 0
            var target
            for (var i = 0, l = _this.dataList.length; i < l; i++) {
              total += _this.dataList[i].value
              if (_this.dataList[i].name == name) {
                target = _this.dataList[i].value
              }
            }
            return (
              target +
              '   ' +
              ((target / total) * 100).toFixed(1) +
              '%' +
              '   ' +
              name
            )
          },
          textStyle: {
            padding: [0, 0, -3, 0],
            fontSize: 14,
            fontWeight: 'bold',
            color: '#333'
          }
        },
        graphic: {
          // 使用 graphic 组件添加自定义文本
          elements: [
            {
              type: 'text', // 文本
              left: 'center', // 水平居中
              top: 'center', // 垂直居中
              style: {
                text: `总数\n\n${this.total}`,
                textAlign: 'center',
                fill: '#1E7CE8', // 文字的颜色
                fontSize: 20,
                lineHeight: 20
              }
            }
          ]
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (colors) {
                  var colorList = [
                    'rgba(245, 154, 35, 1)',
                    'rgba(0, 191, 191, 1)',
                    'rgba(236, 128, 141, 1)'
                  ]
                  return colorList[colors.dataIndex]
                }
              }
            },
            data: this.dataList
          }
        ]
      }
      this.initChart(this.options)
    },
    initChart (eOptions) {
      if (this.myChart && this.myChart.dispose) {
        // 移去上次渲染的结果，确保本次不受影响
        this.myChart.dispose()
      }

      const eContainer = this.$refs.chartContainer
      if (eContainer) {
        // 存在容器&&可以进一步判断数据是否为空
        if (this.dataList.length > 0) {
          this.myChart = echarts.init(eContainer)
          this.myChart.setOption(eOptions, true)
        } else {
          // 没有数据的时候
          this.initInnerHTML(eContainer)
        }
      } else {
        // 容器不存在
      }
    },
    init () {
      this.createChart()
      this.createJyChart()
    },
    initInnerHTML (eContainer) {
      eContainer.innerHTML = `<div class="tips" style='text-align:center'>
           <img src="${this.noDataPic}"
            alt=""
          />
            <p style="line-height:40px;">暂无数据</p>
          </div>
        `
    },
    createJyChart () {
      let _this = this
      this.jyOptions = {
        grid: {
          top: 40,
          bottom: 20,
          left: 50,
          right: 10
        },
        legend: {
          top: 0,
          left: 10,
          icon: 'rect',
          textStyle: {
            padding: [0, 0, -2, 0]
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: this.jyXData,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: 'rgb(190, 200, 200)', // 线的颜色
              type: 'dashed' // 线的类型为虚线
            }
          }
        },
        series: [
          {
            name: '借出数量',
            data: this.jyYData,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            color: 'rgb(235,204,6)',
            symbolSize: 6,
            lineStyle: {
              color: 'rgb(235,204,6)', // 线的颜色
              width: 1, // 线的宽度
              type: 'dashed' // 线的类型为虚线
            }
          }
        ]
      }
      this.initJyChart(this.jyOptions)
    },
    initJyChart (eOptions) {
      if (this.jyMyChart && this.jyMyChart.dispose) {
        // 移去上次渲染的结果，确保本次不受影响
        this.jyMyChart.dispose()
      }

      const eContainer = this.$refs.jyChartContainer
      if (eContainer) {
        // 存在容器&&可以进一步判断数据是否为空
        if (this.dataList.length > 0) {
          this.jyMyChart = echarts.init(eContainer)
          this.jyMyChart.setOption(eOptions, true)
        } else {
          // 没有数据的时候
          this.initInnerHTML(eContainer)
        }
      } else {
        // 容器不存在
      }
    }
  }
}
</script>
<style lang="less" scoped>
.jztj {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .title {
    padding-left: 2%;
    font-weight: 700;
    font-size: 21px;
  }

  .top {
    margin-top: 5px;
    display: flex;
    align-items: center;

    .top-left {
      width: 65%;
      border-bottom: 1px solid rgb(190, 200, 200);
    }

    .top-right {
      flex: 1;
      justify-content: space-evenly;
      display: flex;

      .top-right-item {
        width: 65px;
        height: 24px;
        border-radius: 24px;
        text-align: center;
        line-height: 24px;
        font-size: 14px;
        background-color: rgba(190, 200, 200, 0.7);
        cursor: pointer;
      }

      .top-right-item-active,
      .top-right-item:hover {
        background-color: rgb(45, 140, 240);
        color: #fff;
      }
    }
  }

  .myChartBox {
    width: 100%;
    height: 35%;
    overflow: hidden;
    position: relative;
    left: -40%;
    top: -3%;
  }

  .titles {
    position: relative;
    top: -5%;
  }

  .jyChartBox {
    width: 100%;
    height: 50%;
    position: relative;
    top: -3%;
    overflow: hidden;
  }
}
</style>
