<template>
    <div class="statis">
        <div class="statis-item" v-for="(item, index) in data" :key="index" @click="selectItem(item)">
            <div class="statis-item-title">{{ item.name }}</div>
            <div class="statis-item-value" :class="{ 'statis-item-value-blue': index == 0 }">{{ item.value }}</div>
            <div class="statis-item-zb" v-if="item.zb">{{ item.zb }}</div>
        </div>
    </div>
</template>

<script>

import { mapActions } from "vuex";

export default {
    data() {
        return {
            data: [
                {
                    name: "档案总数",
                    value: "3428",
                },
                {
                    name: "在所人员档案",
                    value: "54323"
                },
                {
                    name: "电子原文总数",
                    value: "141/400",
                    zb: "20%"
                },
                {
                    name: "借阅次数",
                    value: "100"
                }
            ]
        };
    },

    mounted() {

    },
    methods: {
        ...mapActions(["postRequest", "authGetRequest", "authPostRequest"]),
        selectItem(item){
            if(item.name == '档案总数'){
            }
            // this.$router.push({
            //     path: item.path,
            //     query: item.query
            // })
        }

    }
}
</script>


<style lang="less" scoped>
.statis {
    display: flex;
    align-items: center;
    height: 100%;

    .statis-item {
        width: 25%;
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #333;
        cursor: pointer;


        .statis-item-title {
            font-size: 18px;
            font-weight: bold;
        }

        .statis-item-value {
            font-size: 24px;
            font-weight: bold;
            margin: 12px 0;
        }

        .statis-item-zb {
            font-size: 16px;
        }

        .statis-item-value-blue {
            color: #2B5FD9
        }
    }

}
</style>