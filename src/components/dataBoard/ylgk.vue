<template>
  <div class="gk-Flex">
    <!-- <p class="sm-title">医疗概况</p> -->
     <div class="gk-Flex blue-box" style="justify-content: space-around;margin-right: 16px;">
        <img src="@/assets/images/gkIcon.svg" class="gkIcon"  /><p>医 疗<br />概 况</p>
     </div>
     <div v-for="(item,index) in dataList" :key="index" class="dataList-box">
        <p class="num">{{ item.num }}</p>
        <p class="title">{{ item.title }}</p>
     </div>
  </div>
</template>

<script>
export default {
   data(){
    return{
        dataList:[
            {num:'0',title:'今日就诊'},
            {num:'0',title:'本月就诊'},
            {num:'0',title:'本季度就诊'},

        ]
    }
   },
   mounted(){
    this.getData('syjrjzs',0)
    this.getData('sybyjzs',1)
    this.getData('sybjdjzs',2)

   },
   methods:{
    getData(mark,index){
        this.$store.dispatch("authGetRequest", {
          url: this.$path.get_executeMultiQuery,//'/bsp-com/com/form/handle/executeMultiQuery',
          params: { mark: mark },
        }).then(res => {
          if (res.success) {
           this.$set(this.dataList[index],'num',res.data[0].num)
          }
        })
    },
   }

}
</script>

<style>

.blue-box{
    background: #53b2fe;
    color: #fff;
    font-size: 22px;
    font-weight: 700;
    width: 10%;
    border-radius: 6px 0 0 6px;
    height: 100%;
}
.dataList-box{
    width: 10%;
    text-align: center;
    margin-right: 16px;
}
.dataList-box .num{
    font-size: 26px;
    font-weight: 700;
    color: #3b80eb;
}
.gkIcon{
    width: 38px;
    position: relative;
    left: 8px;
}
</style>
