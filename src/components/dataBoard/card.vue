<template>
    <div class="home_top_box">
        <div class="home_top_list">
            <div :class="['home_top_item', 'item_bg' + (index + 1)]" v-for="(item, index) in columns" :key="index"
                @click="linkSetting(item)">
                <div :class="['menu_icon_box', 'menu_icon' + (index + 1)]"></div>
                <div class="text_box">
                    <span class="text_style">{{ item.name }}</span>
                    <div :class="['bottom_border', 'border_color' + (index + 1)]"></div>
                </div>
            </div>
        </div>
    </div>

</template>

<script>

export default {
    data() {
        return {
            columns: [
                {
                    name: '编目组卷',
                    url:'/dms/bmzj/xzaj',
                    code: "dms:bmzj"
                },
                {
                    name: '卷宗管理',
                    url: "/dms/jzgl",
                    code: "dms:jzgl"
                },
                {
                    name: '卷宗借阅',
                },
                {
                    name: '卷宗设置',
                    url:'/dms/dzjz_config',
                    code: "dms:dzjz_config"
                }
            ]
        }
    },
    mounted() {
    },
    methods: {
        linkSetting(row){
            if(row.url){
                if (row.name.indexOf('卷宗管理') != -1) {
				// let address = this.$router.resolve({
				// 	path: row.url,
				// 	query: {
				// 		type: 1
				// 	}
				// })
                //     window.open(address.href)  //, '_blank'
                this.$router.push({
                        path: row.url,
                        query: {
						type: 1
					}
                    })
					
                } else {
                    this.$router.push({
                        path: row.url
                    })
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>
.home_top_box {
    width: 100%;
    height: 100%;
    background-color: #fff;
    border-radius: 4px;

    .home_top_list {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-evenly;

        .home_top_item {
            cursor: pointer;
            position: relative;
            border-radius: 4px;
            width: 22%;
            height: 80%;

            &:last-child {
                margin-right: 0;
            }

            .menu_icon_box {
                position: absolute;
                top: 50%;
                left: 6%;
                transform: translateY(-50%);
                width: 70px;
                height: 70px;
            }

            .menu_icon1 {
                background: url(~@/assets/images/home/<USER>
                background-size: 100%;
                background-position: center;
            }

            .menu_icon2 {
                background: url(~@/assets/images/home/<USER>
                background-size: 100%;
                background-position: center;
            }

            .menu_icon3 {
                background: url(~@/assets/images/home/<USER>
                background-size: 100%;
                background-position: center;
            }

            .menu_icon4 {
                background: url(~@/assets/images/home/<USER>
                background-size: 100%;
                background-position: center;
            }



            .text_box {
                display: flex;
                flex-direction: row-reverse;
                flex-wrap: wrap;
                position: absolute;
                top: 50%;
                right: 6%;
                transform: translateY(-50%);

                .text_style {
                    display: inline-block;
                    width: 100%;
                    text-align: right;
                    font-size: 0.25rem;
                    color: #3d4e66;
                    font-weight: bold;
                }

                .bottom_border {
                    width: 0.8rem;
                    height: 0.08rem;
                    border-radius: 8px;
                    margin-top: 0.1rem;
                }

                .border_color1 {
                    background-color: #66a0ff;
                }

                .border_color2 {
                    background-color: #ff9c31;
                }

                .border_color3 {
                    background-color: #814af8;
                }

                .border_color4 {
                    background-color: #1094fe;
                }

            }
        }

        .item_bg1 {
            background: url(~@/assets/images/home/<USER>
            background-size: 100% 100%;
        }

        .item_bg2 {
            background: url(~@/assets/images/home/<USER>
            background-size: 100% 100%;
        }

        .item_bg3 {
            background: url(~@/assets/images/home/<USER>
            background-size: 100% 100%;
        }

        .item_bg4 {
            background: url(~@/assets/images/home/<USER>
            background-size: 100% 100%;
        }

    }
}
</style>