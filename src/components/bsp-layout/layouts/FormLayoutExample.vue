<template>
  <FormLayout
    :header-config="headerConfig"
    :show-header="true"
    :bottom-actions="bottomActions"
    :show-bottom-actions="true"
    :form-data="formData"
    :layout-mode="layoutMode"
    :loading="loading"
    @header-action="handleHeaderAction"
    @bottom-action="handleBottomAction"
    @update:form-data="updateFormData"
  >
    <!-- 表单内容插槽 -->
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        ref="dynamicForm"
        :config="formConfig"
        :value="formData"
        :mode="formMode"
        :loading="formLoading"
        :disabled="formDisabled"
        @input="updateFormData"
        @submit="handleFormSubmit"
        @validate="handleFormValidate"
      />
    </template>

    <!-- 自定义底部操作按钮 -->
    <template #actions="{ formData, handleAction }">
      <Button
        type="default"
        size="large"
        icon="ios-arrow-back"
        @click="handleGoBack"
      >
        返回
      </Button>
      <Button
        type="default"
        size="large"
        icon="ios-save"
        :loading="saveLoading"
        @click="handleSave"
      >
        保存
      </Button>
      <Button
        type="primary"
        size="large"
        icon="ios-checkmark"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        提交
      </Button>
    </template>

    <!-- 模态框插槽 -->
    <template #modals>
      <Modal
        v-model="showConfirmModal"
        title="确认提交"
        @on-ok="confirmSubmit"
        @on-cancel="cancelSubmit"
      >
        <p>确定要提交表单吗？</p>
      </Modal>
    </template>
  </FormLayout>
</template>

<script>
import FormLayout from './FormLayout.vue'
import DynamicForm from '@/components/dynamic-form/DynamicForm.vue'
import { FORM_MODES } from '@/components/dynamic-form/types'

export default {
  name: 'FormLayoutExample',
  components: {
    FormLayout,
    DynamicForm
  },
  data() {
    return {
      // 表单数据
      formData: {
        name: '',
        email: '',
        phone: '',
        department: '',
        position: '',
        description: ''
      },

      // 表单配置
      formConfig: [
        {
          title: '基本信息',
          fields: [
            {
              key: 'name',
              label: '姓名',
              type: 'input',
              span: 12,
              required: true,
              placeholder: '请输入姓名'
            },
            {
              key: 'email',
              label: '邮箱',
              type: 'input',
              span: 12,
              required: true,
              placeholder: '请输入邮箱地址'
            },
            {
              key: 'phone',
              label: '电话',
              type: 'input',
              span: 12,
              placeholder: '请输入电话号码'
            },
            {
              key: 'department',
              label: '部门',
              type: 'select',
              span: 12,
              options: [
                { label: '技术部', value: 'tech' },
                { label: '产品部', value: 'product' },
                { label: '运营部', value: 'operation' }
              ],
              placeholder: '请选择部门'
            }
          ]
        },
        {
          title: '职位信息',
          fields: [
            {
              key: 'position',
              label: '职位',
              type: 'input',
              span: 24,
              placeholder: '请输入职位'
            },
            {
              key: 'description',
              label: '描述',
              type: 'textarea',
              span: 24,
              placeholder: '请输入描述信息',
              rows: 4
            }
          ]
        }
      ],

      // 头部配置
      headerConfig: {
        title: '员工信息表单',
        icon: 'ios-person-add',
        iconColor: '#5b8ff9',
        actions: [
          {
            name: 'reset',
            label: '重置',
            type: 'default',
            icon: 'ios-refresh'
          }
        ]
      },

      // 底部操作按钮
      bottomActions: [
        {
          name: 'cancel',
          label: '取消',
          type: 'default',
          icon: 'ios-close'
        },
        {
          name: 'save',
          label: '保存',
          type: 'default',
          icon: 'ios-save'
        },
        {
          name: 'submit',
          label: '提交',
          type: 'primary',
          icon: 'ios-checkmark'
        }
      ],

      // 表单模式
      formMode: FORM_MODES.CREATE,

      // 布局模式
      layoutMode: 'default', // default, compact, wide

      // 状态控制
      loading: false,
      formLoading: false,
      formDisabled: false,
      saveLoading: false,
      submitLoading: false,
      showConfirmModal: false
    }
  },

  methods: {
    // 处理头部操作
    handleHeaderAction({ action, formData }) {
      console.log('Header action:', action, formData)

      switch (action.name) {
        case 'reset':
          this.handleReset()
          break
        default:
          break
      }
    },

    // 处理底部操作
    handleBottomAction({ action, formData }) {
      console.log('Bottom action:', action, formData)

      switch (action.name) {
        case 'cancel':
          this.handleGoBack()
          break
        case 'save':
          this.handleSave()
          break
        case 'submit':
          this.handleSubmit()
          break
        default:
          break
      }
    },

    // 更新表单数据
    updateFormData(data) {
      this.formData = { ...this.formData, ...data }
    },

    // 处理表单提交
    handleFormSubmit(data) {
      console.log('Form submit:', data)
      this.handleSubmit()
    },

    // 处理表单验证
    handleFormValidate(valid, errors) {
      console.log('Form validate:', valid, errors)
    },

    // 返回
    handleGoBack() {
      this.$router.go(-1)
    },

    // 重置表单
    handleReset() {
      this.formData = {
        name: '',
        email: '',
        phone: '',
        department: '',
        position: '',
        description: ''
      }
      this.$Message.success('表单已重置')
    },

    // 保存
    async handleSave() {
      try {
        this.saveLoading = true

        // 表单验证
        const valid = await this.$refs.dynamicForm.validate()
        if (!valid) {
          this.$Message.error('请检查表单填写')
          return
        }

        // 模拟保存请求
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.$Message.success('保存成功')
      } catch (error) {
        console.error('Save error:', error)
        this.$Message.error('保存失败')
      } finally {
        this.saveLoading = false
      }
    },

    // 提交
    async handleSubmit() {
      try {
        this.submitLoading = true

        // 表单验证
        const valid = await this.$refs.dynamicForm.validate()
        if (!valid) {
          this.$Message.error('请检查表单填写')
          return
        }

        // 显示确认对话框
        this.showConfirmModal = true
      } catch (error) {
        console.error('Submit error:', error)
        this.$Message.error('提交失败')
      } finally {
        this.submitLoading = false
      }
    },

    // 确认提交
    async confirmSubmit() {
      try {
        this.submitLoading = true

        // 模拟提交请求
        await new Promise(resolve => setTimeout(resolve, 1500))

        this.$Message.success('提交成功')
        this.handleGoBack()
      } catch (error) {
        console.error('Confirm submit error:', error)
        this.$Message.error('提交失败')
      } finally {
        this.submitLoading = false
        this.showConfirmModal = false
      }
    },

    // 取消提交
    cancelSubmit() {
      this.showConfirmModal = false
      this.submitLoading = false
    }
  }
}
</script>

<style lang="less" scoped>
// 可以在这里添加页面特定的样式
</style>
