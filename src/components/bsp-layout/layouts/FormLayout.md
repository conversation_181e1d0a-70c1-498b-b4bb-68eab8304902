# FormLayout 表单布局组件

FormLayout 是一个专门为表单设计的布局组件，通常与 DynamicForm 组件结合使用，提供简洁的表单页面布局。

## 特性

- 🎯 **专为表单设计**：简洁的布局结构，专门优化表单展示
- 📱 **响应式设计**：完美支持移动端和桌面端
- 🎨 **灵活的配置**：支持头部、底部按钮的自定义配置
- 🔧 **多种布局模式**：支持 default、compact、wide 三种布局模式
- 💫 **优雅的动画**：内置淡入动画效果
- 🎪 **插槽支持**：提供丰富的插槽用于自定义内容

## 基本用法

```vue
<template>
  <FormLayout
    :header-config="headerConfig"
    :show-header="true"
    :bottom-actions="bottomActions"
    :form-data="formData"
    @bottom-action="handleBottomAction"
  >
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        :config="formConfig"
        :value="formData"
        @input="updateFormData"
      />
    </template>
  </FormLayout>
</template>
```

## Props

### 基础配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| headerConfig | Object | `{}` | 头部配置 |
| showHeader | Boolean | `false` | 是否显示头部 |
| bottomActions | Array | `[...]` | 底部操作按钮配置 |
| showBottomActions | Boolean | `true` | 是否显示底部操作栏 |
| formData | Object | `{}` | 表单数据 |
| layoutMode | String | `'default'` | 布局模式：default/compact/wide |
| responsive | Boolean | `true` | 是否启用响应式 |
| loading | Boolean | `false` | 是否加载中 |

### headerConfig 配置

```javascript
{
  title: '表单标题',           // 标题文本
  icon: 'ios-person-add',     // 图标类型
  iconSize: 20,               // 图标大小
  iconColor: '#5b8ff9',       // 图标颜色
  actions: [                  // 头部操作按钮
    {
      name: 'reset',
      label: '重置',
      type: 'default',
      icon: 'ios-refresh'
    }
  ]
}
```

### bottomActions 配置

```javascript
[
  {
    name: 'cancel',           // 操作名称
    label: '取消',            // 按钮文本
    type: 'default',          // 按钮类型
    icon: 'ios-close',        // 按钮图标
    loading: false,           // 是否加载中
    disabled: false           // 是否禁用
  },
  {
    name: 'submit',
    label: '提交',
    type: 'primary',
    icon: 'ios-checkmark'
  }
]
```

## 插槽

### form 插槽

表单内容插槽，通常放置 DynamicForm 组件。

```vue
<template #form="{ formData, updateFormData }">
  <DynamicForm
    :config="formConfig"
    :value="formData"
    @input="updateFormData"
  />
</template>
```

**插槽参数：**
- `formData`: 当前表单数据
- `updateFormData`: 更新表单数据的方法

### actions 插槽

底部操作按钮插槽，用于自定义底部按钮。

```vue
<template #actions="{ formData, handleAction }">
  <Button @click="handleCustomAction">自定义按钮</Button>
</template>
```

**插槽参数：**
- `formData`: 当前表单数据
- `handleAction`: 处理操作的方法

### modals 插槽

模态框插槽，用于放置确认对话框等模态组件。

```vue
<template #modals>
  <Modal v-model="showModal" title="确认">
    <p>确定要执行此操作吗？</p>
  </Modal>
</template>
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| header-action | `{ action, formData }` | 头部操作按钮点击 |
| bottom-action | `{ action, formData }` | 底部操作按钮点击 |
| update:form-data | `formData` | 表单数据更新 |
| form-reset | - | 表单重置 |

## 布局模式

### default 模式
标准布局模式，适合大多数场景。

### compact 模式
紧凑布局模式，减少内边距，适合空间有限的场景。

### wide 模式
宽屏布局模式，增加最大宽度，适合内容较多的表单。

## 响应式设计

组件内置响应式设计，在不同屏幕尺寸下自动调整布局：

- **桌面端 (>768px)**：标准布局
- **平板端 (≤768px)**：调整内边距和按钮布局
- **移动端 (≤480px)**：垂直布局，按钮全宽显示

## 完整示例

参考 `FormLayoutExample.vue` 文件查看完整的使用示例。

## 与 DynamicForm 结合使用

FormLayout 专门为与 DynamicForm 组件结合使用而设计：

```vue
<template>
  <FormLayout
    :header-config="{ title: '用户信息', icon: 'ios-person' }"
    :show-header="true"
    :form-data="formData"
    @bottom-action="handleAction"
  >
    <template #form="{ formData, updateFormData }">
      <DynamicForm
        ref="form"
        :config="formConfig"
        :value="formData"
        :mode="FORM_MODES.CREATE"
        @input="updateFormData"
        @submit="handleSubmit"
      />
    </template>
  </FormLayout>
</template>
```

## 样式定制

组件使用 Less 编写样式，支持通过 CSS 变量或覆盖样式类进行定制：

```less
.form-layout {
  // 自定义背景色
  background: #f8f9fa;

  .form-container {
    // 自定义容器样式
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .bottom-actions {
    // 自定义底部按钮样式
    padding: 16px 24px;
  }
}
```

## 注意事项

1. FormLayout 组件会为底部按钮预留空间，表单内容区域会自动添加底部内边距
2. 在移动端，底部按钮会自动调整为垂直布局
3. 建议与 DynamicForm 组件配合使用以获得最佳体验
4. 表单数据通过 props 传递，支持双向绑定
