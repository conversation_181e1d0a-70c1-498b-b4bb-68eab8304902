<template>
  <div class="easy-layout">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧 -->
      <div class="left" :style="{ width: leftWidth }">
        <slot name="left"></slot>
      </div>

      <!-- 右侧 -->
      <div class="right">
        <slot name="right"></slot>
      </div>
    </div>

    <!-- 底部按钮区域 -->
    <div v-if="showBottomActions" class="bottom-actions">
      <div class="actions-container">
        <slot name="actions">
          <Button
            v-for="action in actions"
            :key="action.name"
            :type="action.type || 'default'"
            :size="action.size || 'large'"
            :loading="action.loading"
            :disabled="action.disabled"
            @click="handleAction(action)"
          >
            <Icon v-if="action.icon" :type="action.icon" />
            {{ action.label }}
          </Button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EasyLayout',
  props: {
    leftWidth: {
      type: String,
      default: '300px'
    },
    // 底部按钮配置
    actions: {
      type: Array,
      default: () => []
    }
  },

  computed: {
    showBottomActions() {
      return this.actions.length > 0 || this.$slots.actions
    }
  },

  methods: {
    handleAction(action) {
      this.$emit('action', action)
    }
  }
}
</script>

<style lang="less" scoped>
.easy-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;

    .left {
      flex-shrink: 0;
      background: white;
      border-right: 1px solid #e8eaec;
      overflow-y: auto;
    }

    .right {
      flex: 1;
      background: #f5f7fa;
      overflow-y: auto;
    }
  }

  .bottom-actions {
    background: white;
    border-top: 1px solid #e8eaec;
    padding: 16px 24px;
    flex-shrink: 0;

    .actions-container {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .ivu-btn {
        min-width: 80px;
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .easy-layout {
    .main-content {
      flex-direction: column;

      .left {
        width: 100% !important;
        border-right: none;
        border-bottom: 1px solid #e8eaec;
      }
    }

    .bottom-actions {
      .actions-container {
        justify-content: center;
        flex-wrap: wrap;
      }
    }
  }
}
</style>
