/**
 * 布局配置文件
 * 定义各种布局类型的配置信息
 */

export const layoutConfigs = {
  // 管理页面布局
  management: {
    name: '管理页面布局',
    description: '左侧查询条件，右侧数据表格',
    className: 'bsp-management-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'management'
    },
    slots: [
      {
        name: 'left',
        title: '查询条件区域',
        description: '放置搜索表单和筛选条件',
        defaultWidth: '340px'
      },
      {
        name: 'right',
        title: '数据表格区域',
        description: '放置数据表格和操作按钮',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 选择页面布局
  selection: {
    name: '选择页面布局',
    description: '左侧可选项列表，右侧已选项列表',
    className: 'bsp-selection-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'selection'
    },
    slots: [
      {
        name: 'left',
        title: '可选项列表',
        description: '显示可选择的项目列表',
        defaultWidth: '400px'
      },
      {
        name: 'right',
        title: '已选项列表',
        description: '显示已选择的项目列表',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 详情页面布局
  detail: {
    name: '详情页面布局',
    description: '标准详情页结构：头部、内容、底部',
    className: 'bsp-detail-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'detail'
    },
    slots: [
      {
        name: 'header',
        title: '头部区域',
        description: '标题和操作按钮',
        height: '60px'
      },
      {
        name: 'content',
        title: '内容区域',
        description: '详情内容',
        flex: 1
      },
      {
        name: 'footer',
        title: '底部区域',
        description: '操作按钮',
        height: '60px'
      }
    ],
    responsive: false
  },

  // 搜索页面布局
  search: {
    name: '搜索页面布局',
    description: '顶部搜索条件，下方左侧筛选，右侧结果',
    className: 'bsp-search-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'search'
    },
    slots: [
      {
        name: 'header',
        title: '搜索头部',
        description: '搜索表单和快速筛选',
        height: 'auto'
      },
      {
        name: 'left',
        title: '筛选条件',
        description: '高级筛选条件',
        defaultWidth: '340px'
      },
      {
        name: 'right',
        title: '搜索结果',
        description: '搜索结果列表',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 工作台布局
  dashboard: {
    name: '工作台布局',
    description: '侧边导航，顶部工具栏，主要内容区',
    className: 'bsp-dashboard-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'dashboard'
    },
    slots: [
      {
        name: 'sidebar',
        title: '侧边导航',
        description: '导航菜单',
        defaultWidth: '280px'
      },
      {
        name: 'header',
        title: '顶部工具栏',
        description: '工具栏和统计信息',
        height: 'auto'
      },
      {
        name: 'content',
        title: '主要内容',
        description: '主要内容区域',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 992
  },

  // 卡片网格布局
  'card-grid': {
    name: '卡片网格布局',
    description: '响应式卡片网格展示',
    className: 'bsp-card-grid-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'card-grid'
    },
    slots: [
      {
        name: 'title',
        title: '标题区域',
        description: '页面标题'
      },
      {
        name: 'filters',
        title: '筛选区域',
        description: '筛选条件'
      },
      {
        name: 'grid',
        title: '网格区域',
        description: '卡片网格内容',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 分步表单布局
  wizard: {
    name: '分步表单布局',
    description: '引导式多步骤表单',
    className: 'bsp-wizard-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'wizard'
    },
    slots: [
      {
        name: 'steps',
        title: '步骤条',
        description: '显示当前步骤',
        height: 'auto'
      },
      {
        name: 'content',
        title: '表单内容',
        description: '当前步骤的表单内容',
        flex: 1
      },
      {
        name: 'actions',
        title: '操作按钮',
        description: '上一步、下一步等操作',
        height: '60px'
      }
    ],
    responsive: false
  },

  // 主从表布局
  'master-detail': {
    name: '主从表布局',
    description: '上下分栏，主表和从表关联显示',
    className: 'bsp-master-detail-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'master-detail'
    },
    slots: [
      {
        name: 'master',
        title: '主表',
        description: '主要数据表格',
        height: '40%'
      },
      {
        name: 'detail',
        title: '从表',
        description: '详细信息和关联数据',
        flex: 1
      }
    ],
    responsive: false
  },

  // 聊天界面布局
  chat: {
    name: '聊天界面布局',
    description: '左侧会话列表，右侧聊天主界面',
    className: 'bsp-chat-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'chat'
    },
    slots: [
      {
        name: 'sidebar-header',
        title: '侧边栏头部',
        description: '搜索框等'
      },
      {
        name: 'sidebar-list',
        title: '会话列表',
        description: '会话列表',
        flex: 1
      },
      {
        name: 'main-header',
        title: '聊天头部',
        description: '会话标题和设置'
      },
      {
        name: 'main-content',
        title: '消息内容',
        description: '聊天消息列表',
        flex: 1
      },
      {
        name: 'main-input',
        title: '输入区域',
        description: '消息输入框'
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 嵌套布局
  nested: {
    name: '嵌套布局',
    description: '外层左右，左侧上下分栏',
    className: 'bsp-nested-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'nested'
    },
    slots: [
      {
        name: 'left-top',
        title: '左上区域',
        description: '搜索条件',
        height: 'auto'
      },
      {
        name: 'left-bottom',
        title: '左下区域',
        description: '树形菜单',
        flex: 1
      },
      {
        name: 'right-header',
        title: '右侧头部',
        description: '工具栏',
        height: 'auto'
      },
      {
        name: 'right-content',
        title: '右侧内容',
        description: '主要内容',
        flex: 1
      }
    ],
    responsive: true,
    breakpoint: 768
  },

  // 复杂嵌套布局
  'complex-nested': {
    name: '复杂嵌套布局',
    description: '三层嵌套的复杂布局',
    className: 'bsp-complex-nested-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'complex-nested'
    },
    slots: [
      {
        name: 'left-search',
        title: '左侧搜索',
        description: '高级搜索条件'
      },
      {
        name: 'left-tree',
        title: '左侧树形',
        description: '分类树形菜单',
        flex: 1
      },
      {
        name: 'right-toolbar',
        title: '右侧工具栏',
        description: '统计和操作工具'
      },
      {
        name: 'right-main',
        title: '右侧主表格',
        description: '主数据表格',
        flex: 1
      },
      {
        name: 'right-detail',
        title: '右侧详情',
        description: '详情面板'
      }
    ],
    responsive: true,
    breakpoint: 1200
  },

  // 详情卡片布局
  'detail-card': {
    name: '详情卡片布局',
    description: '左侧固定信息，右侧多卡片展示，底部操作栏',
    className: 'bsp-detail-card-layout',
    component: 'DetailCardLayout',
    props: {
      layoutType: 'detail-card'
    },
    slots: [
      {
        name: 'left',
        title: '左侧信息面板',
        description: '固定宽度的信息展示区域',
        defaultWidth: '400px'
      },
      {
        name: 'basic-info',
        title: '基本信息卡片',
        description: '主要信息展示卡片'
      },
      {
        name: 'detail-info',
        title: '详细信息卡片',
        description: '详细信息展示卡片'
      },
      {
        name: 'actions-card',
        title: '操作卡片',
        description: '操作按钮和功能卡片'
      },
      {
        name: 'bottom-actions',
        title: '底部操作栏',
        description: '固定在底部的操作按钮'
      }
    ],
    responsive: true,
    breakpoint: 1200
  },

  // 自定义布局
  custom: {
    name: '自定义布局',
    description: '完全自定义的布局',
    className: 'bsp-custom-layout',
    component: 'LayoutRenderer',
    props: {
      layoutType: 'custom'
    },
    slots: [],
    responsive: false
  }
}

// 获取布局配置
export function getLayoutConfig(layoutType) {
  return layoutConfigs[layoutType] || layoutConfigs.custom
}

// 获取所有布局类型
export function getLayoutTypes() {
  return Object.keys(layoutConfigs)
}

// 验证布局类型
export function isValidLayoutType(layoutType) {
  return layoutConfigs.hasOwnProperty(layoutType)
}

export default layoutConfigs
