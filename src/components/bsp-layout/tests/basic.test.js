/**
 * BSP 动态布局组件基础测试
 * 验证组件的基本功能和配置
 */

import { layoutConfigs, getLayoutConfig, getLayoutTypes, isValidLayoutType } from '../configs/layoutConfigs.js'
import { componentRegistry } from '../registry/componentRegistry.js'
import { LayoutUtils } from '../index.js'

// 测试布局配置
describe('Layout Configs', () => {
  test('应该包含所有预定义的布局类型', () => {
    const expectedTypes = [
      'management',
      'selection', 
      'detail',
      'search',
      'dashboard',
      'card-grid',
      'wizard',
      'master-detail',
      'chat',
      'nested',
      'complex-nested',
      'custom'
    ]
    
    const actualTypes = getLayoutTypes()
    expectedTypes.forEach(type => {
      expect(actualTypes).toContain(type)
    })
  })
  
  test('应该能正确获取布局配置', () => {
    const managementConfig = getLayoutConfig('management')
    expect(managementConfig).toBeDefined()
    expect(managementConfig.name).toBe('管理页面布局')
    expect(managementConfig.slots).toHaveLength(2)
  })
  
  test('应该能正确验证布局类型', () => {
    expect(isValidLayoutType('management')).toBe(true)
    expect(isValidLayoutType('invalid-type')).toBe(false)
  })
})

// 测试组件注册中心
describe('Component Registry', () => {
  beforeEach(() => {
    componentRegistry.clear()
  })
  
  test('应该能注册和获取组件', async () => {
    const testComponent = { name: 'TestComponent', template: '<div>Test</div>' }
    
    componentRegistry.register('test-component', testComponent)
    
    expect(componentRegistry.hasComponent('test-component')).toBe(true)
    
    const retrievedComponent = await componentRegistry.getComponent('test-component')
    expect(retrievedComponent).toEqual(testComponent)
  })
  
  test('应该能处理异步组件加载', async () => {
    const asyncLoader = () => Promise.resolve({ 
      name: 'AsyncComponent', 
      template: '<div>Async</div>' 
    })
    
    componentRegistry.register('async-component', asyncLoader)
    
    const component = await componentRegistry.getComponent('async-component')
    expect(component.name).toBe('AsyncComponent')
  })
  
  test('应该能处理组件别名', async () => {
    const testComponent = { name: 'TestComponent' }
    
    componentRegistry.register('test-component', testComponent, {
      alias: ['test', 'tc']
    })
    
    expect(componentRegistry.hasComponent('test')).toBe(true)
    expect(componentRegistry.hasComponent('tc')).toBe(true)
    
    const component1 = await componentRegistry.getComponent('test')
    const component2 = await componentRegistry.getComponent('tc')
    
    expect(component1).toEqual(testComponent)
    expect(component2).toEqual(testComponent)
  })
  
  test('应该能获取缓存统计信息', () => {
    componentRegistry.register('comp1', { name: 'Comp1' })
    componentRegistry.register('comp2', { name: 'Comp2' }, { alias: 'c2' })
    
    const stats = componentRegistry.getCacheStats()
    expect(stats.registered).toBe(2)
    expect(stats.aliases).toBe(1)
  })
})

// 测试布局工具类
describe('Layout Utils', () => {
  test('应该能创建布局配置', () => {
    const config = LayoutUtils.createLayoutConfig('management', {
      customClass: 'my-layout',
      slots: [
        { name: 'custom', title: '自定义插槽' }
      ]
    })
    
    expect(config.name).toBe('管理页面布局')
    expect(config.customClass).toBe('my-layout')
    expect(config.slots).toHaveLength(3) // 2个原有 + 1个自定义
  })
  
  test('应该能验证布局配置', () => {
    const validConfig = {
      layoutType: 'management',
      slots: []
    }
    
    const invalidConfig = {
      slots: 'invalid'
    }
    
    const validResult = LayoutUtils.validateLayoutConfig(validConfig)
    const invalidResult = LayoutUtils.validateLayoutConfig(invalidConfig)
    
    expect(validResult.valid).toBe(true)
    expect(validResult.errors).toHaveLength(0)
    
    expect(invalidResult.valid).toBe(false)
    expect(invalidResult.errors.length).toBeGreaterThan(0)
  })
  
  test('应该能生成布局样式类', () => {
    const className = LayoutUtils.generateLayoutClass('management', {
      responsive: true,
      customClass: 'my-class',
      size: 'large'
    })
    
    expect(className).toContain('bsp-management-layout')
    expect(className).toContain('bsp-layout-responsive')
    expect(className).toContain('my-class')
    expect(className).toContain('bsp-layout-large')
  })
  
  test('应该能解析组件配置', () => {
    // 字符串配置
    const stringConfig = LayoutUtils.parseComponentConfig('test-component')
    expect(stringConfig.name).toBe('test-component')
    expect(stringConfig.props).toEqual({})
    
    // 对象配置
    const objectConfig = LayoutUtils.parseComponentConfig({
      name: 'test-component',
      props: { test: true }
    })
    expect(objectConfig.name).toBe('test-component')
    expect(objectConfig.props.test).toBe(true)
    
    // 函数配置
    const functionConfig = LayoutUtils.parseComponentConfig(() => {})
    expect(functionConfig.name).toBe('dynamic')
    expect(functionConfig.loader).toBeInstanceOf(Function)
  })
})

// 测试配置验证
describe('Config Validation', () => {
  test('所有布局配置应该有效', () => {
    Object.keys(layoutConfigs).forEach(layoutType => {
      const config = layoutConfigs[layoutType]
      
      // 检查必需字段
      expect(config.name).toBeDefined()
      expect(config.description).toBeDefined()
      expect(config.className).toBeDefined()
      expect(config.component).toBeDefined()
      
      // 检查插槽配置
      if (config.slots) {
        expect(Array.isArray(config.slots)).toBe(true)
        
        config.slots.forEach(slot => {
          expect(slot.name).toBeDefined()
          expect(slot.title).toBeDefined()
          expect(slot.description).toBeDefined()
        })
      }
    })
  })
  
  test('布局类型应该唯一', () => {
    const types = getLayoutTypes()
    const uniqueTypes = [...new Set(types)]
    
    expect(types.length).toBe(uniqueTypes.length)
  })
})

// 模拟浏览器环境的简单实现
if (typeof global !== 'undefined') {
  global.console = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}

// 导出测试工具函数
export const testUtils = {
  // 创建测试组件
  createTestComponent(name, template = '<div>Test</div>') {
    return {
      name,
      template,
      mounted() {
        console.log(`${name} mounted`)
      }
    }
  },
  
  // 创建测试布局配置
  createTestLayoutConfig(layoutType, overrides = {}) {
    const baseConfig = getLayoutConfig(layoutType)
    return {
      ...baseConfig,
      ...overrides
    }
  },
  
  // 模拟组件事件
  mockComponentEvent(action, data = {}) {
    return {
      event: {
        action,
        data,
        timestamp: Date.now()
      }
    }
  }
}

export default testUtils
