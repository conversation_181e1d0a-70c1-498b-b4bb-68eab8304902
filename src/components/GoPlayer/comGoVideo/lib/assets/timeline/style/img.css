.gs-control-addBookmark:disabled {
    background-image: url(../source/control/addBookmark/disabled.png) !important;
}

.gs-control-addBookmark-disabled {
    background-image: url(../source/control/addBookmark/disabled.png) !important;
}

.gs-control-addBookmark:hover {
    background-image: url(../source/control/addBookmark/hover.png);
}

.gs-control-addBookmark-hover {
    background-image: url(../source/control/addBookmark/hover.png);
}

.gs-control-addBookmark {
    background-image: url(../source/control/addBookmark/normal.png);
}

.gs-control-back:disabled {
    background-image: url(../source/control/back/disabled.png) !important;
}

.gs-control-back-disabled {
    background-image: url(../source/control/back/disabled.png) !important;
}

.gs-control-back:hover {
    background-image: url(../source/control/back/hover.png);
}

.gs-control-back-hover {
    background-image: url(../source/control/back/hover.png);
}

.gs-control-back {
    background-image: url(../source/control/back/normal.png);
}

.gs-control-back10:hover {
    background-image: url(../source/control/back10/hover.png);
}

.gs-control-back10-hover {
    background-image: url(../source/control/back10/hover.png);
}

.gs-control-back10 {
    background-image: url(../source/control/back10/normal.png);
}

.gs-control-back5:hover {
    background-image: url(../source/control/back5/hover.png);
}

.gs-control-back5-hover {
    background-image: url(../source/control/back5/hover.png);
}

.gs-control-back5 {
    background-image: url(../source/control/back5/normal.png);
}

.gs-control-backSelect:hover {
    background-image: url(../source/control/backSelect/hover.png);
}

.gs-control-backSelect-hover {
    background-image: url(../source/control/backSelect/hover.png);
}

.gs-control-backSelect {
    background-image: url(../source/control/backSelect/normal.png);
}

.gs-control-bookmark:disabled {
    background-image: url(../source/control/bookmark/disabled.png) !important;
}

.gs-control-bookmark-disabled {
    background-image: url(../source/control/bookmark/disabled.png) !important;
}

.gs-control-bookmark:hover {
    background-image: url(../source/control/bookmark/hover.png);
}

.gs-control-bookmark-hover {
    background-image: url(../source/control/bookmark/hover.png);
}

.gs-control-bookmark {
    background-image: url(../source/control/bookmark/normal.png);
}

.gs-control-capture:disabled {
    background-image: url(../source/control/capture/disabled.png) !important;
}

.gs-control-capture-disabled {
    background-image: url(../source/control/capture/disabled.png) !important;
}

.gs-control-capture:hover {
    background-image: url(../source/control/capture/hover.png);
}

.gs-control-capture-hover {
    background-image: url(../source/control/capture/hover.png);
}

.gs-control-capture {
    background-image: url(../source/control/capture/normal.png);
}

.gs-control-close:disabled {
    background-image: url(../source/control/close/disabled.png) !important;
}

.gs-control-close-disabled {
    background-image: url(../source/control/close/disabled.png) !important;
}

.gs-control-close:hover {
    background-image: url(../source/control/close/hover.png);
}

.gs-control-close-hover {
    background-image: url(../source/control/close/hover.png);
}

.gs-control-close {
    background-image: url(../source/control/close/normal.png);
}

.gs-control-closeAll:disabled {
    background-image: url(../source/control/closeAll/disabled.png) !important;
}

.gs-control-closeAll-disabled {
    background-image: url(../source/control/closeAll/disabled.png) !important;
}

.gs-control-closeAll:hover {
    background-image: url(../source/control/closeAll/hover.png);
}

.gs-control-closeAll-hover {
    background-image: url(../source/control/closeAll/hover.png);
}

.gs-control-closeAll {
    background-image: url(../source/control/closeAll/normal.png);
}

.gs-control-closeKeepRatio:hover {
    background-image: url(../source/control/closeKeepRatio/hover.png);
}

.gs-control-closeKeepRatio-hover {
    background-image: url(../source/control/closeKeepRatio/hover.png);
}

.gs-control-closeKeepRatio {
    background-image: url(../source/control/closeKeepRatio/normal.png);
}

.gs-control-customSplit:disabled {
    background-image: url(../source/control/customSplit/disabled.png) !important;
}

.gs-control-customSplit-disabled {
    background-image: url(../source/control/customSplit/disabled.png) !important;
}

.gs-control-customSplit:hover {
    background-image: url(../source/control/customSplit/hover.png);
}

.gs-control-customSplit-hover {
    background-image: url(../source/control/customSplit/hover.png);
}

.gs-control-customSplit {
    background-image: url(../source/control/customSplit/normal.png);
}

.gs-control-down:hover {
    background-image: url(../source/control/down/hover.png);
}

.gs-control-down-hover {
    background-image: url(../source/control/down/hover.png);
}

.gs-control-down {
    background-image: url(../source/control/down/normal.png);
}

.gs-control-download:disabled {
    background-image: url(../source/control/download/disabled.png) !important;
}

.gs-control-download-disabled {
    background-image: url(../source/control/download/disabled.png) !important;
}

.gs-control-download:hover {
    background-image: url(../source/control/download/hover.png);
}

.gs-control-download-hover {
    background-image: url(../source/control/download/hover.png);
}

.gs-control-download {
    background-image: url(../source/control/download/normal.png);
}

.gs-control-fast:disabled {
    background-image: url(../source/control/fast/disabled.png) !important;
}

.gs-control-fast-disabled {
    background-image: url(../source/control/fast/disabled.png) !important;
}

.gs-control-fast:hover {
    background-image: url(../source/control/fast/hover.png);
}

.gs-control-fast-hover {
    background-image: url(../source/control/fast/hover.png);
}

.gs-control-fast {
    background-image: url(../source/control/fast/normal.png);
}

.gs-control-frameNext:disabled {
    background-image: url(../source/control/frameNext/disabled.png) !important;
}

.gs-control-frameNext-disabled {
    background-image: url(../source/control/frameNext/disabled.png) !important;
}

.gs-control-frameNext:hover {
    background-image: url(../source/control/frameNext/hover.png);
}

.gs-control-frameNext-hover {
    background-image: url(../source/control/frameNext/hover.png);
}

.gs-control-frameNext {
    background-image: url(../source/control/frameNext/normal.png);
}

.gs-control-framePrevious:disabled {
    background-image: url(../source/control/framePrevious/disabled.png) !important;
}

.gs-control-framePrevious-disabled {
    background-image: url(../source/control/framePrevious/disabled.png) !important;
}

.gs-control-framePrevious:hover {
    background-image: url(../source/control/framePrevious/hover.png);
}

.gs-control-framePrevious-hover {
    background-image: url(../source/control/framePrevious/hover.png);
}

.gs-control-framePrevious {
    background-image: url(../source/control/framePrevious/normal.png);
}

.gs-control-fullScreen:disabled {
    background-image: url(../source/control/fullScreen/disabled.png) !important;
}

.gs-control-fullScreen-disabled {
    background-image: url(../source/control/fullScreen/disabled.png) !important;
}

.gs-control-fullScreen:hover {
    background-image: url(../source/control/fullScreen/hover.png);
}

.gs-control-fullScreen-hover {
    background-image: url(../source/control/fullScreen/hover.png);
}

.gs-control-fullScreen {
    background-image: url(../source/control/fullScreen/normal.png);
}

.gs-control-lock:disabled {
    background-image: url(../source/control/lock/disabled.png) !important;
}

.gs-control-lock-disabled {
    background-image: url(../source/control/lock/disabled.png) !important;
}

.gs-control-lock:hover {
    background-image: url(../source/control/lock/hover.png);
}

.gs-control-lock-hover {
    background-image: url(../source/control/lock/hover.png);
}

.gs-control-lock {
    background-image: url(../source/control/lock/normal.png);
}

.gs-control-more:disabled {
    background-image: url(../source/control/more/disabled.png) !important;
}

.gs-control-more-disabled {
    background-image: url(../source/control/more/disabled.png) !important;
}

.gs-control-more:hover {
    background-image: url(../source/control/more/hover.png);
}

.gs-control-more-hover {
    background-image: url(../source/control/more/hover.png);
}

.gs-control-more {
    background-image: url(../source/control/more/normal.png);
}

.gs-control-mute:disabled {
    background-image: url(../source/control/mute/disabled.png) !important;
}

.gs-control-mute-disabled {
    background-image: url(../source/control/mute/disabled.png) !important;
}

.gs-control-mute:hover {
    background-image: url(../source/control/mute/hover.png);
}

.gs-control-mute-hover {
    background-image: url(../source/control/mute/hover.png);
}

.gs-control-mute {
    background-image: url(../source/control/mute/normal.png);
}

.gs-control-next:disabled {
    background-image: url(../source/control/next/disabled.png) !important;
}

.gs-control-next-disabled {
    background-image: url(../source/control/next/disabled.png) !important;
}

.gs-control-next:hover {
    background-image: url(../source/control/next/hover.png);
}

.gs-control-next-hover {
    background-image: url(../source/control/next/hover.png);
}

.gs-control-next {
    background-image: url(../source/control/next/normal.png);
}

.gs-control-next10:hover {
    background-image: url(../source/control/next10/hover.png);
}

.gs-control-next10-hover {
    background-image: url(../source/control/next10/hover.png);
}

.gs-control-next10 {
    background-image: url(../source/control/next10/normal.png);
}

.gs-control-next5:hover {
    background-image: url(../source/control/next5/hover.png);
}

.gs-control-next5-hover {
    background-image: url(../source/control/next5/hover.png);
}

.gs-control-next5 {
    background-image: url(../source/control/next5/normal.png);
}

.gs-control-nextSelect:hover {
    background-image: url(../source/control/nextSelect/hover.png);
}

.gs-control-nextSelect-hover {
    background-image: url(../source/control/nextSelect/hover.png);
}

.gs-control-nextSelect {
    background-image: url(../source/control/nextSelect/normal.png);
}

.gs-control-openKeepRatio:hover {
    background-image: url(../source/control/openKeepRatio/hover.png);
}

.gs-control-openKeepRatio-hover {
    background-image: url(../source/control/openKeepRatio/hover.png);
}

.gs-control-openKeepRatio {
    background-image: url(../source/control/openKeepRatio/normal.png);
}

.gs-control-pause:disabled {
    background-image: url(../source/control/pause/disabled.png) !important;
}

.gs-control-pause-disabled {
    background-image: url(../source/control/pause/disabled.png) !important;
}

.gs-control-pause:hover {
    background-image: url(../source/control/pause/hover.png);
}

.gs-control-pause-hover {
    background-image: url(../source/control/pause/hover.png);
}

.gs-control-pause {
    background-image: url(../source/control/pause/normal.png);
}

.gs-control-play:disabled {
    background-image: url(../source/control/play/disabled.png) !important;
}

.gs-control-play-disabled {
    background-image: url(../source/control/play/disabled.png) !important;
}

.gs-control-play:hover {
    background-image: url(../source/control/play/hover.png);
}

.gs-control-play-hover {
    background-image: url(../source/control/play/hover.png);
}

.gs-control-play {
    background-image: url(../source/control/play/normal.png);
}

.gs-control-recordingLockList:disabled {
    background-image: url(../source/control/recordingLockList/disabled.png) !important;
}

.gs-control-recordingLockList-disabled {
    background-image: url(../source/control/recordingLockList/disabled.png) !important;
}

.gs-control-recordingLockList:hover {
    background-image: url(../source/control/recordingLockList/hover.png);
}

.gs-control-recordingLockList-hover {
    background-image: url(../source/control/recordingLockList/hover.png);
}

.gs-control-recordingLockList {
    background-image: url(../source/control/recordingLockList/normal.png);
}

.gs-control-recordSearch:disabled {
    background-image: url(../source/control/recordSearch/disabled.png) !important;
}

.gs-control-recordSearch-disabled {
    background-image: url(../source/control/recordSearch/disabled.png) !important;
}

.gs-control-recordSearch:hover {
    background-image: url(../source/control/recordSearch/hover.png);
}

.gs-control-recordSearch-hover {
    background-image: url(../source/control/recordSearch/hover.png);
}

.gs-control-recordSearch {
    background-image: url(../source/control/recordSearch/normal.png);
}

.gs-control-reset:disabled {
    background-image: url(../source/control/reset/disabled.png) !important;
}

.gs-control-reset-disabled {
    background-image: url(../source/control/reset/disabled.png) !important;
}

.gs-control-reset:hover {
    background-image: url(../source/control/reset/hover.png);
}

.gs-control-reset-hover {
    background-image: url(../source/control/reset/hover.png);
}

.gs-control-reset {
    background-image: url(../source/control/reset/normal.png);
}

.gs-control-seekConfirm:hover {
    background-image: url(../source/control/seekConfirm/hover.png);
}

.gs-control-seekConfirm-hover {
    background-image: url(../source/control/seekConfirm/hover.png);
}

.gs-control-seekConfirm {
    background-image: url(../source/control/seekConfirm/normal.png);
}

.gs-control-segmentedPlay:disabled {
    background-image: url(../source/control/segmentedPlay/disabled.png) !important;
}

.gs-control-segmentedPlay-disabled {
    background-image: url(../source/control/segmentedPlay/disabled.png) !important;
}

.gs-control-segmentedPlay:hover {
    background-image: url(../source/control/segmentedPlay/hover.png);
}

.gs-control-segmentedPlay-hover {
    background-image: url(../source/control/segmentedPlay/hover.png);
}

.gs-control-segmentedPlay {
    background-image: url(../source/control/segmentedPlay/normal.png);
}

.gs-control-selectRecord:disabled {
    background-image: url(../source/control/selectRecord/disabled.png) !important;
}

.gs-control-selectRecord-disabled {
    background-image: url(../source/control/selectRecord/disabled.png) !important;
}

.gs-control-selectRecord:hover {
    background-image: url(../source/control/selectRecord/hover.png);
}

.gs-control-selectRecord-hover {
    background-image: url(../source/control/selectRecord/hover.png);
}

.gs-control-selectRecord {
    background-image: url(../source/control/selectRecord/normal.png);
}

.gs-control-showSeek:disabled {
    background-image: url(../source/control/showSeek/disabled.png) !important;
}

.gs-control-showSeek-disabled {
    background-image: url(../source/control/showSeek/disabled.png) !important;
}

.gs-control-showSeek:hover {
    background-image: url(../source/control/showSeek/hover.png);
}

.gs-control-showSeek-hover {
    background-image: url(../source/control/showSeek/hover.png);
}

.gs-control-showSeek {
    background-image: url(../source/control/showSeek/normal.png);
}

.gs-control-slow:disabled {
    background-image: url(../source/control/slow/disabled.png) !important;
}

.gs-control-slow-disabled {
    background-image: url(../source/control/slow/disabled.png) !important;
}

.gs-control-slow:hover {
    background-image: url(../source/control/slow/hover.png);
}

.gs-control-slow-hover {
    background-image: url(../source/control/slow/hover.png);
}

.gs-control-slow {
    background-image: url(../source/control/slow/normal.png);
}

.gs-control-split-1:disabled {
    background-image: url(../source/control/split-1/disabled.png) !important;
}

.gs-control-split-1-disabled {
    background-image: url(../source/control/split-1/disabled.png) !important;
}

.gs-control-split-1:hover {
    background-image: url(../source/control/split-1/hover.png);
}

.gs-control-split-1-hover {
    background-image: url(../source/control/split-1/hover.png);
}

.gs-control-split-1 {
    background-image: url(../source/control/split-1/normal.png);
}

.gs-control-split-16:disabled {
    background-image: url(../source/control/split-16/disabled.png) !important;
}

.gs-control-split-16-disabled {
    background-image: url(../source/control/split-16/disabled.png) !important;
}

.gs-control-split-16:hover {
    background-image: url(../source/control/split-16/hover.png);
}

.gs-control-split-16-hover {
    background-image: url(../source/control/split-16/hover.png);
}

.gs-control-split-16 {
    background-image: url(../source/control/split-16/normal.png);
}

.gs-control-split-4:disabled {
    background-image: url(../source/control/split-4/disabled.png) !important;
}

.gs-control-split-4-disabled {
    background-image: url(../source/control/split-4/disabled.png) !important;
}

.gs-control-split-4:hover {
    background-image: url(../source/control/split-4/hover.png);
}

.gs-control-split-4-hover {
    background-image: url(../source/control/split-4/hover.png);
}

.gs-control-split-4 {
    background-image: url(../source/control/split-4/normal.png);
}

.gs-control-split-9:disabled {
    background-image: url(../source/control/split-9/disabled.png) !important;
}

.gs-control-split-9-disabled {
    background-image: url(../source/control/split-9/disabled.png) !important;
}

.gs-control-split-9:hover {
    background-image: url(../source/control/split-9/hover.png);
}

.gs-control-split-9-hover {
    background-image: url(../source/control/split-9/hover.png);
}

.gs-control-split-9 {
    background-image: url(../source/control/split-9/normal.png);
}

.gs-control-stop:disabled {
    background-image: url(../source/control/stop/disabled.png) !important;
}

.gs-control-stop-disabled {
    background-image: url(../source/control/stop/disabled.png) !important;
}

.gs-control-stop:hover {
    background-image: url(../source/control/stop/hover.png);
}

.gs-control-stop-hover {
    background-image: url(../source/control/stop/hover.png);
}

.gs-control-stop {
    background-image: url(../source/control/stop/normal.png);
}

.gs-control-synchronous:disabled {
    background-image: url(../source/control/synchronous/disabled.png) !important;
}

.gs-control-synchronous-disabled {
    background-image: url(../source/control/synchronous/disabled.png) !important;
}

.gs-control-synchronous:hover {
    background-image: url(../source/control/synchronous/hover.png);
}

.gs-control-synchronous-hover {
    background-image: url(../source/control/synchronous/hover.png);
}

.gs-control-synchronous {
    background-image: url(../source/control/synchronous/normal.png);
}

.gs-control-timeRangeAdd {
    background-image: url(../source/control/timeRangeAdd/normal.png);
}

.gs-control-timeRangeReduce {
    background-image: url(../source/control/timeRangeReduce/normal.png);
}

.gs-control-unMute:hover {
    background-image: url(../source/control/unMute/hover.png);
}

.gs-control-unMute-hover {
    background-image: url(../source/control/unMute/hover.png);
}

.gs-control-unMute {
    background-image: url(../source/control/unMute/normal.png);
}

.gs-control-up:hover {
    background-image: url(../source/control/up/hover.png);
}

.gs-control-up-hover {
    background-image: url(../source/control/up/hover.png);
}

.gs-control-up {
    background-image: url(../source/control/up/normal.png);
}

.gs-control-volume:hover {
    background-image: url(../source/control/volume/hover.png);
}

.gs-control-volume-hover {
    background-image: url(../source/control/volume/hover.png);
}

.gs-control-volume {
    background-image: url(../source/control/volume/normal.png);
}

.gs-control-volumeMute:disabled {
    background-image: url(../source/control/volumeMute/disabled.png) !important;
}

.gs-control-volumeMute-disabled {
    background-image: url(../source/control/volumeMute/disabled.png) !important;
}

.gs-control-volumeMute:hover {
    background-image: url(../source/control/volumeMute/hover.png);
}

.gs-control-volumeMute-hover {
    background-image: url(../source/control/volumeMute/hover.png);
}

.gs-control-volumeMute {
    background-image: url(../source/control/volumeMute/normal.png);
}

.gs-icon-darkLock {
    background-image: url(../source/icon/darkLock.svg) !important;
}

.gs-icon-label {
    background-image: url(../source/icon/label.png) !important;
}

.gs-icon-left {
    background-image: url(../source/icon/left.png) !important;
}

.gs-icon-lightLock {
    background-image: url(../source/icon/lightLock.svg) !important;
}

.gs-icon-right {
    background-image: url(../source/icon/right.png) !important;
}

.gs-icon-scrollDown {
    background-image: url(../source/icon/scrollDown.png) !important;
}

.gs-icon-scrollUp {
    background-image: url(../source/icon/scrollUp.png) !important;
}

.gs-icon-select {
    background-image: url(../source/icon/select.png) !important;
}

.gs-icon-synchronous {
    background-image: url(../source/icon/synchronous.png) !important;
}

.gs-icon-timelineLock {
    background-image: url(../source/icon/timelineLock.svg) !important;
}

