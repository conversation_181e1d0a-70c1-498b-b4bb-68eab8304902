<template>
    <Modal
        width="600"
        class="imsProgressModalWrap ims"
        v-model="showBatchPrintModal"
        :footer-hide="true"
        :closable="true"
        :mask-closable="true"
        title="已选择文书">
        <div class="batchPrintBox">
            <div class="ListOfDocuments">
                <ul class="docInforList">
                    <li v-for="item in DocInformationList" :key="item.id+'DIL'" :title="item.name">
                        <span>{{item.name}}</span>
                        <i class="deleteBtn" @click="docInforDelAction(item)"></i>
                    </li>
                </ul>
            </div>
            <ul class="operateBox">
                <!-- <li @click="clearAll">重置</li> -->
                <li @click="closeBatchPrintBox">关闭</li>
                <li @click="startPrintAction(true)">打印</li>
            </ul>
            <PrintView v-if="showBatchPrintModal" ref="printView" :AllImgList="AllImgList"/>
        </div>
    </Modal>
</template>
<script>
import PrintView from './printView.vue'
import Bus from '@/libs/eventBus.js'
export default {
    components:{
        PrintView,
    },
    props:{
        jzTreeObj:{
            type: Object,
            default: ()=>{}
        },
        newDocDataAll_ybm:{
            type: Array,
            default: ()=>[]
        }
    },
    data() {
        return {
            DocInformationList: [],
            AllImgList: [],
            showBatchPrintModal: false
        }
    },
    created(){
        Bus.$on('batchPrintAction',(val)=>{
            var list = []
            val.forEach(item=>{
                if(!item.isParent){
                    list.push(item)
                }
            })
            this.DocInformationList = list
        })
    },
    methods:{
        docInforDelAction(item){
            this.$parent.BatchPrintAction(3,item)
            this.DocInformationList.forEach((item2,index)=>{
                if(item2.id == item.id){
                    this.DocInformationList.splice(index,1)
                }
            })
        },
        startPrintAction(val){
            this.AllImgList = []
            if(val){
                if(this.newDocDataAll_ybm.length > 0 && this.DocInformationList.length > 0){
                    this.newDocDataAll_ybm.forEach(item=>{
                        this.DocInformationList.forEach(item2=>{
                            if(item2.catalogId && item.catalogId == item2.catalogId){
                                this.AllImgList.push(...item.materialFile)
                            }
                        })
                    })
                    this.$refs.printView.doPrint(this.AllImgList)
                }else{
                    this.$Message.warning('请选择勾选目录！');
                }
            }else{
                if(this.newDocDataAll_ybm.length > 0 && this.DocInformationList.length > 0){
                    this.showBatchPrintModal = true;
                    this.AllImgList = [];
                }else{
                    this.$Message.warning('请选择勾选目录！');
                }
            }
        },
        clearAll(){
            this.$parent.BatchPrintAction(2)
            this.DocInformationList = [];
        },
        closeBatchPrintBox(){
            this.DocInformationList = [];
            this.showBatchPrintModal = false;
            this.$parent.BatchPrintAction(1)
        }
    },
}
</script>
<style lang="less" scoped>
@assets: '../../assets';
.batchPrintBox{
    width: 5.9rem;
    min-height: 2rem;
    max-height: 5rem;
    overflow: auto;
    h3{
        font-size: 0.2rem;
        font-weight: bold;
        line-height: 0.4rem;
        padding-left: 0.1rem;
    }
    .ListOfDocuments{
        width: 100%;
        min-height: 1.4rem;
        max-height: 4.4rem;
        padding: 0.1rem;
        box-sizing: border-box;
        overflow: auto;
        .docInforList{
            display: flex;
            flex-wrap: wrap;
            li{
                width: calc(~'33% - 0.2rem');
                display: flex;
                align-items: center;
                margin: 0 0.1rem;
                span{
                    display: inline-block;
                    max-width: calc(~'100% - 0.4rem');
                    line-height: 0.35rem;
                    text-align: left;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .deleteBtn{
                    display: inline-block;
                    width: 0.2rem;
                    height: 0.2rem;
                    background: url('@{assets}/images/dzjz/icon_dlt2.png') no-repeat;
                    background-size: 100% 100%;
                    margin-left: 0.05rem;
                    cursor: pointer;
                }
                .deleteBtn:hover{
                    opacity: 0.7;
                }
            }
        }
    }
    .operateBox{
        width: 100%;
        height: 0.6rem;
        display: flex;
        justify-content: center;
        align-items: center;
        li{
            width: 0.8rem;
            text-align: center;
            line-height: 0.32rem;
            border: solid 1px #087EFF;
            color: #087EFF;
            margin: 0 0.05rem;
            border-radius: 0.04rem;
            width: 80px;
            background: #FFFFFF;
            border-radius: 2px;
            cursor: pointer;
        }
        li:hover{
            opacity: 0.8;
        }
        li:last-child{
            background: #087EFF;
            color: #fff;
        }
    }
}
</style>
