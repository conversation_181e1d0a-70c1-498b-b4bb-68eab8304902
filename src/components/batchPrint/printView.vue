<!--
 * @Author: hanjinxiang && <EMAIL>
 * @Date: 2022-09-21 09:49:07
 * @LastEditors: hanjinxiang && <EMAIL>
 * @LastEditTime: 2022-10-21 15:07:49
 * @FilePath: \ims-admin-web\src\components\batchPrint\printView.vue
 * @Description: 
-->
<template>
    <div class="printViewBox" v-show="false">
        <img style="width: 750px;height: 1071px;" v-for="(item,index) in AllImgList" :key="index+'PV'" :src="changeUrl(item.url)"/>
    </div>
</template>
<script>
export default {
    data() {
        return {
            AllImgList: []
        }
    },
    mounted(){
       
    },
    methods:{
        changeUrl(originalUrl){
            if(originalUrl){
				return originalUrl.indexOf(serverConfig.minioHttp)>-1?originalUrl:serverConfig.minioHttp+serverConfig.APP_CODE+'/'+originalUrl //.replace(serverConfig.uploadUrl, serverConfig.downloadUrl);
            }
        },
        doPrint(list){
            //判断iframe是否存在，不存在则创建iframe
            this.AllImgList = list;
            this.$forceUpdate();
            this.$nextTick(()=>{
                var iframe=document.getElementById("print-iframe");
                if(!iframe){  
                    var el = document.getElementsByClassName("printViewBox")[0];
                    iframe = document.createElement('iframe');
                    var doc = null;
                    iframe.setAttribute("id", "print-iframe");
                    iframe.setAttribute('style', 'position:absolute;width:0px;height:0px;left:-500px;top:-500px;');
                    document.body.appendChild(iframe);
                    doc = iframe.contentWindow.document;
                    //这里可以自定义样式
                    doc.write('<style media="print">@page {size: auto;margin: 0mm;}</style>'); //解决出现页眉页脚和路径的问题
                    doc.write('<div>' + el.innerHTML + '</div>');
                    doc.close();
                    iframe.contentWindow.focus();            
                }
                setTimeout(()=>{ 
                    iframe.contentWindow.print();
                    document.body.removeChild(iframe);
                },50)  //解决第一次样式不生效的问题
            })
        }
    },
}
</script>
<style lang="less" scoped>
.printViewBox{
    img{
        width: 7.5rem;
    }
    
}
</style>
