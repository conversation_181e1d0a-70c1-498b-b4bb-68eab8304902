<template>
    <div>
    <Modal
        width="560"
        class-name="imsProgressModalWrap ims"
        v-model="showModal"
        title="智能格式化审查"
        :mask-closable="false">
        <!-- <template #header>
            <div class="box_header">
                <div class="header_title">智能格式化审查</div>
                <div class="header_title" @click="closeBtn"><Icon type="md-close" /></div>
            </div>
        </template> -->
        <div class="InspectionEventBox">
            <el-checkbox style="margin-bottom: 10px" :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
            <div class="DetectionItem" v-for="(item,index) in testList" :key="index">
                <label>{{item.name}}:</label>
                <div :class="['checkBoxItem',{active: item2.check}]" v-for="(item2, index2) in item.list" :key="item2.name" @click="checkAction(index, index2, item)">{{item2.name}}<i class="check"></i></div>
            </div>
            <div class="tips">
                <p>{{'提示：' + (checkType == 0 ? tipText : tipText2)}}</p>
            </div>
        </div>
        <template #footer>
            <ul class="operateBox">
                <li @click="closeBtn('close')">取消</li>
                <li @click="confirmBtn">确定</li>
            </ul>
        </template>
    </Modal>
    
    
    <Modal v-model="waitModal" :mask-closable="false" title="提示" class-name="waitBox">
        <div class="wait-content">
            <img src="@/assets/images/dzjz/bmzj/warning_icon.png" alt="">
            <span>正在识别中，请稍后！</span>
        </div>
        <template #footer>
            <div class="footer-btn-box">
                <my-btn plain size="small" @click="waitModal = false" style='margin-right:16px;'>取消</my-btn>
                <my-btn size="small" @click="waitModal = false">确定</my-btn>
            </div>
        </template>
    </Modal>

    <Modal v-model="finishModal" :mask-closable="false" title="提示" class-name="waitBox">
        <div class="wait-content">
            <img src="@/assets/images/dzjz/bmzj/warning_icon.png" alt="">
            <span>智能审查完成！</span>
        </div>
        <template #footer>
            <div class="footer-box">
                <Button @click="finishModal = false">确定</Button>
            </div>
        </template>
    </Modal>


    </div>
</template>
<script>
export default {
    props: {
        
        ajbh: {
            type: String,
            default: ''
        },
        jgrybm: {
            type: String,
            default: ''
        },
        ajmc: {
            type: String,
            default: ''
        },
        //检测类型 0：未编目  1：已编目  2：智能阅卷
        checkType: {
            type: String,
            default: '0',
            require: true
        }
    },
    data() {
        return {
            checkAll: true,
            isIndeterminate: false,
            showModal: false,
            waitModal: false,
            finishModal: false,
            testResult: [],
            requireFlag: false,
            socket: null,
            testList: [
                {
                    name: '质量检测',
                    list: [
                        {
                            name: '空白页检测',
                            type: 'blankSpace',
                            check: true
                        },
                        {
                            name: '重复页检测',
                            type: 'repeatPage',
                            check: true
                        },
                        {
                            name: '瑕疵页检测',
                            type: 'flaw',
                            check: true
                        }
                    ],

                },
                {
                    name: '规范检测',
                    list: [
                        {
                            name: '文书填写规范检测',
                            type: 'infoExtract',
                            check: true
                        },
                    ],
                },
                {
                    name: '格式检测',
                    list: [
                        {
                            name: '签章检测',
                            type: 'seal',
                            check: true
                        },
                        {
                            name: '签名检测',
                            type: 'signature',
                            check: true
                        },
                        {
                            name: '捺印检测',
                            type: 'fingerprint',
                            check: true
                        },
                    ],
                },
            ],
            checking: false, //是否处于审查状态
            tipText: '材料检测无误后将自动编目。',
            tipText2: '材料检测无误后将更新检测结果'
        }
    },
    created(){
        
    },
    mounted() {
    },
    methods:{
        handleCheckAllChange(val){
            this.isIndeterminate = !this.isIndeterminate;
            if(this.checkAll){
                this.testList.forEach(item=>{
                    item.list.forEach(ele=>{
                        ele.check=true
                    })
                })
            }else{
                this.testList.forEach(item=>{
                    item.list.forEach(ele=>{
                        ele.check=false
                    })
                })
            }
        },
        // 打开弹窗
        openModal() {
            // this.openSocket();
            this.getCheckMsg('reset');
            this.showModal = true;
        },
        //选中审查类型
        checkAction(index,index2,item){
            this.testList[index].list[index2].check = !this.testList[index].list[index2].check;
            let arr=[]
            this.testList.forEach(item=>{
                    item.list.forEach(ele=>{
                        arr.push(ele.check)
                    })
                })
                if(arr.indexOf(false)>-1){
                    this.checkAll=false
                    this.isIndeterminate=true
                }else{
                    this.checkAll=true
                    this.isIndeterminate=false
                }

        },
        //获取选中类型
        getCheckMsg(type){
            var obj = {}, array = []
            this.testList.forEach(item=>{
                item.list.forEach(item2=>{
                    if(type == 'obj'){
                        if(item2.check){
                            this.requireFlag = true
                        }
                        obj[item2.type] = item2.check ? '1' : '0'
                    }else if(type == 'array'){
                        if(item2.check){
                            let obj = {
                                type: item2.type,
                                title: this.ajmc+'-'+this.ajbh,
                                progress: 0,
                            }
                            array.push(obj)
                        }
                    }else if(type == 'reset'){
                        item2.check = true
                    }
                })
            })
            if(type == 'obj'){
                return obj
            }else if(type == 'array'){
                return array
            }
        },
        //发起检测请求
        confirmBtn(){
            // if(!this.socket) {
            //     this.$Message.warning('websocket连接失败');
            //     return ;
            // }
            let obj = this.getCheckMsg('obj')
            if(this.requireFlag){
                let params = {
                    jgrybm: this.jgrybm,
                    checkType: this.checkType
                }
                params = Object.assign(params, obj)
                this.$Post_RP(this.$path.startFormatCheck,params).then(res=>{ 
                    if(res.success){
                        this.checking = true;
                        this.showModal = false;
                        this.waitModal = true;
                        this.$emit('startExamine', true);
                    }else{
                        this.$Message.warning(res.msg);
                    }
                }).catch(err => {
                    this.$Message.warning(err);
                })
            }else{
                this.$Message.warning('请选择要检测的内容！');
            }
        },
        //关闭弹窗
        closeBtn(){ 
            // if(this.socket) {
            //     this.socket.close();
            // }
            this.getCheckMsg('reset');
            this.showModal = false;
        },
        //通过websocket获取检测进度
        openSocket() {
            if (!'WebSocket' in window) {
                this.$Message.warning('您的浏览器不支持WebSocket');
            } else {
                //实现化WebSocket对象，指定要连接的服务器地址与端口  建立连接
                if(!this.socket) {
                    var usreInfo = JSON.parse(localStorage.getItem('usreInfo'));
                    var sid = usreInfo.loginId + "-" + usreInfo.name;
                    var socketUrl = ImsServerConfig.websocketUrl + "/webSocket/" + sid + "?access_token=" + usreInfo.access_token;
                    this.socket = new WebSocket(socketUrl);
                    //打开事件
                    this.socket.onopen =  () =>{
                        console.log("websocket已连接(格式化审查)");
                    };
                    //获得消息事件
                    this.socket.onmessage = (msg)=> {
                        console.log("websocket获得data123:",data);
                        var data =  JSON.parse(msg.data);
                        if( data.code == '3500' && data.ajbh == this.ajbh ){
                            console.log("websocket获得data:",data);
                            if((data.catalogFinish == 1 && this.checkType == '0') || (data.status == 1 && this.checkType == '1')) {
                                this.waitModal = false;
                                this.finishModal = true;
                                this.checking = false;
                                this.$emit('eventOver');
                            }
                        }
                    };
                    //关闭事件
                    this.socket.onclose = ()=> {
                        console.log("websocket已关闭(格式化审查)");
                    };
                    //发生了错误事件
                    this.socket.onerror = function () {
                        if(this.socket)
                           this.socket.close();
                        console.log("websocket发生了错误(格式化审查)");
                    }
                }
            }
        },
    },
    beforeDestroy() {
        if(this.socket) 
            this.socket.close();
    }
}
</script>
<style lang="less" scoped>
    @images: '@/assets/images';
    .InspectionEventBox{
        min-height: 2rem;
        box-sizing: border-box;
        .DetectionItem{
            padding: 0.18rem 0.31rem 0.18rem 0.23rem;
            margin-bottom: 0.11rem;
            display: flex;
            align-items: center;
            background: #FAFBFF;
            label{
                font-size: 0.16rem;
                font-weight: bold;
                margin-right: 0.25rem;
                font-family: "Source Han Sans CN-Regular";
            }
            .checkBoxItem{
                padding: 0 0.18rem 0 0.1rem;
                height: 0.32rem;
                line-height: 0.32rem;
                border-radius: 2px;
                background: #fff;
                border: 1px solid #CEE0F0;
                margin-right: 0.2rem;
                cursor: pointer;
                position: relative;
                font-family: "Source Han Sans CN-Regular";
                &.active{
                    background: #DFF2FF;
                    border: 1px solid #309BFF;
                    color: #309BFF;
                    .check{
                        display: inline-block;
                        width: 0.23rem;
                        height: 0.17rem;
                        background: url("~@{images}/check.png") no-repeat;    
                        background-size: 100% 100%;
                        position: absolute;
                        top: -1px;
                        right: -1px;
                    }
                }
            }
            .checkBoxItem:last-child{
                margin-right: 0;
            }
        }
        .tips{
            color: #7A8699;
            padding-left: 0.23rem;
            margin-bottom: 0.17rem;
        }
    }
    .waitBox {
        .wait-content {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100px;
            margin-top: 15px;
            img {
               width: 30px;
               height: 30px;
               margin-right: 10px;
            }
            span {
               font-size: 0.16rem;
               font-family: Source Han Sans CN;
               font-weight: 400;
               color: #2B3646;
            }
        }
    }   
    .operateBox {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #F7FAFF;
        border-radius: 0 0 0.04rem 0.04rem;
        // border-top: solid 1px #CEE0F0;
        li {
            width: 0.6rem;
            height: 0.3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.06rem;
            border-radius: 4px;
            cursor: pointer;
        }
        li:first-child {
            border: 1px solid #087EFF;
            color: #087EFF;
            background-color: #FFFFFF;
        }
        li:last-child {
            color: #FFFFFF;
            background-color: #087EFF;
        }
    }

    .footer-box{
        button {
            cursor: pointer;
            width: 0.6rem;
            height: 0.3rem;
            background-color: #087EFF;
            color: #fff;
            font-size: 0.16rem;
            margin: 0 0.06rem;
            border-radius: 0.04rem;
        }
        button:first-child {
            color: #087EFF;
            border: 1px solid #087EFF;
            background-color: #fff;
        }
    }
</style>

<style>
.waitBox .ivu-modal {
        width: 3.338rem !important;
        height: 2.27rem !important;
}
</style>
