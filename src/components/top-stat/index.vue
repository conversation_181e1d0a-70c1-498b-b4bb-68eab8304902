<template>
    <div class="top-stat">
        <div class="stat-item" v-for="item in list">
            <img :src="item.img" alt="" @click="item.isClick ? statClick(item) : ''" :class="{ 'isClick': item.isClick }">
            <div class="stat-item-right">
                <div>{{ item.title }}</div>
                <div class="stat-item-value" v-if="!item.isClick">{{ item.value }}</div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        list: Array,
    },
    data() {
        return {
            statClick(e) {
                this.$emit("statClick", e)
            }
        }
    }
}
</script>

<style lang="less" scoped>
.top-stat {
    height: 98px;
    padding: 16px 16px 16px 0;
    margin-bottom: 16px;
    background: #fff;
    border-radius: 4px;
    display: flex;

    .stat-item {
        width: 25%;
        display: flex;
        align-items: center;
        position: relative;
        padding-left: 16px;

        &:not(:first-child):before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 60px;
            background: #e8eef0;
        }

            img {
                width: 68px;
                height: 68px;
                margin-right: 18px;
            }

            .isClick {
                cursor: pointer;
            }

            .stat-item-right {
                color: #616f6f;
                font-size: 16px;

                .stat-item-value {
                    margin-top: 5px;
                    line-height: 30px;
                    font-weight: 700;
                    font-size: 32px;
                }
            }
        }
    }</style>