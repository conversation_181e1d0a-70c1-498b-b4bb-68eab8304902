<template>
    <div>
        <div class="cont">
            <div class="fm-content-info">
                <p class="fm-content-info-title">
                    <Icon type="md-list-box" size="24" color="#2b5fda" />详情
                </p>
                <div class="fm-content-box">
                    <Row>
                        <Col span="4"><span>门禁点名称</span></Col>
                        <Col span="8"><span>{{ formItem.name }}</span></Col>
                        <Col span="4"><span>所属区域</span></Col>
                        <Col span="8"><span>{{ formItem.origin_region_name }}</span></Col>
                        <Col span="4"><span>区域路径</span></Col>
                        <Col span="8"><span>{{ formItem.origin_region_path_name }}</span></Col>
                        <Col span="4"><span>监室</span></Col>
                        <Col span="8"><span>{{ formItem.room_name }}</span></Col>
                        <Col span="4"><span>门禁点编号</span></Col>
                        <Col span="8"><span>{{ formItem.door_no }}</span></Col>
                        <Col span="4"><span>门禁点状态</span></Col>
                        <Col span="8"><span>{{ formItem.door_statusName }}</span></Col>
                        <Col span="4"><span>绑定监室</span></Col>
                        <Col span="20"><span>{{ formItem.bind_room_statusName }}</span></Col>
                    </Row>
                </div>
            </div>
        </div>
        <div class="tab-box">
            <Tabs value="1">
                <TabPane label="门禁控制事件" name="1" style="min-height: 300px;">
                    <div>
                        <Form ref="searchform" :model="searchform" :label-width="80">
                            <Row>
                                <Col span="6">
                                <FormItem label="姓名" prop="xm">
                                    <Input v-model="searchform.xm"></Input>
                                </FormItem>
                                </Col>
                                <Col span="6">
                                <FormItem label="时间" prop="sj">
                                    <DatePicker v-model="searchform.sj" type="daterange" style="width: 100%"
                                        format="yyyy-MM-dd" @on-change="dateChange">
                                    </DatePicker>
                                </FormItem>
                                </Col>
                                <Col span="6">
                                <FormItem label=""> <Button>重置</Button>
                                    <Button type="primary" style="margin-left: 15px;">查询</Button>
                                </FormItem>

                                </Col>
                            </Row>
                        </Form>
                    </div>
                    <Table border :columns="columns1" :data="tableData"></Table>
                </TabPane>
            </Tabs>
        </div>


        <div class="bsp-base-fotter">
            <Button @click="close" style="margin-right: 10px;">取消</Button>
        </div>
    </div>
</template>

<script>

export default {
    props: {
        formItem: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {
            tableData: [],
            columns1: [
                {
                    title: '告警ID',
                    key: 'id',
                    align: 'center'
                }
            ],
            searchform: {}
        }
    },
    methods: {
        close() {
            this.$emit('close')
        },
        getData() {
            let params = {
                doorIndexCodes: ['b121718a8c354f88b4ab7aecdecc639d']
            }
            this.$store.dispatch('authPostRequest', { url: this.$path.pm_getDoorControlEventList, params: params }).then(res => {

                if (res.success) {
                    console.log(res, 'res');
                    this.tableData = res.data
                }

            })

        },
        dateChange(dates) {
            const date1 = new Date(dates[0]);
            const date2 = new Date(dates[1]);
            // 计算天数差
            const timeDiff = Math.abs(date2 - date1);
            const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

            // 检查是否 ≤ 90 天
            const isWithin90Days = daysDiff <= 90;
            if (!isWithin90Days) {
                this.$Message.error('日期范围超过三个月');
                return
            }
            // console.log(e);
            console.log('dateChange', this.searchform.sj[0])

        }
    },
    mounted() {
        this.getData()
    },
}

</script>

<style scoped lang="less">
.fm-content-info-title {
    border-top: 1px solid #CEE0F0;
    border-left: 1px solid #CEE0F0;
    border-right: 1px solid #CEE0F0;
}

.cont {
    box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
    border-radius: 4px;
    padding: 10px;
}

.tab-box {
    margin-top: 10px;
    box-shadow: 0 2px 4px 1px rgba(0, 34, 84, .12);
    padding: 10px;

}
</style>