<template>
    <div class="restraintsUsedApply">
        <div class="title">死亡登记</div>
        <Row style="margin-top: 10px; flex: 1">
            <Col :span="6" style="display: flex; flex-direction: column">
            <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
                <PersonnelSelector :enable-scan="false" @change="handleChange" title="死亡人员" />
            </div>
            </Col>
            <Col :span="18" style="border-left: 1px solid #efefef;display: flex;flex-direction: column;">
            <div class="form-container">
                <div class="form-content">
                    <Form ref="form" :model="formData" :rules="ruleValidate" :label-width="150">
                        <p class="detail-title">死亡登记</p>
                        <Row>
                            <Col :span="8">
                            <FormItem label="死亡人员" prop="jgryxm">
                                <Input v-model="formData.jgryxm" placeholder="待选择" disabled />
                            </FormItem>
                            </Col>
                            <Col :span="8">
                            <FormItem label="死亡时间" prop="deathTime"
                                :rules="[{ trigger: 'blur', message: '死亡时间为必填', required: true }]">
                                <DatePicker v-model="formData.deathTime" format="yyyy-MM-dd HH:mm:ss" type="datetime"
                                    placeholder="请选择"></DatePicker>
                            </FormItem>
                            </Col>
                            <Col :span="8">
                            <FormItem label="死亡地点" prop="deathSite"
                                :rules="[{ trigger: 'blur', message: '死亡地点为必填', required: true }]">
                                <Input v-model="formData.deathSite" placeholder="请输入" />
                            </FormItem>
                            </Col>
                            <Col :span="24">
                            <FormItem label="现场处理情况" prop="siteHandlingSituation"
                                :rules="[{ trigger: 'blur', message: '现场处理情况为必填', required: true }]">
                                <Input v-model="formData.siteHandlingSituation" placeholder="请输入" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 5 }"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                        <p class="detail-title">流程关联</p>
                        <row>
                            <Col :span="8">
                            <FormItem label="是否需要死亡鉴定">
                                <RadioGroup v-model="formData.deathAppraise">
                                    <Radio label="0">否</Radio>
                                    <Radio label="1">是</Radio>

                                </RadioGroup>
                            </FormItem>
                            </Col>
                        </row>
                    </Form>
                </div>
                <div style="display: flex; justify-content: flex-end; padding: 10px 0">
                    <Button style="margin-right: 15px" @click="handleCancel">取 消</Button>
                    <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
                </div>
            </div>
            </Col>
        </Row>
    </div>
</template>

<script>
import { mapActions } from "vuex";
import { formatDateparseTime } from "@/libs/util.js"
export default {
    components: {
    },
    data() {
        return {
            formData: {
                jgrybm: '',
                jgryxm: '',
                deathTime: '',
                deathAppraise: '0',
                deathSite: '',
                siteHandlingSituation: '',
            },
            ruleValidate: {
            },
            loading: false,
            punishmentMeasuresDict: [],
        };
    },
    computed: {},
    created() {
    },
    methods: {
        ...mapActions([
            "postRequest",
            "authGetRequest",
            "authPostRequest",
            "getRequest",
        ]),
        handleChange(data) {
            this.formData.jgrybm = data.jgrybm;
            this.formData.jgryxm = data.xm;
            this.formData.deathTime = new Date();
        },
        handleSubmit() {
            this.formData.deathTime = formatDateparseTime(this.formData.deathTime);
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.authPostRequest({ url: this.$path.zh_DeathRegisterSubmit, params: this.formData }).then(res => {
                        if (res.success) {
                            this.$Message.success('提交成功')
                            this.handleCancel()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })

                }
            })

        },
        handleCancel() {
            this.$refs.form.resetFields();
            this.$emit('on_show_table')
        },
    },
};
</script>

<style lang="less" scoped>
.fixPersonelStyle {
    /deep/ .flex-box {
        margin-bottom: 16px;

        .detail-title {
            margin-bottom: unset;
            line-height: 1.8;
            height: unset;
        }

        .ivu-btn {
            margin-top: 0 !important;
        }
    }
}

.restraintsUsedApply {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 10px;

    .title {
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
    }

    .form-container {
        padding: 10px;
        display: flex;
        flex-direction: column;
        flex: 1;

        .form-content {
            flex: 1;
            padding: 15px;
            border: 1px solid #d7d7d7;
            border-radius: 0.2em;
        }
    }
}
</style>
