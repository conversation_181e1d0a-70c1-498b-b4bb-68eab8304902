<template>
    <div>
        <s-DataGrid ref="grid" funcMark="sk-zhgl-swdjlb" :customFunc="true" v-if="!showAdd">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" icon="ios-add" @click.native="handleAdd()" size="small"
                    v-if="func.includes(globalAppCode + ':zhgl-swdjlb:swdj')">死亡登记</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary" @click.native="dieAppraisal(row)" size="small"
                    v-if="func.includes(globalAppCode + ':zhgl-swdjlb:swjd') && row.status == '02'">死亡鉴定</Button>
                <Button type="primary" @click.native="handleCorpse(row)" size="small"
                    v-if="func.includes(globalAppCode + ':zhgl-swdjlb:stcl') && row.status == '04'">尸体处理</Button>
                <Button type="primary" @click.native="leaderApproval(row,'approval')" size="small"
                    v-if="func.includes(globalAppCode + ':zhgl-swdjlb:ldsp') && row.status == '06'">所领导审批</Button>
                <Button type="primary" @click.native="leaderApproval(row,'details')" size="small"
                    v-if="func.includes(globalAppCode + ':zhgl-swdjlb:xq') && (row.status == '07' || row.status == '08')">详情</Button>
            </template>
        </s-DataGrid>

        <div v-if="showAdd" class="bsp-base-form">
            <component :is="component" @on_show_table="on_show_table" :dataMsg="dataMsg" :type="type" />
        </div>
    </div>
</template>
<script>

import addDeathRegister from './addDeath.vue'
import appraiseForm from './appraiseForm.vue'
import corpseHandle from './corpseHandle.vue'
import leaderAudit from './leaderAudit.vue'
export default {
    name: 'deathRegisterHome',
    components: {
        addDeathRegister,
        appraiseForm,
        corpseHandle,
        leaderAudit,

    },

    data() {
        return {
            showAdd: false,
            dataMsg: {},
            type: ''
        }
    },
    methods: {
        handleAdd() {
            this.component = 'addDeathRegister'
            this.showAdd = true
        },
        dieAppraisal(row) {
            this.component = 'appraiseForm'
            this.showAdd = true
            this.dataMsg = row
        },
        handleCorpse(row) {
            this.component = 'corpseHandle'
            this.showAdd = true
            this.dataMsg = row
        },
        leaderApproval(row,type) {
            this.component = 'leaderAudit'
            this.showAdd = true
            this.dataMsg = row
            this.type = type
        },
        on_show_table() {
            this.showAdd = false
            this.component = null
            // this.on_refresh_table()
        },
    }
}



</script>