<template>
    <div>
        <div class="shift-header" ref="header">
            <el-date-picker class="header-left" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                v-model="shiftTime" size="small" placeholder="请选择" @change="changeShiftTime" :clearable="false" />
            <!-- <div class="header-right">
                <Button :disabled="isNone" @click="handleExport">导出配置</Button>
                <Button type="primary" :disabled="isNone" @click="handleImport">导入配置</Button>
            </div> -->
        </div>
        <div class="shift-box">
            <div class="shift-content">
                <shift-table type="edit" :is-edit.sync="isEdit" :header-list="headerList"
                    :person-list="personList"></shift-table>
            </div>
            <Spin size="large" v-if="loading" fix></Spin>
        </div>
    </div>
</template>

<script>

import dayjs from 'dayjs';
import shiftTable from "./shiftTable";
import { mapActions } from "vuex";

export default {
    name: 'dytyPlan',
    data() {
        return {
            headerList: [],
            personList: [],
            shiftTime: [],
            isEdit: false,
            historyTime: {},
            loading: false,
        }
    },
    components: {
        shiftTable
    },

    computed: {

        isNone() {
            let postList = this.headerList && this.headerList[0];
            if (!postList) return true;
            let subPosList = postList.subPostList && postList.subPostList[0];
            if (!subPosList) return true;
            let timeList = subPosList.subPostTimeVOS && subPosList.subPostTimeVOS[0];
            if (!timeList) return true;
            return false;
        }
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleExport() {
            //   let param =  Object.assign({}, this.shiftTime);
        },
        handleImport() {
            //   this.$refs.inputFile.click();
        },
        changeShiftTime(time) {
            this.$refs.header.click();
            this.$nextTick(() => {
                if (!time[0] && !time[1]) return this.shiftTime = Object.assign({}, this.historyTime);
                if (this.isEdit) {
                    this.$Modal({
                        content: "当前存在未提交的数据，切换日期将丢失，是否继续?",
                        confirm: () => {
                            this.historyTime = Object.assign({}, this.shiftTime);
                            this.getInfo();
                        },
                        cancel: () => {
                            this.shiftTime = Object.assign({}, this.historyTime);
                        }
                    });
                } else {
                    this.historyTime = Object.assign({}, this.shiftTime);
                    this.getInfo();
                }
            });
        },
        getHeader() {
            this.authGetRequest({ url: this.$path.zh_defaultHeader, params: {} }).then(res => {
                let { headerList = [], tempId } = res.data || {};
                this.headerList = headerList;
                this.tempId = tempId;
            });
        },
        getInfo() {
            this.isEdit = false;
            let param = {
                startTime: this.shiftTime[0],
                endTime: this.shiftTime[1]
            }
            this.loading = true;
            this.authGetRequest({ url: this.$path.zh_listByDutyDate, params: param }).then(res => {

                let { personList } = res.data || {};
                this.personList = personList;
                 this.loading = false;
            });

        },
        initTime() {
            let startTime = dayjs().format('YYYY-MM-DD');
            let endTime = dayjs().add(6, 'day').format('YYYY-MM-DD');

            this.shiftTime = [startTime, endTime];
            this.historyTime = [startTime, endTime];
        },
        handleSubmit() {
            let param = {
                personList: this.personList,
                tempId: this.tempId,
            };
            this.authPostRequest({ url: this.$path.zh_staffDutyRecordAdd, params: param }).then(res => {
                if (res.success) {
                    this.$Message.success('提交成功！');
                    this.$emit("refresh", 1)
                } else {
                    this.$Message.error(res.message);
                }
            })
        },
        initData() {
            this.initTime();
            this.getInfo();
            this.getHeader();
        }
    }
}
</script>

<style scoped lang="less">
.shift-header {
    height: 70px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left {
        width: 260px;
    }

    .header-right {
        button {
            margin-left: 16px;
        }
    }
}
</style>