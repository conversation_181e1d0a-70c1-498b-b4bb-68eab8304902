<template>
  <div class="person-box"   @dblclick="onSelect">
    <div v-show="showTip" :class="['person-tip', isLeft?  'left-tip' : 'right-tip'  ]">
      <div class="list tip-list">
        <span  v-for="(item,idx) in renderList[0]" :key="idx" :class="[item.policeType == '0' ? 'police' : 'no-police']">{{item.policeName}}<Icon v-if="type === 'edit'" class="tip-close" type="md-close" @click="delTip(item)" /></span>
      </div>
      <div class="list tip-list">
        <span  v-for="(item,idx) in renderList[1]" :key="idx" :class="[item.policeType == '0' ? 'police' : 'no-police']">{{item.policeName}}<Icon v-if="type === 'edit'" class="tip-close" type="md-close" @click="delTip(item)"/></span>
      </div>
    </div>
    <div class="list display-list"  @click="onClick" >
      <span  v-for="(item,idx) in displayList" :key="idx" :class="[item.policeType == '0' ? 'police' : 'no-police']">{{item.policeName}}</span>
      <!-- <span class="tip-item" v-if="isOver">{{list.length - count + 1}}人...</span> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "shiftPerson",
  data() {
    return {
      showTip: false,
      renderList: [[], []],
    };
  },
  props: {
    count: {
      type: Number,
      default: Number.MAX_VALUE
    },
    list: {
      type: Array,
      default: () => [],
    },
    defaultKey: {
      type: String,
      require: true,
    },
    clickKey: {
      type: String,
      default: "",
    },
    isLeft: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "view",
      validator: (val) => ["edit", "view"].includes(val),
    },
  },
  computed: {
    displayList() {
    //   return this.isOver ? this.list.slice(0, this.count - 1) : this.list;
       return this.list;
    },
    isOver() {    
      return this.list.length > this.count;

    }
  },
  watch: {
    clickKey() {
      if (this.clickKey === this.defaultKey) {
        this.onShow();
      } else {
        this.showTip = false;
      }
    },
    list() {
      if (this.clickKey === this.defaultKey) {
        this.onShow();
      } else {
        this.showTip = false;
      }
    }
  },
  methods: {
    onShow() {
      this.showTip = true;
      let len = Math.ceil(this.list.length / 2);
      len = len >= this.count / 2 ? len : this.count / 2;
      this.renderList = [this.list.slice(0, len), this.list.slice(len, this.list.length)];
    },
    onClick() {
      this.$emit("on-click", this.defaultKey);
    },
    onSelect() {
      this.$emit("on-select");
    },
    delTip(item) {
      // console.log(item);
      this.$emit("on-delTip",item);
      
    },
  }

};
</script>

<style scoped lang="less">
  .person-box {
    width: 100%;
    height: 100%;
    position: relative;
    user-select: none;
  }
  .person-tip{
    min-width: 100%;
    top: 0;
    position: absolute;
    width: auto;
    height: 65px;
    background: #FFFFFF;
    box-shadow: 0px 2px 14px 0px rgba(0,0,0,0.24);
    border-radius: 6px;
    padding: 4px 0 0 4px;
    z-index: 10;
    &.right-tip {
      right: 0;
    }
    &.left-tip {
      left: 0;
    }
  }
  .display-list{
    padding: 4px 0 0 4px;
    font-size: 0;
    width: 100%;
    height: 100%;
    flex-wrap: wrap;
    overflow: auto;
    span {
      width: 65px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .list{
    display: flex;
    user-select: none;
    span {
      white-space: nowrap;
      user-select: none;
      min-width: 58px;
      height: 27px;
      line-height: 27px;
      border-radius: 4px;
      font-size: 14px;
      text-align: center;
      padding: 0 8px;
      margin: 0 4px 4px 0;
      &.police{
        background: #D6EAFF;
        color: #0555A8;
      }
      &.no-police{
        background: #E4E0FF;
        color: #5B49D0;
      }
    }
    .tip-item{
      background: #E1E7EF;
      color: #8D99A5;
    }
  }

  .tip-close {
    cursor: pointer;
  }
</style>