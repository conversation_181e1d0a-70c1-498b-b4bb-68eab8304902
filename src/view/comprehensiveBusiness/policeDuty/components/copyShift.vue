<template>
  <div>
    <Modal title="复制排班" :value="stepModal1 || stepModal2" :width="752" @on-close="closeModal()" :footer-hide="true">
      <div class="com-modal-container" v-if="stepModal1">
        <div class="com-module-layout">
          <div class="com-content-wrapper">
            <p class="title-box">
              <span class="title">源排班日期</span>
              <span class="tip">日期为灰色不可选, 仅可选择当前状态为"启用"的模板, 且已有排班的记录</span>
            </p>
            <p class="date-box">
              <span class="title">已选择日期：</span>
              <span class="date" v-if="sourceStartTime && sourceEndTime">{{ sourceStartTime }}~{{ sourceEndTime
              }}</span>
            </p>
            <div class="date-picker">
              <date-picker key="stepPicker1" :start-time="sourceStartTime" :end-time="sourceEndTime" type="daterange"
                :disable-fn="sourceDisableFn" @on-change="changeSourceDate"
                @on-change-range="changeSourceRange"></date-picker>
            </div>
          </div>
        </div>
        <div slot="footer" class="com-modal-submit">
          <Button @click="closeModal()">取消</Button>
          <Button type="primary" @click="onStep1">下一步</Button>
        </div>
      </div>
      <div class="com-modal-container" v-if="stepModal2">
        <div class="com-module-layout">
          <div class="com-content-wrapper">
            <p class="title-box">
              <span class="title">目标排班起始日期</span>
              <span class="tip">选择单个日期作为起始日期</span>
            </p>
            <p class="date-box">
              <span class="title">已选择日期：</span>
              <span class="date" v-if="targetTime">{{ targetTime }}</span>
            </p>
            <div class="date-picker">
              <date-picker key="stepPicker2" :start-time="targetTime" type="date" :disable-fn="targetDisableFn"
                @on-change="changeTargetDate"></date-picker>
            </div>
          </div>
        </div>
        <div slot="footer" class="com-modal-submit">
          <Button @click="closeModal()">取消</Button>
          <Button @click="onStep2" type="primary">下一步</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import datePicker from "./datePicker";
import dayjs from 'dayjs';
export default {
  name: "copyShift",
  components: { datePicker },
  data() {
    // const nowDate = new Date().Format("yyyy-MM-dd");
    const nowDate = dayjs().format("yyyy-MM-dd");
    return {
      stepModal1: false,
      stepModal2: false,
      sourceStartTime: "",
      sourceEndTime: "",
      sourceDisableFn: () => { },
      targetTime: "",
      targetDisableFn: (date) => date < nowDate
    };
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    open() {
      this.stepModal1 = true;
    },
    changeSourceDate(time) {
      this.sourceStartTime = time.startTime;
      this.sourceEndTime = time.endTime;
    },
    changeSourceRange(obj) {
      let obj1 = obj.startRange || {};
      let obj2 = obj.endRange || {};
      let param = {
        // startTime: new Date(obj1.year, obj1.month - 2, 1).Format("yyyy-MM-dd"),
        // endTime: new Date(obj2.year, obj2.month + 1, 1).Format("yyyy-MM-dd"),
        startTime: dayjs().year(obj1.year).month(obj1.month - 2).date(1).format('YYYY-MM-DD'),
        endTime: dayjs().year(obj2.year).month(obj2.month + 1).date(1).format('YYYY-MM-DD'),
      };
      this.authGetRequest({ url: this.$path.zh_checkCopyDate, params: param }).then(res => {
        let disableArr = res.data || [];
        this.sourceDisableFn = (date) => disableArr.includes(date);
      });

    },
    closeModal(isRefresh = false) {
      this.stepModal1 = false;
      this.stepModal2 = false;
      this.$emit("on-close", isRefresh);
    },
    onStep1() {
      if (!(this.sourceStartTime && this.sourceEndTime)) return this.$Message.info("请选择源排版日期");
      let param = {
        startTime: this.sourceStartTime,
        endTime: this.sourceEndTime,
      };
      this.authGetRequest({ url: this.$path.zh_checkCopyDateNext, params: param }).then(res => {
        if (res.success) {
          this.stepModal1 = false;
          this.stepModal2 = true;
        } else {
          this.$Message.info(res.msg);

        }

      }).catch(msg => {
        this.$Message.info(msg);
      });
    },
    changeTargetDate(time) {
      this.targetTime = time.startTime;
    },
    onStep2() {
      if (!this.targetTime) return this.$Message.info("请选择目标排班起始日期");
      const targetDate = new Date(this.targetTime).getTime(); // 目标日期的时间戳
      const today = new Date().setHours(0, 0, 0, 0);      // 今天凌晨的时间戳（忽略时分秒）
      if (targetDate < today) return this.$Message.info("请选择今天及以后的日期");
      let time = new Date(this.sourceEndTime).getTime() - new Date(this.sourceStartTime).getTime();
      // let endTime = new Date(new Date(this.targetTime).getTime() + time).Format("yyyy-MM-dd");
      // let timeDiff = dayjs(this.sourceEndTime).valueOf() - dayjs(this.sourceStartTime).valueOf();
      let endTime = dayjs(new Date(this.targetTime).getTime() + time).format('YYYY-MM-DD');
      let param = {
        startTime: this.targetTime,
        endTime,
      };
      this.authGetRequest({ url: this.$path.zh_checkHasData, params: param }).then(res => {
        if (!res.data) return this.completeStep(param);
        this.$Modal.confirm({
          title: '删除',
          content: '请确认是否删除？',
          onOk: () => {
            this.completeStep(param)
          }
        }).catch(err => {
          this.$Message.error(err || "");
        });
      })

    },
    completeStep(obj) {
      let param = {
        sourceEndTime: this.sourceEndTime,
        sourceStartTime: this.sourceStartTime,
        targetStartTime: obj.startTime,
        targetEndTime: obj.endTime
      };

      this.authPostRequest({ url: this.$path.zh_copyData, params: param }).then(res => {
        this.$Message.success("提交成功");
        this.closeModal(true);
      }).catch(err => {
        this.$Message.error(err || "");
      });
    }
  }
};
</script>

<style lang="less" scoped>
.title-box {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 28px;

  .title {
    font-weight: bold;
    color: #00244A;
    margin-right: 12px;
  }

  .tip {
    color: #8D99A5;
  }
}

.date-box {
  margin-bottom: 16px;
  font-size: 16px;
  line-height: 28px;

  .title {
    color: #415060;
  }

  .date {
    color: #00244A;
  }
}

.com-modal-submit {
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>