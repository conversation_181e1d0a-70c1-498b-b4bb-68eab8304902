<template>
    <Modal v-model="showAddModel" width="60%" title="新增模板" class="addModel" ref="comProcessModalRef">
        <div class="com-modal-height-limit">
            <Form ref="formData" :model="formData" :label-width="120" :rules="formRule" :label-colon="true">
                <p class="detail-title">基本信息</p>
                <Row>
                    <Col span="8">
                    <FormItem label="模板名称" prop="name" required>
                        <Input v-model="formData.name" placeholder="请输入"></Input>
                    </FormItem>
                    </Col>

                    <Col span="8">
                    <FormItem label="状态" prop="status" required>
                        <!-- <Select v-model="formData.status">
                            <Option value="0">启用</Option>
                            <Option value="1">关闭</Option>
                        </Select> -->
                        <s-dicgrid v-model="formData.status" dicName="ZD_SFQY" />
                    </FormItem>

                    </Col>
                </Row>
            </Form>
            <p class="detail-title">模板设置</p>
            <div class="com-content-wrapper com-lists-box-page-width plr">
                <div class="com-lists-box mb-15 " v-for="(post, idx1) in dutyPostDTOS" :key="'post' + idx1">
                    <Icon class="item-trash" type="ios-trash" @click="delDutyPost(idx1)" v-show="idx1 !== 0" />
                    <Form :model="post" :ref="'postForm' + idx1" :rules="postRule" :label-width="120">
                        <Row>
                            <Col span="8">
                            <FormItem label="值班岗位" prop="postName">
                                <Input v-model="post.postName" placeholder="请输入"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                    </Form>
                    <div class="sub-post-list">
                        <div v-for="(subPos, idx2) in post.dutyRoleDTOS" :key="'subPost' + idx1 + idx2"
                            class="sub-post-item">
                            <Icon class="sub-post-del" type="md-close-circle" @click="delDutySubPost(idx1, idx2)"
                                v-show="idx2 !== 0" />
                            <Form :model="subPos" :ref="'subPostForm' + idx1 + idx2" :rules="subPostRule"
                                :label-width="120">
                                <Row>
                                    <Col span="12">
                                    <FormItem label="值班角色">
                                        <Input v-model="subPos.dutyRoleName" :max-length="20" placeholder="请输入"></Input>
                                    </FormItem>
                                    </Col>
                                    <Col span="10">
                                    <FormItem label="岗位名称" prop="dutyPostId">
                                        <Select v-model="subPos.dutyPostId"
                                            @on-change="changeSubPost($event, idx1, idx2)" :label-in-value="true">
                                            <Option v-for="item in dutyList" :value="item.code" :key="item.code">{{
                                                item.name }}</Option>
                                        </Select>
                                    </FormItem>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span="12">
                                    <FormItem label="班次设置" prop="roleTimeDTOS">
                                        <div class="time-list">
                                            <template v-if="subPos.roleTimeDTOS">
                                                <div v-for="(time, idx3) in subPos.roleTimeDTOS" class="time-item"
                                                    :key="'time' + idx1 + idx2 + idx3">
                                                    <span v-if="time.dutyShift">{{ time.dutyShift }}：</span>
                                                    <span v-if="`${time.dutyTimeTypeBegin}` === '2'">次日</span>
                                                    <span>{{ time.dutyTimeBegin }}</span>
                                                    <span>-</span>
                                                    <span v-if="`${time.dutyTimeTypeEnd}` === '2'">次日</span>
                                                    <span>{{ time.dutyTimeEnd }}</span>
                                                    <Icon type="md-close" class="time-del"
                                                        @click="delDutyTime(idx1, idx2, idx3)" />
                                                </div>
                                            </template>
                                            <Icon class="time-add" type="ios-add-circle-outline"
                                                @click="openTimeModal(idx1, idx2)" />
                                        </div>
                                    </FormItem>
                                    </Col>
                                    <Col span="10">
                                    <FormItem label="缺勤通知人" prop="absentNotifierSfzh">
                                        <user-selector v-model="subPos.absentNotifierSfzh" tit="用户选择"
                                            :text.sync="subPos.absentNotifierName" @onCancel="onCancel"
                                            @onClear="onClear" @onSelect="onSelect" returnField="idCard"
                                            numExp='num>=1'>
                                        </user-selector>
                                    </FormItem>
                                    </Col>
                                </Row>
                            </Form>
                        </div>
                        <div class="sub-post-add" @click="addDutySubPost(idx1)">添加值班角色</div>
                    </div>
                </div>
                <div class="com-dy-btn btn-add mt-15" @click="addDutyPost">新增</div>
            </div>

        </div>
        <div slot="footer">
            <Button @click="showAddModel = false" class="save">关 闭</Button>
            <Button type="primary" @click="handleSubmit" class="save">确 定</Button>

        </div>
        <Modal v-model="timeModal" width="548" class-name="time-modal" title="班次时间设置">
            <div class="com-modal-container" v-if="timeModal">
                <div class="com-module-layout">
                    <div class="com-content-wrapper">
                        <Form :model="timeForm" ref="timeForm" :rules="timeRule" :label-width="120">
                            <FormItem label="班次名称">
                                <Input class="time-name" v-model="timeForm.dutyShift"></Input>
                            </FormItem>
                            <FormItem label="开始时间" prop="dutyTimeBegin">
                                <Select v-model="timeForm.dutyTimeTypeBegin" class="select-type">
                                    <Option v-for="item in timeTypeData" :value="item.value" :key="item.value">{{
                                        item.label }}
                                    </Option>
                                </Select>
                                <TimePicker class="select-time" v-model="timeForm.dutyTimeBegin" format="HH:mm"
                                    placeholder="开始时间"></TimePicker>
                            </FormItem>
                            <FormItem label="结束时间" prop="dutyTimeEnd">
                                <Select v-model="timeForm.dutyTimeTypeEnd" class="select-type">
                                    <Option v-for="item in timeTypeData" :value="item.value" :key="item.value">{{
                                        item.label }}
                                    </Option>
                                </Select>
                                <TimePicker class="select-time" v-model="timeForm.dutyTimeEnd" format="HH:mm"
                                    placeholder="开始时间"></TimePicker>
                            </FormItem>
                        </Form>
                    </div>
                </div>

            </div>
            <div class="com-modal-submit" slot="footer">
                <Button @click="closeTimeModal" class="save">关 闭</Button>
                <Button type="primary" @click="confirmTimeModal" class="save">确 定</Button>
            </div>
        </Modal>
    </Modal>
</template>

<script>
import { mapActions } from "vuex";
import { sDialog } from 'sd-custom-dialog'
import { userSelector } from 'sd-user-selector'
export default {
    name: 'addModel',
    components: {
        userSelector,
        sDialog
    },
    props: {
        optType: { // 操作类型
            type: String,
            default: "add", // add、edit
        },
    },
    data() {
        return {
            showAddModel: false,
            formRule: {
                name: [{ required: true, message: "请填写模板名称", trigger: "change" }],
                status: [{ required: true, message: "状态为必填", trigger: "change" }],
            },
            formData: {
                status: "0",
            },
            postRule: {
                postName: [{ required: true, message: "请填写值班岗位", trigger: "change" }],
            },
            subPostRule: {
                roleTimeDTOS: [{ required: true, message: "请选择至少一个班次设置", trigger: "change", type: "array", min: 1 }],
                dutyPostId: [{ required: true, message: "请选择岗位名称", trigger: "change" }],
            },
            dutyPostDTOS: [{
                dutyRoleDTOS: [{
                    roleTimeDTOS: [],
                }]
            }],
            selectIdx: [],
            dutyList: [],
            timeForm: {},
            timeTypeData: [
                { label: "当日", value: 1 },
                { label: "次日", value: 2 },
            ],
            timeModal: false,
            timeRule: {
                dutyShift: [{ required: true, message: "请填写班次名称", trigger: "change" }],
                dutyTimeBegin: [{ required: true, trigger: "change", message: "请选择开始时间" }],
                dutyTimeEnd: [{ required: true, trigger: "change", message: "请选择结束时间" }],
            },

        }
    },
    mounted() {
        this.handleGetZD_POST()
    },
    methods: {
        ...mapActions([
            "postRequest",
            "authGetRequest",
            "authPostRequest",
            "getRequest",
        ]),
        open(id) {
            this.showAddModel = true;
            this.init(id);
            this.handleGetZD_POST()
        },

        addDutySubPost(postIdx) {
            this.selectIdx = [postIdx];
            let list = this.getList(1).arr;
            list.push({ roleTimeDTOS: [] });
        },
        init(tempId) {
            if (tempId) {
                this.formData.status = '0'
                this.getTempInfo(tempId);
            }
        },
        getTempInfo(id) {
            this.authGetRequest({ url: this.$path.zh_GetTemplateInfo, params: { id } }).then(res => {
                let data = res.data || {};
                let postList = data.dutyPostDTOS || [{
                    dutyRoleDTOS: [{
                        roleTimeDTOS: [],
                    }]
                }];
                postList.forEach(post => {
                    let subPostList = post.dutyRoleDTOS || [];
                    post.dutyRoleDTOS = subPostList;
                    subPostList.forEach(subPost => {
                        let timeList = subPost.roleTimeDTOS || [];
                        subPost.roleTimeDTOS = timeList;
                    });
                });
                // this.dutyPostDTOS = postList;
                this.dutyPostDTOS = postList.map(item => {
                    // 处理 dutyRoleDTOS 数组
                    if (item.dutyRoleDTOS && Array.isArray(item.dutyRoleDTOS)) {
                        item.dutyRoleDTOS = item.dutyRoleDTOS.map(role => {
                            // 替换 absentNotifierSfzh 为 '' 如果为 null
                            if (role.absentNotifierSfzh === null) {
                                role.absentNotifierSfzh = '';
                            }
                            return role;
                        });
                    }
                    return item;
                });
                console.log(this.dutyPostDTOS, 'ssssssssssssss');
                this.$set(this.formData, "name", data.name);
                this.$set(this.formData, "id", data.id);
                this.formData.name = data.name;
            });
        },
        getList(type) {
            let [postIdx, subPosIdx, timeIdx] = this.selectIdx;
            let obj = {};
            let dutyPost = this.dutyPostDTOS || [];
            obj = dutyPost[postIdx] || {};
            if (type === 0) return { arr: dutyPost, obj };
            let dutySubPost = obj.dutyRoleDTOS || [];
            obj = dutySubPost[subPosIdx] || {};
            if (type === 1) return { arr: dutySubPost, obj };
            let dutyTime = obj.roleTimeDTOS || [];
            obj = dutyTime[timeIdx] || {};
            if (type === 2) return { arr: dutyTime, obj };
        },
        addDutyPost() {
            this.dutyPostDTOS.push({
                dutyRoleDTOS: [{
                    roleTimeDTOS: [],
                }]
            });
        },
        delDutyPost(postIdx) {
            this.dutyPostDTOS.splice(postIdx, 1);
        },
        delDutySubPost(postIdx, subPostIdx) {
            this.selectIdx = [postIdx];
            let list = this.getList(1).arr;
            list.splice(subPostIdx, 1);
        },
        changeSubPost(data, postId, subPosIdx) {
            let dataName = data.label;
            this.selectIdx = [postId, subPosIdx];
            let obj = this.getList(1).obj;
            console.log(obj);
            obj["postName"] = dataName;

        },
        openTimeModal(postIdx, subPosIdx) {
            this.timeModal = true;
            this.timeForm = {
                dutyTimeTypeEnd: 1,
                dutyTimeTypeBegin: 1,
                dutyTimeBegin: '08:00',
                dutyTimeEnd: '18:00'
            };
            this.selectIdx = [postIdx, subPosIdx];
        },
        closeTimeModal() {
            this.timeModal = false;
        },
        async confirmTimeModal() {
            let flag = await this.$refs.timeForm.validate();
            if (!flag) return;
            this.timeModal = false;
            let data = Object.assign({}, this.timeForm);
            let list = this.getList(2).arr;
            list.push(data);
            let [postIdx, subPosIdx] = this.selectIdx;
            this.$refs[`subPostForm${postIdx}${subPosIdx}`][0].validateField("roleTimeDTOS");
        },
        delDutyTime(postIdx, subPosIdx, timeIdx) {
            this.selectIdx = [postIdx, subPosIdx];
            let list = this.getList(2).arr;
            list.splice(timeIdx, 1);
            this.$refs[`subPostForm${postIdx}${subPosIdx}`][0].validateField("roleTimeDTOS");
        },
        handleBack(flag = 0) {
            this.$emit("on-close", flag);
            this.visible = false;
            this.formData = {};
            this.dutyPostDTOS = [{
                dutyRoleDTOS: [{
                    roleTimeDTOS: [],
                }]
            }];
            this.$refs.comProcessModalRef.close();
        },
        async checkDutyPost() {
            let flag = true;
            let postList = this.dutyPostDTOS || [];
            for (let idx1 = 0; idx1 < postList.length; idx1++) {
                flag = await this.$refs[`postForm${idx1}`][0].validate();
                if (!flag) return flag;
                let subPostList = postList[idx1].dutyRoleDTOS || [];
                for (let idx2 = 0; idx2 < subPostList.length; idx2++) {
                    flag = await this.$refs[`subPostForm${idx1}${idx2}`][0].validate();
                    if (!flag) return flag;
                }
            }
            return flag;
        },
        getHasSubPost(postList) {
            postList.forEach(post => {
                post.hasSubPost = 0;
                let subPostList = post.dutyRoleDTOS || [];
                subPostList.forEach(subPost => {
                    let flag = (subPost.dutyRoleName || "").trim() !== "";
                    if (flag) return post.hasSubPost = 1;
                });
            });
        },
        async handleSubmit() {
            let flag = await this.$refs.formData.validate() && await this.checkDutyPost();
            if (!flag) return this.$Message.error("请检查表单填写是否正确！");
            let param = Object.assign({}, this.formData, { dutyPostDTOS: [...this.dutyPostDTOS] });
            this.getHasSubPost(param.dutyPostDTOS);
            let api = "";
            if (this.optType == "edit") {
                // todo
                // api = shiftTemplateEdit;
                this.authPostRequest({ url: this.$path.zh_Templateupdate, params: param }).then(res => {
                    if (res.success) {
                        this.$Message.success('修改成功')
                        this.showAddModel = false
                        this.$emit('on-close')
                    } else {
                        this.$Message.error(res.message)
                    }
                })
            } else {
                // api = shiftTemplateAdd;
                this.authPostRequest({ url: this.$path.zh_DutyTemplateAdd, params: param }).then(res => {
                    if (res.success) {
                        this.$Message.success('提交成功')
                        this.showAddModel = false
                        this.$emit('on-close')
                    } else {
                        this.$Message.error(res.message)
                    }
                })
            }
            console.log(param, '-----param-----');


        },
        handleGetZD_POST() {
            this.authGetRequest({ url: "/bsp-com/static/dic/acp/ZD_POST.js" }).then(res => {
                let scales = eval('(' + res + ')')
                this.dutyList = scales()

            })
        },
        onSelect(data) {
            //  alert("选择后，点击确认时触发")
            //console.info(data)
        },
        onCancel() {
            //  alert("取消事件，点击取消时触发")
        },
        onClear() {
            //  alert("清除内容，点击清除内容时触发")
        },

    }
}
</script>

<style scoped lang="less">
.time-picker-container {
    display: flex;
    align-items: center;
}

.com-modal-height-limit {

    // max-height: 75vh;
    // overflow: auto;
    .btn-add {
        text-align: center;
        font-size: 16px;
        padding: 10px 0;
        border: 1px dashed #2390ff;
        color: #2390ff;
        margin: 15px 0;
        cursor: pointer;
    }
}

.time-list {
    display: flex;
    flex-wrap: wrap;
    min-height: 38px;
    align-items: center;

    // margin-top: -8px;
    .time-item {
        padding: 0 8px;
        width: auto;
        background: #DCEDFF;
        border-radius: 4px;
        margin-right: 8px;
        font-size: 0;
        display: inline-flex;
        align-items: center;

        span {
            font-size: 16px;
            color: #1071D2;
        }

        .time-del {
            font-size: 18px;
            cursor: pointer;
            margin-left: 5px;
            color: #9c9c9c;
        }
    }

    .time-add {
        font-size: 28px;
        color: #2390ff;
        cursor: pointer;
    }
}

.com-lists-box {
    border: 1px solid #dae1e9;
    box-shadow: 0 2px 5px 0 rgba(78, 91, 109, .12);
    border-radius: 6px;
    padding: 16px;
    position: relative;

    .item-trash {
        position: absolute;
        right: 16px;
        top: 16px;
        cursor: pointer;
        font-size: 28px;
        color: #bbb;

    }
}

.mb-15 {
    margin-bottom: 15px;
}

.sub-post-list {
    .sub-post-item {
        min-height: 124px;
        background: #F5F7FA;
        border-radius: 8px;
        margin-bottom: 16px;
        padding: 16px 0;
        position: relative;

        .sub-post-del {
            position: absolute;
            right: 16px;
            top: 16px;
            cursor: pointer;
            font-size: 24px;
            color: #bbb;
        }
    }

    .sub-post-add {
        width: 116px;
        height: 30px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #C4CED8;
        text-align: center;
        line-height: 30px;
        font-size: 14px;
        color: #415060;
        cursor: pointer;
    }
}

.time-modal {
    .time-name {
        width: 320px;
    }

    .select-type {
        width: 92px;
        margin-right: 8px;
        display: inline-block;
        vertical-align: middle;
    }

    .select-time {
        width: 220px;
        display: inline-block;
    }
}
</style>