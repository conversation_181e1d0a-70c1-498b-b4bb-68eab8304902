<template>
    <div class="form-container">
        <Form ref="formData" :model="formData" :label-width="155" :rules="rules">
            <Row>
                <Col span="12">
                <FormItem label="签到验证模式" prop="signinValidMode">
                    <RadioGroup v-model="formData.signinValidMode">
                        <Radio v-for="(item, index) in signinValidMode" :label="item.code" :key="item.code">{{ item.name
                            }}</Radio>
                    </RadioGroup>
                </FormItem>
                </Col>
            </Row>
            <Row>
                <Col span="12">
                <FormItem label="超时阈值" prop="timeoutThreshold">
                    <Input v-model="formData.timeoutThreshold" placeholder="单位：分钟" type="number" />
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="超时推送对象" prop="timeoutPushTarget">
                    <user-selector v-model="formData.timeoutPushTargetSfzh" tit="用户选择"
                        :text.sync="formData.timeoutPushTarget" returnField="id" numExp='num>=1' msg="至少选中1人">
                    </user-selector>
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="超时次数阀值" prop="todayTimeoutCount">
                    <Input v-model="formData.todayTimeoutCount" placeholder="单位：次数" type="number" />
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="本日超时推送对象" prop="todayTimeoutCountPushTarget">
                    <user-selector v-model="formData.todayTimeoutCountPushTargetSfzh" tit="用户选择"
                        :text.sync="formData.todayTimeoutCountPushTarget" returnField="id" numExp='num>=1' msg="至少选中1人">
                    </user-selector>
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="签到间隔" prop="signinInterval">
                    <Input v-model="formData.signinInterval" placeholder="单位：分钟" type="number" />
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="签到时间段" prop="signinTime">
                    <TimePicker v-model="signinTime" type="timerange" placeholder="请输入" format="HH:mm">
                    </TimePicker>
                </FormItem>
                </Col>
                <Col span="12">
                <FormItem label="补签时间" prop="lateSigninTime">
                    <Input v-model="formData.lateSigninTime" placeholder="请输入" type="number"
                        :disabled="!formData.signinInterval" />
                </FormItem>
                </Col>
            </Row>
        </Form>
    </div>
</template>

<script>
import { mapActions } from "vuex";
import { userSelector } from 'sd-user-selector'
export default {
    name: 'superviseModel',
    components: {
        userSelector
    },
    props: {
        row: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        const validateLateTime = (rule, value, callback) => {
            const { signinInterval } = this.formData;

            if (!signinInterval || !value) {
                callback();
                return;
            }
            if (Number(value) > Number(signinInterval)) {
                callback(new Error('补签时间不能超过签到间隔'));
            } else {
                callback();
            }
        };
        return {
            formData: {
                signinValidMode: '',
                signinStartTime: '',
                signinEndTime: '',
                signinInterval: '',
                lateSigninTime: '',
                timeoutThreshold: '',
                timeoutPushTarget: '',
                todayTimeoutCount: '',
                todayTimeoutCountPushTarget: '',


            },
            signinTime: [],
            signinValidMode: [],
            rules: {
                signinValidMode: [
                    { required: true, message: '请选择签到验证模式', trigger: 'change' }
                ],
                timeoutThreshold: [
                    { required: true, message: '请填写超时阈值', trigger: 'blur' }
                ],
                timeoutPushTarget: [
                    { required: true, message: '请选择超时推送对象', trigger: 'change' }
                ],
                todayTimeoutCount: [
                    { required: true, message: '请填写超时次数阀值', trigger: 'blur' }
                ],
                todayTimeoutCountPushTarget: [
                    { required: true, message: '请选择本日超时推送对象', trigger: 'change' }
                ],
                signinInterval: [
                    { required: true, message: '请填写签到间隔', trigger: 'blur' }
                ],
                lateSigninTime: [
                    { required: true, message: '请填写补签间', trigger: 'blur' },
                    { validator: validateLateTime, trigger: 'blur' }

                ],


            }

        }
    },
    watch: {
        // row: {
        //     handler(val) {
        //         this.getSuperviseRule(val.id);
        //     },
        // }
    },
    mounted() {
        this.handleGetZD_SIGNTYPE()
        // this.getSuperviseRule();
    },
    methods: {
        ...mapActions([
            "postRequest",
            "authGetRequest",
            "authPostRequest",
            "getRequest",
        ]),
        handleGetZD_SIGNTYPE() {
            this.$store
                .dispatch("authGetRequest", {
                    url: "/bsp-com/static/dic/acp/ZD_SIGNTYPE.js",
                })
                .then((res) => {
                    let scales = eval("(" + res + ")");
                    this.signinValidMode = scales();
                });
        },
        refresh() {
            this.$refs.formData.resetFields();

        },
        confirmSupervise() {
            this.formData.signinStartTime = this.signinTime[0];
            this.formData.signinEndTime = this.signinTime[1];
            this.formData.staffDutyTemplateId = this.row.id;
            this.$refs.formData.validate((valid) => {
                if (valid) {
                    console.log("start", this.formData);
                    let params = {
                        ...this.formData
                    };
                    this.authPostRequest({ url: this.$path.zh_createOrUpdate, params: params }).then(res => {
                        if (res.success) {
                            this.$Message.success('操作成功');
                            this.$emit('on-close');
                        } else {
                            this.$Message.error(res.message);
                        }
                    })
                }
            })
        },
        getSuperviseRule(id) {
            this.authPostRequest({ url: this.$path.zh_dutySuperviseRuleList, params: { staffDutyTemplateId: id } }).then(res => {
                 console.log(res.data ,res.success,res.data.length);
                if (res.success && res.data.length > 0) {
                 
                    this.formData = res.data[0];
                    this.formData.lateSigninTime = String(this.formData.lateSigninTime);
                    this.formData.signinInterval = String(this.formData.signinInterval);
                    this.formData.timeoutThreshold = String(this.formData.timeoutThreshold);
                    this.formData.todayTimeoutCount = String(this.formData.todayTimeoutCount);
                    this.signinTime = [res.data[0].signinStartTime, res.data[0].signinEndTime];
                }
            })
        },


    }
}
</script>

<style scoped lang="less">
.form-container {
    width: 80%;
    margin: 0 auto;
}
</style>