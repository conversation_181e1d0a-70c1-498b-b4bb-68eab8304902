<template>
    <div>
        <s-DataGrid ref="grid" funcMark="sn-mjzb-lbcx" :customFunc="true">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" icon="md-add" @click.native="handleAdd()"
                    v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:cj')">自定义创建模板</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Button type="primary"  v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:xq')" size="small"
                    style="margin-right: 10px;" @click="toDetail(row.id)">详情</Button>
                <Button type="primary" size="small"
                    v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:qy') && row.status == 0"
                    style="margin-right: 10px;" @click="toSwitch(row.id)">启用</Button>
                <Button type="primary" size="small"
                    v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:bj') && row.status == 0"
                    style="margin-right: 10px;" @click="handleAdd(row.id, 'edit')">编辑</Button>
                <Button type="primary" size="small"
                    v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:ddgz')"
                    style="margin-right: 10px;" @click="handleSupervise(row)">督导规则</Button>
                <Button type="error" size="small"
                    v-if="func.includes(globalAppCode + ':sn-mjzb-lbcx:sc') && row.status == 0"
                    @click.native="handleDel(row)">删除</Button>
            </template>
        </s-DataGrid>
        <add-model ref="addModel" :optType="optType" @on-close="on_refresh_table"></add-model>
        <Modal ref="detModel" title="详情" width="60%" v-model="showDetModel">
            <shiftTemplateDetail ref="shiftTemplateDetail"></shiftTemplateDetail>
            <div slot="footer"></div>
        </Modal>

        <Modal ref="supervise" title="督导规则配置" width="60%" v-model="showSupervise">
            <superviseModel ref="superviseModel" @on-close="closeSupervise" :row="dataMsg"></superviseModel>
            <div class="com-modal-submit" slot="footer">
                <Button @click="closeSupervise" class="save">关 闭</Button>
                <Button type="primary" @click="confirmSupervise" class="save">确 定</Button>
            </div>
        </Modal>
        <!--  -->
    </div>
</template>

<script>
import { mapActions } from "vuex";
import addModel from './addModel.vue'
import shiftTemplateDetail from "./shiftTemplateDetail.vue";
import superviseModel from "./superviseModel.vue";
export default {
    name: 'dutyModel',
    components: {
        addModel,
        shiftTemplateDetail,
        superviseModel
    },

    props: {
    },
    data() {
        return {
            showDetModel: false,
            optType: "",
            showSupervise: false, // 是否显示督导规则配置
            dataMsg: {},

        }
    },
    methods: {
        ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
        handleAdd(id, optType = "add") {
            this.optType = optType;
            this.$refs.addModel.open(id)

        },
        toDetail(id) {
            this.showDetModel = true

            this.$refs.shiftTemplateDetail.getTempInfo(id)
        },
        handleDel(row) {
            let list = [row.id]
            console.log(list);
            this.$Modal.confirm({
                title: '删除',
                content: '请确认是否删除？',
                onOk: () => {
                    this.authPostRequest({ url: this.$path.zh_TemplateDelete, params: { ids: list } }).then(res => {
                        if (res.success) {
                            this.$Message.success('删除成功')
                            this.on_refresh_table()
                        } else {
                            this.$Message.error(res.message)
                        }
                    })
                }
            })
        },
        toSwitch(id) {
            this.authGetRequest({ url: this.$path.zh_TempOpenCheck, params: { id } }).then(res => {
                if (!res.data) return this.changeStatus(id);
                this.$Modal.confirm({
                    title: '提醒',
                    content: '启用模板将清除今后的排班数据，是否确定启用?',
                    onOk: () => {
                        this.changeStatus(id)
                    }
                })
            })
        },
        changeStatus(id) {
            let param = {
                id,
                status: 1,
            };
            this.authGetRequest({ url: this.$path.zh_TempOpen, params: param }).then(res => {
                if (res.success) {
                    this.$Message.success('状态切换成功')
                    this.on_refresh_table()
                } else {
                    this.$Message.error(res.message)
                }
            })
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        },
        handleSupervise(row) {
            this.showSupervise = true
            this.dataMsg = row
            this.$refs.superviseModel.getSuperviseRule(row.id)
        },
        confirmSupervise() {
            this.$refs.superviseModel.confirmSupervise()
        },
        closeSupervise() {
            this.showSupervise = false
            this.$refs.superviseModel.refresh()
        }

    },
    mounted() {



    },

}


</script>


<style scoped lang="less">
.addModel {


    .com-modal-height-limit {

        // max-height: 75vh;
        // overflow: auto;
        .btn-add {
            text-align: center;
            font-size: 16px;
            padding: 10px 0;
            border: 1px dashed #2390ff;
            color: #2390ff;
            margin: 15px 0;
            cursor: pointer;
        }
    }

    /deep/.ivu-modal-body {
        // min-height: 500px !important;
        // overflow: auto;
        max-height: 75vh;
        overflow: auto;
    }

    /deep/.setting-container {
        padding: 15px;
        background-color: #e8f5ff;
        margin-bottom: 10px;

        .item-box {
            border: 1px solid #dcdcdc;
            background-color: #fff;
            margin-bottom: 10px;
            position: relative;

            .item-trash {
                position: absolute;
                font-size: 32px;
                right: 10px;
                top: 10px;
                color: #2390ff;
                cursor: pointer;
            }
        }

        .post {
            font-size: 16px;
            font-weight: bold;
            padding: 10px;
            // color: #000;
        }
    }
}
</style>
