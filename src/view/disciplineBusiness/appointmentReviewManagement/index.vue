<template>
  <div class="punishment-management">
    <div class="bsp-base-content">
      <s-DataGrid
        ref="grid"
        funcMark="szpt-gjyw-yyshgl-lb"
        :customFunc="true"
      >
        <template slot="customRowFunc" slot-scope="{ func, row }">
          <Button
            type="primary"
            v-if="
              func.includes(globalAppCode + ':szpt-gjyw-yyshgl-lb:tg') && row.status == '1'
            "
            style="margin-right: 5px"
            @click="handleUpdate(row, '同意')"
            >通过</Button
          >
          <Button
            type="error"
            ghost
            v-if="
              func.includes(globalAppCode + ':szpt-gjyw-yyshgl-lb:butg') && row.status == '1'
            "
            style="margin-right: 5px"
            @click="handleUpdate(row, '不同意')"
            >不予通过</Button
          >
          <Button
            type="primary"
            v-if="
              func.includes(globalAppCode + ':szpt-gjyw-yyshgl-lb:xq') && row.status !== '1'
            "
            style="margin-right: 5px"
            @click="toDetail(row)"
            >详情</Button
          >
        </template>
      </s-DataGrid>
    </div>
  </div>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
import { mapActions } from "vuex";
import api from "./api.js";
export default {
  components: {
    sDataGrid,
  },
  data() {
    return {};
  },
  methods: {
    ...mapActions(["authPostRequest"]),
    handleUpdate(row, type) {
      this.loading = true;
      const { jgrybm, id } = row;
      this.authPostRequest({
        url: api.check,
        params: {
          id,
          jgrybm,
          approvalComments: type,
          approvalResult: type === "同意" ? 5 : 6,
        },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Message.success("操作成功，操作结果为：【" + type + "】")
          this.refreshTable();
        } else {
          this.errorModal({ content: res.msg || "操作失败" });
        }
      });
    },
    toDetail(row) {
      this.$router.replace({
        path: `/discipline/appointmentReviewManagement/detail?id=${row.id}`,
      });
    },
    refreshTable() {
      this.$refs.grid.query_grid_data(1);
    },
  },
};
</script>

<style scoped lang="less"></style>
