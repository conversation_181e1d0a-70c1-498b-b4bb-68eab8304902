<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      预约审核项配置
    </div>
    <div style="padding: 20px">
      <Table style="margin-top: 15px" :columns="[
        { title: '预约类别', key: 'bookingCategoryName' },
        { title: '服务类别', key: 'serviceCategoryName' },
        { title: '操作', key: 'isEnabled', slot: 'action' },
      ]" :data="list">
        <template slot-scope="{ row }" slot="action">
          <i-switch :value="row.isEnabled" :true-value="1" :false-value="0"
            @on-change="handleChange(row.isEnabled, row)" size="large">
            <span slot="open">开启</span>
            <span slot="close">关闭</span>
          </i-switch>
        </template>
      </Table>
    </div>
    <div class="bsp-base-fotter">
      <Button @click="goList">返 回</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      loading: false,
      list: [],
    };
  },
  computed: {},
  created() {
    this.getList();
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    getList() {
      this.loading = true;
      this.authPostRequest({
        url: api.list,
        params: {},
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.list = res.data;
        }
      });
    },
    handleChange(e, row) {
      console.log(111, e);
      this.authPostRequest({
        url: api.update,
        params: [
          {
            ...row,
            isEnabled: e === 1 ? 0 : 1,
          },
        ],
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.getList();
        }
      });
    },
    goList() {
      this.$router.replace({ name: "appointmentReviewManagement" });
    },
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
