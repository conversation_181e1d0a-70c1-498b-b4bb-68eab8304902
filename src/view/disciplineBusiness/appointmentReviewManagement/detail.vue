<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div
      class="title"
      style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      "
    >
      预约审核详情
    </div>
    <div>
      <div class="list-title">预约信息</div>
      <el-descriptions class="margin-top" :column="2" size="small" border>
        <el-descriptions-item label="发起人" :labelStyle="{ width: '8em' }">
          {{ detail.jgryxm }}
        </el-descriptions-item>
        <el-descriptions-item label="人员编码">
          {{ detail.jgrybm }}
        </el-descriptions-item>
        <el-descriptions-item label="监室号" :labelStyle="{ width: '8em' }">
          {{ detail.roomId }}
        </el-descriptions-item>
        <el-descriptions-item label="监室名称" :labelStyle="{ width: '8em' }">
          {{ detail.roomName }}
        </el-descriptions-item>
        <el-descriptions-item label="预约类别">
          {{ detail.bookingCategoryName }}
        </el-descriptions-item>
        <el-descriptions-item label="服务类别">
          {{ detail.serviceCategoryName }}
        </el-descriptions-item>
        <el-descriptions-item :span="2" label="发起时间">
          {{ detail.applicationTime }}
        </el-descriptions-item>
        <el-descriptions-item label="入所时间">
          {{ detail.rssj || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="审核结果">
          {{ detail.approvalResultName }}
        </el-descriptions-item>
        <el-descriptions-item label="审核意见">
          {{ detail.approvalComments }}
        </el-descriptions-item>
        <el-descriptions-item label="审核人">
          {{ detail.approverXm }}
        </el-descriptions-item>
        <el-descriptions-item label="审核时间">
          {{ detail.approverTime }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="bsp-base-fotter">
      <Button @click="goList">返 回</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      loading: false,
      detail: {},
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions(["authGetRequest"]),
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "appointmentReviewManagement" });
    },
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
