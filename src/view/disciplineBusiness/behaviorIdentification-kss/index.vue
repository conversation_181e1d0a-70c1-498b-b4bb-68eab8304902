<template>
  <s-DataGrid ref="grid1" funcMark="rybxjd-kss" :customFunc="true">
    <template slot="customHeadFunc" slot-scope="{ func }">
      <Button v-if="func.includes(globalAppCode + ':rybxjd-kss:add')" type="primary"
        @click.native="createApply()">人员表现鉴定呈批</Button>
    </template>
    <template slot="customRowFunc" slot-scope="{ func, row }">
      <Button v-if="func.includes(globalAppCode + ':rybxjd-kss:sp') && row.status === '01'" type="primary"
        @click="handleCheck(row)">审批</Button>
      <Button v-if="func.includes(globalAppCode + ':rybxjd-kss:xq')" type="primary" @click="toDetail(row)">详情</Button>
      <Button v-if="func.includes(globalAppCode + ':rybxjd-kss:dy')" type="primary"
        @click="toPrint(row)">预览打印</Button>
    </template>
  </s-DataGrid>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
export default {
  components: {
    sDataGrid,
  },
  data() {
    return {
    };
  },
  created() { },
  methods: {
    createApply() {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/create`,
      });
    },
    handleCheck(row) {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/check?id=${row.id}&jgrybm=${row.jgrybm}`,
      });
    },
    toDetail(row) {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/detail?id=${row.id}&jgrybm=${row.jgrybm}`,
      });
    },
    toPrint(row) {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/print?id=${row.id}&jgrybm=${row.jgrybm}`,
      });
    }
  },
};
</script>

<style scoped lang="less"></style>
