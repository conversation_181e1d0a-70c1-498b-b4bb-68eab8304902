<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <SinglePDFViewer ref="pdfViewer" title="被监管人员羁押表现告知书"
      :key="routerData.id" formId="1940683204616916992" :businessId="routerData.id">
      <template slot="action">
        <Button @click="goList()">返回</Button>
        <Button :loading="loading" style="margin-left: 10px;" type="primary" @click="handlePrint()">打印</Button>
      </template>
    </SinglePDFViewer>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { SinglePDFViewer } from "@/components/index.js";
export default {
  components: {
    SinglePDFViewer
  },
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
        id: "",
      },
      detail: {},
    };
  },
  computed: {
  },
  created() {
    this.routerData = {
      ...this.$route.query,
    };
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    handlePrint() {
      this.$refs.pdfViewer.print();
    },
    goList() {
      this.$router.replace({ name: "behaviorIdentificationKssList" });
    },
    toPrint() {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/print?id=${this.routerData.id}&jgrybm=${this.routerData.jgrybm}`,
      });
    }
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
