<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      人员表现鉴定审批
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container"
        style="padding: 10px;padding-bottom: 60px; display: flex; flex-direction: column; flex: 1">
        <div class="list-title">
          健康情况
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="身体状况" :labelStyle="{ width: '14em' }">
            {{ detail.healthStzk === "1" ? "健康" : "不健康" }}
          </el-descriptions-item>
          <el-descriptions-item label="外伤情况" :labelStyle="{ width: '8em' }">
            {{ detail.healthWsqk === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="重大疾病">
            {{ detail.healthZdjb === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="精神状况">
            {{ detail.healthJszk === "1" ? "良好" : "不好" }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="重大疾病及出所住院原因">
            {{ detail.healthZdjbjcszyyy }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          关押期间表现
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="所规所纪制度" :labelStyle="{ width: '10em' }">
            {{ detail.performanceSgsjzd === "1" ? "遵守" : "不遵守" }}
          </el-descriptions-item>
          <el-descriptions-item label="一日生活管理" :labelStyle="{ width: '12em' }">
            {{ detail.healthJszk === "1" ? "服从" : "不服从" }}
          </el-descriptions-item>
          <el-descriptions-item label="自伤自残行为或倾向">
            {{ detail.healthJszk === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="殴打他人行为">
            {{ detail.performanceOdtrxw === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="曾列为重大安全风险情况">
            {{ riskSituation.join(" , ") }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          家属告知联系情况
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="家属姓名及电话" :labelStyle="{ width: '10em' }">
            {{ detail.familyXmjdh }}
          </el-descriptions-item>
          <el-descriptions-item label="羁押期间联系家属情况" :labelStyle="{ width: '12em' }">
            {{ detail.familyJyqjlxjsqk === "1" ? "联系过" : "未联系" }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          接收信息登记
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item :labelStyle="{ width: '7em' }" label="接收单位">
            {{ detail.jsdw }}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle="{ width: '7em' }" label="移送因由">
            {{ detail.ysyyName }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="其他情况">
            {{ detail.qtqk }}
          </el-descriptions-item>
          <el-descriptions-item label="呈批人">
            {{ detail.addUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="呈批时间">
            {{ detail.addTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="toList">返 回</Button>
      <Button type="primary" @click="handleCheck">审 批</Button>
    </div>
    <Modal v-model="modalVisible" :loading="loading" title="审批" @on-ok="handleSubmit" @on-cancel="handleCancel">
      <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="120" label-position="left"
        @submit.native.prevent>
        <Row>
          <Col :span="24">
          <FormItem label="审批结果" prop="status">
            <RadioGroup v-model="formData.status" @on-change="handleRadioChange">
              <Radio label="02">通过</Radio>
              <Radio label="03">不通过</Radio>
            </RadioGroup>
          </FormItem>
          </Col>
          <Col :span="24">
          <FormItem label="审批意见" prop="approvalComments">
            <Input v-model="formData.approvalComments" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
              placeholder="请填写具体评估理由"></Input>
          </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      loading: false,
      formData: {
        status: '02',
        approvalComments: "同意",
      },
      modalVisible: false,
      routerData: {
        jgrybm: "",
        id: "",
      },
      ruleValidate: {
        status: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
        approvalComments: [
          {
            required: true,
            message: "该项为必填项",
            trigger: "blur",
          },
        ],
      },
      detail: {},
    };
  },
  computed: {
    riskSituation() {
      let result = [
        this.detail.performanceZdaqfxqk1 === "1" ? "一般" : "",
        this.detail.performanceZdaqfxqk2 === "1" ? "三级" : "",
        this.detail.performanceZdaqfxqk3 === "1" ? "二级" : "",
        this.detail.performanceZdaqfxqk4 === "1" ? "一级" : ""];
      return result.filter(item => !!item);
    }
  },
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    handleRadioChange(e) {
      this.formData.approvalComments = e === '02' ? "通过" : "不通过";
    },
    toList() {
      this.$router.replace({ name: "behaviorIdentificationKssList" });
    },
    handleCheck() {
      this.modalVisible = true;
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.modalVisible = false;
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = { ...this.routerData, ...this.formData };
          this.authPostRequest({
            url: api.approval,
            params,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("已成功审批");
              this.$router.replace({ name: "behaviorIdentificationKssList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
            this.modalVisible = false;
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.fixPersonelStyle {
  /deep/ .flex-box {
    margin-bottom: 16px;

    .detail-title {
      margin-bottom: unset;
      line-height: 1.8;
      height: unset;
    }

    .ivu-btn {
      margin-top: 0 !important;
    }
  }
}

.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
