<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      人员表现鉴定详情
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px;padding-bottom: 60px; display: flex; flex-direction: column; flex: 1">
        <div class="list-title">
          健康情况
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="身体状况" :labelStyle="{ width: '14em' }">
            {{ detail.healthStzk === "1" ? "健康" : "不健康" }}
          </el-descriptions-item>
          <el-descriptions-item label="外伤情况" :labelStyle="{ width: '10em' }">
            {{ detail.healthWsqk === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="重大疾病">
            {{ detail.healthZdjb === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="精神状况">
            {{ detail.healthJszk === "1" ? "良好" : "不好" }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="重大疾病及出所住院原因">
            {{ detail.healthZdjbjcszyyy }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          关押期间表现
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="所规所纪制度" :labelStyle="{ width: '14em' }">
            {{ detail.performanceSgsjzd === "1" ? "遵守" : "不遵守" }}
          </el-descriptions-item>
          <el-descriptions-item label="一日生活管理" :labelStyle="{ width: '10em' }">
            {{ detail.healthJszk === "1" ? "服从" : "不服从" }}
          </el-descriptions-item>
          <el-descriptions-item label="自伤自残行为或倾向">
            {{ detail.healthJszk === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="殴打他人行为">
            {{ detail.performanceOdtrxw === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="曾列为重大安全风险情况">
            {{ riskSituation.join(" , ") }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          家属告知联系情况
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="家属姓名及电话" :labelStyle="{ width: '14em' }">
            {{ detail.familyXmjdh }}
          </el-descriptions-item>
          <el-descriptions-item label="羁押期间联系家属情况" :labelStyle="{ width: '14em' }">
            {{ detail.familyJyqjlxjsqk === "1" ? "联系过" : "未联系" }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          接收信息登记
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item :labelStyle="{ width: '14em' }" label="接收单位">
            {{ detail.jsdw }}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="移送因由">
            {{ detail.ysyyName }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label="其他情况">
            {{ detail.qtqk }}
          </el-descriptions-item>
          <el-descriptions-item label="呈批人">
            {{ detail.addUserName }}
          </el-descriptions-item>
          <el-descriptions-item label="呈批时间">
            {{ detail.addTime }}
          </el-descriptions-item>
        </el-descriptions>
        <div v-if="detail.approverTime" class="list-title" style="border-top: 0;">
          审批信息
        </div>
        <el-descriptions v-if="detail.approverTime" :column="2" size="small" border>
          <el-descriptions-item :labelStyle="{ width: '14em' }" label="审批结果">
            {{ detail.statusName }}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="审批意见">
            {{ detail.approvalComments || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="审批人">
            {{ detail.approverXm }}
          </el-descriptions-item>
          <el-descriptions-item label="审批时间">
            {{ detail.approverTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="goList">返 回</Button>
      <Button @click="toPrint" type="primary">鉴定表预览打印</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
        id: "",
        type: ""
      },
      detail: {},
    };
  },
  computed: {
    riskSituation() {
      let result = [
        this.detail.performanceZdaqfxqk1 === "1" ? "一般" : "",
        this.detail.performanceZdaqfxqk2 === "1" ? "三级" : "",
        this.detail.performanceZdaqfxqk3 === "1" ? "二级" : "",
        this.detail.performanceZdaqfxqk4 === "1" ? "一级" : ""];
      return result.filter(item => !!item);
    }
  },
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "behaviorIdentificationKssList" });
    },
    toPrint() {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-kss/print?id=${this.routerData.id}&jgrybm=${this.routerData.jgrybm}`,
      });
    }
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
