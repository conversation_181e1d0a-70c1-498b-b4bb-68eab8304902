<template>
  <s-DataGrid ref="grid1" funcMark="kss-qlywgzs" :customFunc="true">
    <template slot="customHeadFunc" slot-scope="{ func }">
      <!-- <Button v-if="func.includes(globalAppCode + ':gjyw-fxpg-dpgsj:xzpgdj')" type="primary" -->
      <Button type="primary" :disabled="checkedItems.length > 0" @click.native="handleExport()">批量导出</Button>
    </template>
    <template slot="customRowFunc" slot-scope="{ func, row }">
      <Button v-if="func.includes(globalAppCode + ':kss-qlywgzs:xq')" type="primary" @click="toDetail(row)">详情</Button>
      <Button v-if="func.includes(globalAppCode + ':kss-qlywgzs:dc')" type="primary"
        @click="handleExport(row)">导出</Button>
    </template>
  </s-DataGrid>
</template>

<script>
import { sDataGrid } from "sd-data-grid";
export default {
  components: {
    sDataGrid,
  },
  data() {
    return {
      checkedItems: []
    };
  },
  created() {
  },
  methods: {
    handleExport(row) {
      this.$Message.info("此功能正在加急开发中。")
    },
    toDetail(row) {
      this.$router.replace({
        path: `/discipline/noticeOfRightsAndObligations-kss/detail?jgrybm=${row.jgrybm}`,
      });
    },
  },
};
</script>

<style scoped lang="less">
</style>
