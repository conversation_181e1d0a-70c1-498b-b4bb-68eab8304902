<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      权利义务告知详情
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="display: flex; flex-direction: column; flex: 1">
        <SinglePDFViewer ref="pdfViewer" title="在押人员权利和义务告知书"
          :key="businessId" formId="1940312854863417344" :businessId="businessId">
          <template slot="action">
            <Button @click="goList()">返回</Button>
            <Button :loading="loading" style="margin-left: 10px;" type="primary" @click="handlePrint()">打印</Button>
          </template>
        </SinglePDFViewer>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import { SinglePDFViewer } from "@/components/index.js";
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {
    SinglePDFViewer
  },
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
      },
      detail: {},
      businessId: ""
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getBusinessId();
    this.getDetail();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    getBusinessId() {
      this.loading = true;
      this.authGetRequest({
        url: api.getBusinessId,
        params: { jgrybm: this.routerData.jgrybm },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.businessId = res.data;
        }
      });
    },
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "noticeOfRightsAndObligationsKssList" });
    },
    handlePrint() {
      this.$refs.pdfViewer.print();
    },
  },
};
</script>

<style lang="less" scoped>
.fixPersonelStyle {
  /deep/ .flex-box {
    margin-bottom: 16px;

    .detail-title {
      margin-bottom: unset;
      line-height: 1.8;
      height: unset;
    }

    .ivu-btn {
      margin-top: 0 !important;
    }
  }
}

.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
