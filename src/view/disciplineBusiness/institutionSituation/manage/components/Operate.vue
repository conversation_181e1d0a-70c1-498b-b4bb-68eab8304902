<template>
  <Modal fullscreen v-model="visible" v-loading="loading" :title="title" @on-ok="handleSubmit"
    @on-cancel="handleCancel">
    <Form :model="formData" ref="form" :key="id" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
      <div>
        <div class="form-title">所情事件配置</div>
        <Row>
          <Col :span="24">
          <FormItem label="事件名称" prop="typeName">
            <Input v-model="formData.typeName" placeholder="请输入事件名称" style="width: 200px" :disabled="disabled">
            </Input>
          </FormItem>
          </Col>
          <Col :span="24">
          <FormItem label="事件内容" prop="approvalResult">
            <div :class="['action-cont', disabled ? 'disabled' : '']">
              <div class="level1" v-for="(item, index) in formData.eventItemList" :key="index">
                <div class="input-box">
                  <Input v-model="item.itemName" :readonly="disabled" :max-length="100"
                    placeholder="请输入事件内容，最长100个字符"></Input>
                  <span class="kf-cls" v-if="!item.childList || item.childList.length == 0">
                    违规扣分：
                    <Input type="number" class="input-cls" v-model="item.deductPoint" :readonly="disabled"></Input>分
                  </span>
                  <Icon :size="22" title="删除该事件"
                    :style="{ color: disabled ? 'grey' : 'red', cursor: disabled ? 'not-allowed' : 'pointer', marginTop: '5px' }"
                    type="md-close-circle" @click="handleEventDelete(1, formData.eventItemList, index)" />
                </div>
                <template v-if="item.childList && item.childList.length > 0">
                  <div v-for="(citem, cindex) in item.childList" :key="cindex" class="level2 ">
                    <div class="input-box">
                      <Input v-model="citem.itemName" :readonly="disabled" :max-length="100"
                        placeholder="请输入事件内容，最长100个字符"></Input>
                      <span class="kf-cls" v-if="!citem.childList || citem.childList.length == 0">
                        违规扣分：
                        <Input type="number" class="input-cls" v-model="citem.deductPoint"
                          :readonly="disabled"></Input>分
                      </span>
                      <Icon :size="22" title="删除该事件"
                        :style="{ color: disabled ? 'grey' : 'red', cursor: disabled ? 'not-allowed' : 'pointer', marginTop: '5px' }"
                        type="md-close-circle" @click="handleEventDelete(2, item, cindex)" />
                    </div>
                    <template v-if="citem.childList && citem.childList.length > 0">
                      <div v-for="(ccitem, ccindex) in citem.childList" :key="ccindex" class="level3 ">
                        <div class="input-box">
                          <Input v-model="ccitem.itemName" :readonly="disabled" :max-length="100"
                            placeholder="请输入事件内容，最长100个字符"></Input>
                          <span class="kf-cls">
                            违规扣分：
                            <Input type="number" class="input-cls" v-model="ccitem.deductPoint"
                              :readonly="disabled"></Input> 分
                          </span>
                          <Icon :size="22" title="删除该事件"
                            :style="{ color: disabled ? 'grey' : 'red', cursor: disabled ? 'not-allowed' : 'pointer', marginTop: '5px' }"
                            type="md-close-circle" @click="handleEventDelete(3, citem, ccindex)" />
                        </div>
                      </div>
                    </template>
                    <div style="margin-top: 10px;" v-if="!disabled">
                      <Button @click="handleEventAdd(3, citem)" type="text" style="color: #2390ff;" icon="ios-add"
                        ghost>新增三级事件</Button>
                    </div>
                  </div>
                </template>
                <div style="margin-top: 10px;" v-if="!disabled">
                  <Button type="text" @click="handleEventAdd(2, item)" style="color: #2390ff;" icon="ios-add"
                    ghost>新增二级事件</Button>
                </div>
              </div>
              <div v-if="!disabled" style="margin-top: 10px;border:1px dashed #2390ff"><Button type="primary"
                  @click="handleEventAdd(1, formData.eventItemList)" icon="ios-add" ghost long>新增一级事件</Button>
              </div>
            </div>
          </FormItem>
          </Col>
        </Row>
      </div>
      <div>
        <div class="form-title" style="margin-top: 25px;">处置模板</div>
        <Row :gutter="16" v-for="(item, index) in formData.disposeTemplateList" :key="index">
          <Col :span="6">
          <FormItem label="" prop="templateName" :labelWidth="0">
            <Input v-model="item.templateName" placeholder="请输入模板名称" :disabled="disabled">
            </Input>
          </FormItem>
          </Col>
          <Col :span="17">
          <FormItem label="" prop="templateContent" :labelWidth="0">
            <Input v-model="item.templateContent" placeholder="请输入模板内容" :disabled="disabled">
            </Input>
          </FormItem>
          </Col>
          <Col :span="1">
          <Icon :size="22" title="删除该处置模板"
            :style="{ color: disabled ? 'grey' : 'red', cursor: disabled ? 'not-allowed' : 'pointer', marginTop: '5px' }"
            type="md-close-circle" @click="handleTemplateDelete(index)" />
          </Col>
        </Row>
        <div v-if="!disabled" style="border:1px dashed #2390ff" @click="handleTemplateAdd"><Button ghost type="primary"
            icon="ios-add" long>新增处置模板</Button></div>
        <noData v-if="disabled && formData.disposeTemplateList.length === 0" />
      </div>
      <div>
        <div class="form-title" style="margin-top: 25px;">推送岗位</div>
        <Row v-for="(item, index) in formData.pushSettingList" :key="item.id"
          style="margin-top: 15px; border-bottom: 1px solid #ebebeb;">
          <Col :span="23">
          <Col :span="24">
          <FormItem label="推送对象" prop="pushPostId">
            <s-dicgrid placeholder="请选择关联业务" :disabled="disabled" v-model="item.pushPostId" ref="dicGrid"
              style="width: 200px" dicName="ZD_POST" filterField="02,03,04,05,06,07" />
          </FormItem>
          </Col>
          <!-- 只有管教岗才有关联业务配置，code是字典配的 -->
          <Col :span="24" v-if="item.pushPostId === '03'">
          <FormItem label="关联业务" prop="disposeBusiness">
            <s-dicgrid placeholder="请选择关联业务" :disabled="disabled" :multiple="true" v-model="item.disposeBusiness"
              ref="dicGrid" dicName="ZD_JJKS_GLYW" />
          </FormItem>
          </Col>
          <Col :span="24">
          <FormItem label="处置预案" prop="disposePlans">
            <Input :disabled="disabled" v-model="item.disposePlans" type="textarea"
              :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请填写处置预案"></Input>
          </FormItem>
          </Col>
          </Col>
          <Col :span="1" style="display: flex;justify-content: center; align-items: center;">
          <Icon :size="22" title="删除该推送岗位"
            :style="{ color: disabled ? 'grey' : 'red', cursor: disabled ? 'not-allowed' : 'pointer', marginTop: '5px' }"
            type="md-close-circle" @click="handlePushDelete(index)" />
          </Col>
        </Row>
        <div v-if="!disabled" style="border:1px dashed #2390ff" @click="handlePushAdd"><Button type="primary"
            icon="ios-add" ghost long>新增推送对象配置</Button></div>
        <noData v-if="disabled && formData.pushSettingList.length === 0" />
      </div>
    </Form>
  </Modal>
</template>

<script>
import noData from "@/components/bsp-empty/index.vue";
import { mapActions } from "vuex";
import api from "../api";
export default {
  name: "institutionSituationManageOperate",
  components: {
    noData
  },
  props: {},
  data() {
    return {
      formData: {
        // 处置模板list
        disposeTemplateList: [
          {
            id: "",
            orderId: "1",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
          {
            id: "",
            orderId: "2",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
          {
            id: "",
            orderId: "3",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
        ],
        typeName: "",
        // 事件明细
        eventItemList: [
          {
            id: "",
            orderId: 1,
            levelCode: 1,
            itemName: "",
            childList: [
              {
                orderId: 2,
                levelCode: 2,
                itemName: "",
                childList: [
                  {
                    orderId: 1,
                    levelCode: 3,
                    itemName: "",
                    deductPoint: null,
                  },
                ],
              },
            ],
          },
        ],
        id: "",
        // 推送岗位
        pushSettingList: [
          {
            id: "",
            typeId: "",
            orderId: "0",
            pushPostId: "",
            disposeBusiness: "",
            disposePlans: "",
          },
        ],
        isEnabled: 1
      },
      ruleValidate: {},
      disabled: false,
      visible: false,
      title: "",
      loading: false,
      type: "",
      id: ""
    };
  },
  created() {
  },
  mounted() { },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    open(type, title, data) {
      this.title = title;
      this.type = type;
      this.disabled = type === 'detail';
      this.visible = true;
      this.id = data ? data.id : Math.random();
      !!data && this.getDetail(data.id);
    },
    getDetail(id) {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id }
      }).then(res => {
        this.loading = false;
        if (res.success) {
          Object.assign(this.formData, res.data);
        } else {
          this.visible = false;
          this.$Message.error("获取所情类型配置失败");
        }
      })
    },
    handleEventAdd(type, pItem) {
      let obj = {
        id: "",
        levelCode: type, // 级别代码（1：一级，2：二级，3：三级）
        itemName: "",
        childList: [],
        deductPoint: null, // 扣分值
        orderId: "", // 排序
      };
      switch (type) {
        case 1:
          pItem.push(obj);
          break;
        case 2:
        case 3:
          pItem.childList.push(obj);
          break;
      }
    },
    handleEventDelete(type, item, index) {
      if (this.disabled) {
        return
      }
      this.$Modal.confirm({
        title: "提示",
        content: "是否确认删除当前数据以及该节点下所有下级内容?",
        onOk: () => {
          switch (type) {
            case 1:
              item.splice(index, 1);
              break;
            case 2:
            case 3:
              item.childList.splice(index, 1);
              break;
          }
        },
      });
    },
    handleTemplateAdd() {
      this.formData.disposeTemplateList.push({
        id: "",
        orderId: "" + this.formData.disposeTemplateList.length,
        templateContent: "",
        templateName: "",
        typeId: "",
      });
    },
    handleTemplateDelete(index) {
      this.$Modal.confirm({
        title: "提示",
        content: "是否确认删除该内容?",
        onOk: () => {
          this.formData.disposeTemplateList.splice(index, 1);
        },
      });
    },
    handlePushAdd() {
      this.formData.pushSettingList.push({
        id: "",
        typeId: "",
        orderId: "",
        pushPostId: "",
        disposeBusiness: "",
        disposePlans: "",
      });
    },
    handlePushDelete(index) {
      this.$Modal.confirm({
        title: "提示",
        content: "是否确认删除该内容?",
        onOk: () => {
          this.formData.pushSettingList.splice(index, 1);
        },
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
      this.formData = {
        // 处置模板list
        disposeTemplateList: [
          {
            id: "",
            orderId: "1",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
          {
            id: "",
            orderId: "2",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
          {
            id: "",
            orderId: "3",
            templateContent: "",
            templateName: "",
            typeId: "",
          },
        ],
        typeName: "",
        // 事件明细
        eventItemList: [
          {
            id: "",
            orderId: 1,
            levelCode: 1,
            itemName: "",
            childList: [
              {
                orderId: 2,
                levelCode: 2,
                itemName: "",
                childList: [
                  {
                    orderId: 1,
                    levelCode: 3,
                    itemName: "",
                    deductPoint: null,
                  },
                ],
              },
            ],
          },
        ],
        id: "",
        // 推送岗位
        pushSettingList: [
          {
            id: "",
            typeId: "",
            orderId: "0",
            pushPostId: "",
            disposeBusiness: "",
            disposePlans: "",
          },
        ],
        isEnabled: 1
      }
    },
    handleCancel() {
      if (!this.disabled) {
        this.resetForm();
      }
      this.visible = false;
      this.$emit("refresh");
    },
    handleSubmit() {
      if (this.disabled) {
        return this.handleCancel();
      } else {
        this.loading = true;
        this.formData.eventItemList = this.formData.eventItemList.map(item => { item.typeId = this.id; return item });
        this.authPostRequest({
          url: this.type === "add" ? api.create : api.update,
          params: this.formData
        }).then(res => {
          this.loading = false;
          if (res.success) {
            this.$Message.success("编辑所情类型配置成功");
            this.handleCancel();
          } else {
            this.$Message.error("编辑所情类型配置失败");
          }
        })
      }
    }
  }
};
</script>

<style lang="less" scoped>
.form-title {
  font-size: 16px;
  font-weight: bold;
  padding-left: 10px;
  position: relative;
  margin-bottom: 15px;

  &::before {
    content: "";
    width: 4px;
    background-color: #343cf3;
    position: absolute;
    left: 0;
    top: 10%;
    bottom: 10%;
  }
}

.action-cont {
  &.disabled {
    /deep/ .ivu-input {
      border: 1px dashed #343cf3;
      cursor: not-allowed
    }
  }

  max-height: 500px;
  width: 100%;
  position: relative;
  padding: 10px;
  overflow: auto;
  border: 1px solid #eee;

  .kf-cls {
    margin-left: 20px;
    display: flex;
    align-items: center;
    white-space: nowrap;

    .input-cls {
      width: 100px;
      margin-right: 4px;
    }
  }

  .level2:before,
  .level3:before {
    content: " ";
    width: 1px;
    position: absolute;
    top: 30px;
    bottom: 15px;
    border-right: 1px dotted #ccc;
  }

  .level2,
  .level3 {
    margin-top: 10px;
    margin-left: 30px;
    position: relative;
  }

  .level1 {
    margin-bottom: 10px;

    &:before {
      content: " ";
      width: 1px;
      position: absolute;
      top: 45px;
      bottom: 40px;
      border-right: 1px dotted #ccc;
    }
  }

  .input-box {
    display: flex;
    gap: 20px;
  }
}
</style>
