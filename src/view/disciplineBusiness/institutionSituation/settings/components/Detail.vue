<template>
  <Modal fullscreen v-model="visible" v-loading="loading" title="联动详情" @on-ok="handleSubmit" ok-text="返回" cancel-text>
    <div>
      <div class="form-title">基本配置</div>
      <el-descriptions :column="3" size="small" border>
        <el-descriptions-item label="所情来源" :labelStyle="{ width: '8em' }">
          {{ detail.name }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时效" :labelStyle="{ width: '8em' }">
          {{ detail.processingDuration }}
        </el-descriptions-item>
        <el-descriptions-item label="所情等级" :labelStyle="{ width: '8em' }">
          {{ detail.eventLevelName }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div style="margin-top: 20px;">
      <div class="form-title">联动配置</div>
      <el-descriptions :column="2" size="small" border>
        <el-descriptions-item :span="detail.tone ? 2 : 1" label="可选配置" :labelStyle="{ width: '8em' }">
          {{ realValue }}
        </el-descriptions-item>
        <el-descriptions-item v-if="!!detail.tone" label="所情提示音" :labelStyle="{ width: '8em' }">
          {{toneDicts?.find(item => item.code === detail.tone)}}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </Modal>
</template>

<script>
import api from "../api.js";
import { mapActions } from "vuex";
export default {
  name: "institutionSituationSettingsDetail",
  components: {},
  data() {
    return {
      visible: false,
      detail: {},
      settingsDicts: [],
      toneDicts: [],
      loading: false
    };
  },
  computed: {
    realValue() {
      const settings = this.detail.optionalSettings;
      const settingsList = typeof settings === 'string' ? settings.split(',') : [];
      const convertDictObj = {};
      this.settingsDicts.map(item => {
        convertDictObj[item.code] = item.name;
      })
      const result = settingsList.map(item => convertDictObj[item]).filter(item => !!item).join(" , ");
      return result;
    },
  },
  created() {
    this.getDict();
  },
  mounted() { },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    open(id) {
      this.visible = true;
      this.getDetail(id);
    },
    getDict() {
      // 警戒看守-联动可选配置字典
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_JJKS_LDKXPZ" },
      }).then((res) => {
        if (res.success) {
          this.settingsDicts = res.data;
        } else {
          this.$Message.error("获取联动可选配置字典失败");
        }
      });
      // 警戒看守-所情预警提示音字典
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: { dicName: "ZD_JJKS_SQYJTSY" },
      }).then((res) => {
        if (res.success) {
          this.toneDicts = res.data;
        } else {
          this.$Message.error("获取所情预警提示音字典失败");
        }
      });
    },
    getDetail(id) {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id }
      }).then(res => {
        this.loading = false;
        if (res.success) {
          this.detail = res.data;
        } else {
          this.$Message.error("获取报警联动设置详情失败");
        }
      })
    },
    handleSubmit() {
      this.visible = false;
    }
  }
};
</script>

<style lang="less" scoped>
.form-title {
  font-size: 16px;
  font-weight: bold;
  padding-left: 10px;
  position: relative;
  margin-bottom: 15px;

  &::before {
    content: "";
    width: 4px;
    background-color: #343cf3;
    position: absolute;
    left: 0;
    top: 10%;
    bottom: 10%;
  }
}
</style>
