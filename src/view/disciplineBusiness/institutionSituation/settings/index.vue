<template>
  <div>
    <div>
      <Form :model="formData" ref="form" :label-width="80" @submit.native.prevent>
        <div style="padding:10px; border: 1px solid #efefef; display: flex; flex-direction: row;">
          <FormItem label="事件名称" prop="name" style="margin-bottom: 0;">
            <Input v-model="formData.name" placeholder="请输入事件名称"></Input>
          </FormItem>
          <div style="margin-left: 15px;">
            <Button type="primary" ghost @click="handleReset">重置</Button>
            <Button style="margin-left: 10px;" type="primary" @click="handleSearch">搜索</Button>
          </div>
        </div>
      </Form>
    </div>
    <div class="item-box">
      <div v-for="item in list" :key="item.id"
        style="border:1px solid #efefef; padding: 10px;border-radius: 5px;width: 300px">
        <div style="display: flex; justify-content: space-between;">
          <h3>{{ item.name }}</h3>
          <div style="display: flex; align-items: center;">
            <span :style="{ color: item.enabled === 1 ? 'green' : 'red', marginRight: '15px' }">已{{ item.enabled
              ===
              1 ? '开启' : '关闭' }}</span>
            <i-switch size="small" :true-value="1" :false-value="0" :value="item.enabled"
              @on-change="handleStatusChange(item)"></i-switch>
          </div>
        </div>
        <div style="display: flex; margin: 15px 0;">
          <div style="margin-right: 25px;">
            <Icon :size="48" style="color: #69beff" type="md-notifications" />
          </div>
          <div>
            <p>所情等级：{{ item.eventLevelName }}</p>
            <p>处理时效：{{ item.processingDuration }}分钟</p>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" @click="toDetail(item.id)">查看</Button>
          <Button type="primary" @click="toUpdate(item.id)">修改</Button>
        </div>
      </div>
    </div>
    <Update ref="updateModal" @refresh="refresh" />
    <Detail ref="detailModal" />
  </div>
</template>

<script>
import api from "./api.js";
import { Update, Detail } from "./components/index.js";
import { mapActions } from "vuex";
export default {
  name: "institutionSituationMange",
  components: {
    Update, Detail
  },
  data() {
    return {
      formData: {
        name: ""
      },
      list: [],
    };
  },
  created() {
    this.getList()
  },
  mounted() { },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    refresh() {
      this.getList()
    },
    getList(params) {
      this.loading = true;
      this.authPostRequest({
        url: api.list,
        params: params || {}
      }).then(res => {
        this.loading = false;
        if (res.success) {
          this.list = res.data;
        } else {
          this.$Message.error("获取所情类型配置失败");
        }
      })
    },
    handleReset() {
      this.formData.name = "";
      this.getList();
    },
    handleSearch() {
      this.getList({ name: this.formData.name })
    },
    handleStatusChange(e) {
      this.authGetRequest({
        url: api.changeStatus,
        params: {
          id: e.id
        }
      }).then(res => {
        if (res.success) {
          this.getList();
        } else {
          this.$Message.error("修改所情类型配置失败");
          this.getList();
        }
      })
    },
    toUpdate(item) {
      this.$refs.updateModal.open(item);
    },
    toDetail(id) {
      this.$refs.detailModal.open(id);
    },
  }
};
</script>

<style lang="less" scoped>
.item-box {
  padding: 20px 10px 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.btn-group {
  display: flex;
  justify-content: flex-end;

  >button:not(:first-of-type) {
    margin-left: 15px;
  }
}
</style>
