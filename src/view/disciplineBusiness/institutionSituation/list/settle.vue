<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      {{ routerData.id ? '巡控处置' : '所情登记' }}
    </div>
    <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="80" @submit.native.prevent>
      <Row style="padding: 10px" :gutter="16">
        <Col :span="8">
        <div :class="'card-background-' + formData.eventLevel" style="color: #fff; border-radius: 0.5em; padding: 10px">
          <Row>
            <Col :span="6">
            <div
              style="border: 1px solid #efefef;border-radius: 0.5em;background-color: #ffffff09; display: flex; flex-direction: column; height: 156px;align-items: center; padding: 10px">
              <Icon :size="64" style="margin: 10px 0" type="md-notifications" />
              <div style="">
                <s-dicgrid style="width: 80px" :isSearch="false" placeholder="请选择所情等级" v-model="formData.eventLevel"
                  ref="dicGrid" dicName="ZD_JJKS_SQDJ" />
              </div>
            </div>
            </Col>
            <Col :span="18">
            <div style="margin-left: 15px">
              <Row style="display:flex;" :gutter="16">
                <Col :span="12">
                <Select v-model="formData.areaId" @on-change="handleRoomChange" label-in-value>
                  <OptionGroup v-for="item in roomList" :key="item.areaCode" :label="item.areaName">
                    <Option style="padding-left: 2em" v-for="j in item.children" :label="j.roomName" :value="j.roomCode"
                      :key="j.roomCode"></Option>
                  </OptionGroup>
                </Select>
                </Col>
                <Col :span="12">
                <DatePicker v-model="formData.happenTime" type="datetime" placeholder="请选择报警时间">
                </DatePicker>
                </Col>
              </Row>
              <div style="margin-top: 8px;">
                <PeopleSelector v-model="formData.reportPrisonerList" class="fixStyle"
                  :options="{ addTitleValue: '报警人', limit: 3, multiple: false }" />
              </div>
            </div>
            </Col>
          </Row>
        </div>
        <Tabs value="realTime" style="margin-top: 15px;">
          <TabPane label="监控实况" name="realTime" style="min-height: 340px">
            <GoPlayer />
          </TabPane>
          <TabPane label="监控回放" name="playback" style="min-height: 340px">
            <GoPlayer :playConfig="{ isPlayback: true }" />
          </TabPane>
          <TabPane v-if="routerData.id" label="截图照片" name="screenshot" style="min-height: 340px">
            <div v-if="detail.screenshotUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
              <img v-for="i in detail.screenshotUrl.split(',')" :key="i" style="width: 175px; height:120px"
                :src="`//${i}`" alt="">
            </div>
            <div v-else>
              <noData />
            </div>
          </TabPane>
          <TabPane v-if="routerData.id" label="附件" name="attachment" style="min-height: 340px">
            <div v-if="detail.attUrl" style="display: flex;flex-wrap: wrap; gap: 10px">
              <div v-for="i in detail.attUrl.split(',')" :key="i"
                style="display:flex; align-items: center; width: 225px; border: 1px solid #efefef; padding: 10px;font-size: 14px">
                <Icon :size="36" type="md-document" />
                <div style="margin-left: 15px;flex:1">
                  <p>名称：{{ 333 }}</p>
                  <p>大小：{{ 333 }}MB</p>
                  <p>类型： pdf</p>
                  <p style="text-align: right;">
                    <a href="javascript:;" style="color: #2b5fd9" download="">下载</a>
                  </p>
                </div>
              </div>
            </div>
            <div v-else>
              <noData />
            </div>
          </TabPane>
        </Tabs>
        </Col>
        <Col :span="16">
        <div style="height: 640px;overflow-y: auto; overflow-x: hidden;border: 1px solid #efefef; border-radius:
          0.25em">
          <div style="line-height: 2.2; border-bottom: 1px solid #efefef;padding-left: 10px;font-weight: bold;">警情处置
          </div>
          <div style="padding: 10px">
            <div style="margin-bottom: 15px;">
              <div class="form-title">所情事件</div>
              <div style="padding: 10px 0">
                <div style="margin-bottom: 15px;">
                  <Button style="margin-right: 10px;" v-for="item in prisonEventTypeList" :key="item.id"
                    @click="handleTypeChose(item)"
                    :type="item.id && item.id === formData.eventRootTypeId ? 'primary' : 'dashed'">{{
                      item.typeName }}</Button>
                </div>
                <Select v-model="formData.eventTypeId" label-in-value @on-change="handleSubEventChange">
                  <OptionGroup v-for="item in prisonEventTypeDetail?.eventItemList" :key="item.id"
                    :label="item.itemName">
                    <OptionGroup style="padding-left: 1em" v-for="e in item.childList" :value="e.id" :key="e.id"
                      :label="e.itemName">
                      <Option style="padding-left: 2em" v-for="j in e.childList" :value="j.id" :key="j.id">{{
                        `${j.itemName}(扣${j.deductPoint}分)` }}</Option>
                    </OptionGroup>
                  </OptionGroup>
                </Select>
              </div>
            </div>
            <div style="margin-bottom: 15px;">
              <div class="form-title">关联人员</div>
              <div style="padding: 10px 0">
                <el-collapse v-model="activeName" accordion>
                  <el-collapse-item title="在押人员" name="1">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '在押人员' }" v-model="formData.inPrisonerList" />
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="工作人员" name="2">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '工作人员' }" type="worker"
                        v-model="formData.workPrisonerList" />
                    </div>
                  </el-collapse-item>
                  <el-collapse-item title="外来人员" name="3">
                    <div style="padding: 10px 10px 0 10px">
                      <PeopleSelector :options="{ addTitleValue: '外来人员' }" type="outer"
                        v-model="formData.outsiderPrisonerList" />
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
            <div>
              <div class="form-title">所情处置</div>
              <div style="padding: 10px 0">
                <Row>
                  <Col :span="24">
                  <FormItem label="精准时间" prop="eventRangeTime">
                    <DatePicker v-model="formData.eventRangeTime" style="width: 420px" type="datetimerange"
                      placeholder="请选择精准时间">
                    </DatePicker>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="事件详情" prop="eventDetails">
                    <Input v-model="formData.eventDetails" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                      placeholder="请填写事件详情"></Input>
                  </FormItem>
                  </Col>
                  <Col :span="24">
                  <FormItem label="处置情况" prop="handleInfo" style="margin-bottom: 15px;">
                    <Input v-model="formData.handleInfo" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                      placeholder="请填写处置情况"></Input>
                  </FormItem>
                  <div style="margin-left: 80px; margin-bottom: 20px;"><Button style="margin-right: 10px;"
                      v-for="item in prisonEventTypeDetail?.disposeTemplateList" :key="item.id"
                      @click="() => formData.handleInfo = item.templateContent">{{
                        item.templateName }}</Button></div>
                  </Col>
                  <Col :span="24">
                  <FormItem label="推送对象" prop="postInfoList">
                    <Row class="table-head">
                      <Col :span="4">
                      岗位名称
                      </Col>
                      <Col :span="6">
                      推送对象
                      </Col>
                      <Col :span="14">
                      增加推送人员
                      </Col>
                    </Row>
                    <Row class="table-body" v-for="(role, index) in formData.postInfoList" :key="role.postCode">
                      <Col :span="4" style="display: flex; align-items: center; justify-content: center;">
                      <Checkbox :key="role.postCode" v-model="formData.postInfoList[index].checkedState"
                        :disabled="formData.postInfoList[index].isDefault">{{
                          role.postName
                        }}</Checkbox>
                      </Col>
                      <Col :span="6" style="display: flex; align-items: center; justify-content: center;">
                      <Select v-model="formData.postInfoList[index].postType">
                        <Option v-for="item in postTypeList" :value="item.value" :key="item.value">{{ item.label }}
                        </Option>
                      </Select>
                      </Col>
                      <Col :span="14">
                      <PeopleSelector :options="{ addTitleValue: '' }" type="worker"
                        v-model="formData.postInfoList[index].otherUserList" />
                      </Col>
                    </Row>
                  </FormItem>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </div>
        </Col>
      </Row>
    </Form>
    <div style="position: sticky; display: flex; justify-content: flex-end; gap: 10px;">
      <Button v-if="routerData.id" @click="handleNotDispose(routerData.id)" type="primary"
        style="margin-right: 120px;">无需处置</Button>
      <Button @click="goList">取 消</Button>
      <Button @click="handleSubmit(1)" type="primary">提 交</Button>
      <Button v-if="routerData.id" @click="handleSubmit(0)" type="info"
        style="background-color: #2db7f5; border-color: #2db7f5;">办
        结</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import { GoPlayer } from "@/components"
import { PeopleSelector } from "./components";
import noData from "@/components/noData";
export default {
  components: {
    GoPlayer,
    PeopleSelector,
    noData
  },
  data() {
    return {
      loading: false,
      routerData: {
        id: ""
      },
      roomList: [],
      postTypeList: [
        { label: '推送值班人员', value: "0" },
        { label: '岗位全部人员推送', value: "1" },
        { label: '自定义', value: "2" },
      ],
      formData: {
        eventLevel: '4',
        happenTime: new Date(),
        eventRangeTime: [],
        eventStartTime: "",
        eventEndTime: "",
        inPrisonerList: [],
        workPrisonerList: [],
        outsiderPrisonerList: [],
        eventRootTypeId: '',
        eventTypeId: '',
        postInfoList: [],
        reportPrisonerList: [],
        handleInfo: ""
      },
      // 所有所情事件类型
      prisonEventTypeList: [],
      // 单个所情事件详情
      prisonEventTypeDetail: {
        // 推送角色列表
        pushSettingList: [],
        // 事件内容列表
        eventItemList: [],
        // 处置模板列表
        disposeTemplateList: []
      },
      // 所有岗位类型
      pushRolesList: [],
      detail: {
        attUrl: "",
        screenshotUrl: "",
        inPrisonerList: [],
        reportPrisonerList: [],
        outsiderPrisonerList: [],
        workPrisonerList: [],
      },
      activeName: "1",
      ruleValidate: {}
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.fetch();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
      "authPostRequest",
    ]),
    fetch() {
      this.getJailRoomList();
      this.getPrisonEventTypeList();
    },
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.detail = res.data;
          // 以下内容为重置数据类型，基本上可以忽视
          this.detail.eventRangeTime = [this.detail.eventStartTime, this.detail.eventEndTime];
          this.detail.inPrisonerList = (this.detail.inPrisonerList === null && []) || this.detail.inPrisonerList;
          this.detail.reportPrisonerList = (this.detail.reportPrisonerList === null && []) || this.detail.reportPrisonerList;
          this.detail.outsiderPrisonerList = (this.detail.outsiderPrisonerList === null && []) || this.detail.outsiderPrisonerList;
          this.detail.workPrisonerList = (this.detail.workPrisonerList === null && []) || this.detail.workPrisonerList;
          // 回显选择的事件内容
          Object.assign(this.formData, this.detail);
          this.formData.eventRootTypeId && this.getPrisonEventDetail(this.formData.eventRootTypeId);
          console.log('this.detail', this.detail);
          console.log('this.formData', this.formData);
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 获取推送对象字典
    getPushRolesList() {
      this.authGetRequest({
        url: "/bsp-uac/ops/dic/code/getDicCodeTreeData",
        params: {
          dicName: "ZD_POST",
        },
      }).then((res) => {
        if (res.success) {
          this.pushRolesList = res.data.filter(item => {
            // 固定的6个岗位【巡控、管教、领导、综合、押解、医务】
            if (["02", "03", "04", "05", "06", "07"].includes(item.code)) {
              return item
            }
          });
          console.log("所情类型详情-推送岗位列表", this.prisonEventTypeDetail.pushSettingList);
          console.log("所有岗位列表", this.pushRolesList);
          // 需要把获取到的推送对象转换到formData中的postInfoList
          this.pushRolesList.map(item => {
            const result = this.prisonEventTypeDetail.pushSettingList.findIndex(role => role.pushPostId === item.code) > -1;
            this.formData.postInfoList.push({
              checkedState: result,
              isDefault: result,
              postType: "",
              postCode: item.code,
              postName: item.name,
              otherUserList: []
            })
            this.formData.postInfoList = this.formData.postInfoList.sort((a, b) => {
              // 将checkedState为true的排在前面
              if (a.checkedState === b.checkedState) return 0;
              if (a.checkedState) return -1;
              return 1;
            })
          })
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 获取所情事件类型
    getPrisonEventTypeList() {
      this.authPostRequest({
        url: api.prisonEventTypeList,
        params: { isEnabled: 1 },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.prisonEventTypeList = res.data;
          // 如果是带ID来的
          if (this.routerData.id) {
            this.getDetail();
          } else {
            // 如果是新增则默认勾选第一个
            this.handleTypeChose(res.data[0]);
          }
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 获取监室信息
    getJailRoomList() {
      this.authGetRequest({
        url: api.getJailArea,
        params: {
          orgCode: this.$store.state.common.orgCode
        }
      }).then((res) => {
        if (res.success) {
          this.roomList = res.data || [];
        } else {
          this.$Message.error("获取监室数据失败");
        }
      });
    },
    // 获取所情事件类型详情【取所情内容渲染树状选择框】
    getPrisonEventDetail(id) {
      this.authGetRequest({
        url: api.prisonEventTypeDetail,
        params: { id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.prisonEventTypeDetail = Object.assign(this.prisonEventTypeDetail, res.data);
          // 只有获取成功所情事件详情后才能获取推送角色列表
          this.getPushRolesList();
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    // 监室选择
    handleRoomChange(e) {
      console.log(333, e);

      const index = this.roomList.findIndex(item => item.value === e);
      if (index > -1) {
        this.formData.areaName = this.roomList[index].label;
      }
    },
    // 选择事件类型时要清空部分数据
    handleTypeChose(item) {
      this.formData.eventTypeId = '';
      this.formData.eventType = '';
      this.formData.postInfoList = [];
      this.formData.handleInfo = "";
      this.formData.eventRootTypeId = item.id;
      this.formData.eventRootTypeName = item.typeName;
      this.getPrisonEventDetail(item.id);
    },
    // 子事件内容选择
    handleSubEventChange(e) {
      this.formData.eventType = e.label.split("(")[0];
    },
    // 无需处置
    handleNotDispose(id) {
      this.loading = true;
      this.authGetRequest({
        url: api.noNeeddisposal,
        params: { id },
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Message.success("编辑成功");
          this.handleCancel();
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    handleSubmit(type) {
      this.loading = true;
      this.formData.saveType = type;
      this.formData.eventStartTime = this.formData.eventRangeTime[0];
      this.formData.eventEndTime = this.formData.eventRangeTime[1];
      this.formData.postInfoList = this.formData.postInfoList.filter(item => item.checkedState === true);
      this.authPostRequest({
        url: this.routerData.id ? api.patrolControlDispose : api.create,
        params: this.formData,
      }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.$Message.success("编辑成功");
          this.handleCancel();
        } else {
          this.$Modal.error({
            title: "错误提示",
            content: res.msg
          });
        }
      });
    },
    resetForm() {
      this.$refs.form.resetFields();
    },
    handleCancel() {
      this.resetForm();
      this.goList();
    },
    goList() {
      this.$router.replace({ name: "institutionSituationList" });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-collapse-item {
  .el-collapse-item__header {
    background: #f9fafd !important;
    border: 1px solid #dbe4ff;
    padding: 0 1.2em;
    font-size: 16px;
    color: #4f4f4f;
  }

  .el-collapse-item__wrap {
    border: 1px solid #dbe4ff;
  }
}

.card-background {
  &-1 {
    background: linear-gradient(45deg, #f7645f 5%, #f54040);
  }

  &-2 {
    background: linear-gradient(45deg, #f9cc45 5%, #f8bb20);
  }

  &-3 {
    background: linear-gradient(45deg, #39c451 5%, #49a977);
  }

  &-4 {
    background: linear-gradient(45deg, #58a0fe 5%, #168aff);
  }
}

.fixStyle {
  /deep/ .people-selector-item_plusmask {
    color: #fff;
    border-color: #fff;
  }
}

.form-title {
  font-size: 16px;
  font-weight: bold;
  padding-left: 10px;
  position: relative;

  &::before {
    content: "";
    width: 4px;
    background-color: #343cf3;
    position: absolute;
    left: 0;
    top: 10%;
    bottom: 10%;
  }
}

.table-head {
  text-align: left;
  line-height: 3;
  font-weight: bold;
  color: #232323;

  >div {
    padding: 0 15px;
    background-color: #2db7f5;
    border: 1px solid #eaeaea;

    &:nth-of-type(2) {
      border-left: none;
      border-right: none;
    }
  }
}

.table-body {
  >div {
    padding: 10px;
    border: 1px solid #eaeaea;
    border-top: none;

    &:nth-of-type(2n) {
      border-left: none;
      border-right: none;
    }
  }
}
</style>
