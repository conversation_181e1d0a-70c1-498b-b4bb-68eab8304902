<template>
  <div style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      所情详情
    </div>
    <Row style="padding: 10px" :gutter="16">
      <Col :span="8">
      <div
        style="background: linear-gradient(45deg, #f7645f 5%, #f54040); color: #fff; border-radius: 0.5em; padding: 10px">
        <Row>
          <Col :span="6">
          <div
            style="border: 1px solid #efefef;border-radius: 0.5em;background-color: #ffffff09; display: flex; flex-direction: column; height: 136px;align-items: center; padding: 10px">
            <Icon :size="64" style="margin: 10px 0" type="md-notifications" />
            <div
              style="display: inline-block; max-width: 6em; text-align:center; background-color: #f54a49a8; padding: 0.1em 1em;border:1px solid; border-radius:0.5em;color: #fff">
              紧急
            </div>
          </div>
          </Col>
          <Col :span="18">
          <div style="margin-left: 15px">
            <Row style="display:flex; align-items: baseline">
              <Col :span="12">
              <h3 style="color: inherit; font-size: 24px">A101监室</h3>
              </Col>
              <Col :span="12">
              <div>
                <Icon type="ios-alert-outline" />
                智能终端报警
              </div>
              </Col>
            </Row>
            <div style="margin-top: 8px">报警时间： {{ 5 - 2 }}</div>
            <div style="margin-top: 10px">所情事件： {{ 5 - 2 }}</div>
            <div style="margin-top: 10px">报警人员： {{ 5 - 2 }}</div>
          </div>
          </Col>
        </Row>
      </div>
      <div style="margin-top:10px">
        <Tabs value="jailPeople">
          <TabPane label="被监管人员" name="jailPeople">
            <PeopleSelector action="show" />
          </TabPane>
          <TabPane label="工作人员" name="workPeople">
            <PeopleSelector action="show" />
          </TabPane>
          <TabPane label="外来人员" name="outPeople">
            <PeopleSelector action="show" />
          </TabPane>
        </Tabs>
      </div>
      <Tabs value="realTime">
        <TabPane label="监控实况" name="realTime" style="min-height: 340px">
          <GoPlayer />
        </TabPane>
        <TabPane label="监控回放" name="playback" style="min-height: 340px">
          <GoPlayer :playConfig="{ isPlayback: true }" />
        </TabPane>
      </Tabs>
      </Col>
      <Col :span="16">
      <div>
        <div style="border-bottom: 1px solid #efefefef;padding: 8px 0; display:flex; justify-content: space-between">
          <div style="padding-left: 10px;font-weight: bold;">所情处置</div>
          <div style="padding-right: 10px; display: flex">
            <div style="margin-right: 15px">巡控岗: <span :style="{ color: 5 - 2 > 4 ? '#349ffb' : 'green' }">{{ 5 - 2
                }}办结</span>
            </div>
            <div style="margin-right: 15px">所领导岗: <span :style="{ color: 5 - 2 < 4 ? '#349ffb' : 'green' }">{{ 5 + 2
                }}办结</span>
            </div>
            <Icon :size="22" style="color: #349ffb;cursor:pointer; line-height: 1.2em" @click="() => { }"
              type="md-sync" />
          </div>
        </div>
        <div style="padding: 10px">
          <el-collapse v-model="activeNames">
            <el-collapse-item name="1">
              <template slot="title">
                <div style="flex:1; display: flex; justify-content: space-between;">
                  <div>
                    <Icon :type="activeNames.includes('1') ? 'ios-arrow-dropdown-circle' : 'ios-arrow-dropright-circle'"
                      :size="22" />
                    <span style="font-weight: bold; padding-left: 10px; font-size: 1.25em;">title</span>
                  </div>
                  <div>2025-06-06 16:05:07</div>
                </div>
              </template>
              <div style="padding: 10px">
                <div>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；</div>
                <div>在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</div>
              </div>
            </el-collapse-item>
          </el-collapse>
          <div></div>
        </div>
      </div>
      </Col>
    </Row>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
import { GoPlayer } from "@/components"
import { PeopleSelector } from "./components"
export default {
  components: {
    GoPlayer,
    PeopleSelector
  },
  data() {
    return {
      loading: false,
      routerData: {
        id: ""
      },
      detail: {},
      activeNames: ['1']
    };
  },
  computed: {},
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "institutionSituationList" });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .el-collapse-item {
  .el-collapse-item__header {
    background: #f9fafd !important;
    border: 1px solid #dbe4ff;
    padding: 0 1.2em;
    font-size: 16px;
    color: #4f4f4f;

    .el-collapse-item__arrow {
      display: none;
    }
  }

  .el-collapse-item__wrap {
    border: 1px solid #dbe4ff;
  }
}
</style>
