<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
      ">
      人员表现鉴定详情
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector :value="routerData.jgrybm" mode="detail" title="被拘留人" :show-case-info="true" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container"
        style="padding: 10px;padding-bottom: 60px; display: flex; flex-direction: column; flex: 1">
        <div class="list-title">
          关押期间表现
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="所规所纪制度" :labelStyle="{ width: '10em' }">
            {{ detail.performanceSgsjzd === "1" ? "遵守" : "不遵守" }}
          </el-descriptions-item>
          <el-descriptions-item label="一日生活管理" :labelStyle="{ width: '10em' }">
            {{ detail.performanceYrshgl === "1" ? "服从" : "不服从" }}
          </el-descriptions-item>
          <el-descriptions-item label="自杀行为或倾向">
            {{ detail.performanceZsxwhqx === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="暴力行为或倾向">
            {{ detail.performanceBlxwhqx === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="列为严管人员情况">
            {{ detail.performanceLwygryqk === "1" ? "不曾列为" : "曾列为" }}
          </el-descriptions-item>
          <el-descriptions-item label="参与所内教育情况">
            {{ detail.performanceCjsnjyqk === "1" ? "积极主动" : "消极抵触" }}
          </el-descriptions-item>
          <el-descriptions-item label="认真悔过情况">
            {{ detail.performanceRchgqk === "1" ? "无" : "有" }}
          </el-descriptions-item>
          <el-descriptions-item label="其他情况">
            {{ detail.performanceQtqk }}
          </el-descriptions-item>
          <el-descriptions-item label="需要说明的情况">
            {{ detail.needSituations }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          呈批信息
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="呈批人">
            {{ detail.addUserName }}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="呈批时间">
            {{ detail.addTime }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="list-title" style="border-top: 0;">
          审批信息
        </div>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="审批人">
            {{ detail.approverXm }}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle="{ width: '10em' }" label="审批时间">
            {{ detail.approverTime }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="goList">返 回</Button>
      <Button @click="toPrint" type="primary">鉴定表预览打印</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      loading: false,
      routerData: {
        jgrybm: "",
        id: "",
        type: ""
      },
      detail: {},
    };
  },
  computed: {
    riskSituation() {
      let result = [
        this.detail.performanceZdaqfxqk1 === "1" ? "一般" : "",
        this.detail.performanceZdaqfxqk2 === "1" ? "三级" : "",
        this.detail.performanceZdaqfxqk3 === "1" ? "二级" : "",
        this.detail.performanceZdaqfxqk4 === "1" ? "一级" : ""];
      return result.filter(item => !!item);
    }
  },
  created() {
    this.routerData = {
      ...this.$route.query,
    };
    this.getDetail();
  },
  methods: {
    ...mapActions([
      "authGetRequest",
    ]),
    getDetail() {
      this.loading = true;
      this.authGetRequest({
        url: api.detail,
        params: { id: this.routerData.id },
      }).then((res) => {
        if (res.success) {
          this.loading = false;
          this.detail = res.data;
        }
      });
    },
    goList() {
      this.$router.replace({ name: "behaviorIdentificationJlsList" });
    },
    toPrint() {
      this.$router.replace({
        path: `/discipline/behaviorIdentification-jls/print?id=${this.routerData.id}&jgrybm=${this.routerData.jgrybm}`,
      });
    }
  },
};
</script>

<style lang="less" scoped>
.list-title {
  line-height: 2.4;
  background: #f2f5fc;
  font-size: 14px;
  padding: 0 0.8em;
  font-weight: bold;
  border: 1px solid #CEE0F0;
  border-bottom: unset;
}
</style>
