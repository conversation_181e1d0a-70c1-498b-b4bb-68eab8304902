<template>
  <div class="restraintsUsedApply" style="display: flex; flex-direction: column; height: 100%">
    <div class="title" style="
        border-bottom: 1px solid #efefef;
        padding-bottom: 10px;
        font-weight: bold;
        text-align: left;
      ">
      人员表现鉴定登记
    </div>
    <Row style="margin-top: 10px; flex: 1">
      <Col :span="6" style="display: flex; flex-direction: column">
      <div style="flex: 1; display: flex; flex-direction: column; padding: 10px">
        <PersonnelSelector @change="selectUser" />
      </div>
      </Col>
      <Col :span="18" style="
          border-left: 1px solid #efefef;
          display: flex;
          flex-direction: column;
        ">
      <div class="form-container" style="padding: 10px; display: flex; flex-direction: column; flex: 1">
        <Form :model="formData" ref="form" :rules="ruleValidate" :label-width="180" label-position="left"
          @submit.native.prevent>
          <div class="form-title">
            关押期间表现
          </div>
          <div class="form-content no-round">
            <Row>
              <Col :span="24">
              <FormItem label="所规所纪制度" prop="performanceSgsjzd">
                <RadioGroup v-model="formData.performanceSgsjzd">
                  <Radio label="1">遵守</Radio>
                  <Radio label="2">不遵守</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="一日生活管理" prop="performanceYrshgl">
                <RadioGroup v-model="formData.performanceYrshgl">
                  <Radio label="1">服从</Radio>
                  <Radio label="2">不服从</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="自杀行为或倾向" prop="performanceZsxwhqx">
                <RadioGroup v-model="formData.performanceZsxwhqx">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="暴力行为或倾向" prop="performanceBlxwhqx">
                <RadioGroup v-model="formData.performanceBlxwhqx">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="列为严管人员情况">
                <RadioGroup v-model="formData.performanceLwygryqk">
                  <Radio label="1">不曾列为</Radio>
                  <Radio label="2">曾列为</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="参与所内教育情况" prop="performanceCjsnjyqk">
                <RadioGroup v-model="formData.performanceCjsnjyqk">
                  <Radio label="1">积极主动</Radio>
                  <Radio label="2">消极抵触</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="认真悔过情况" prop="performanceRchgqk">
                <RadioGroup v-model="formData.performanceRchgqk">
                  <Radio label="1">无</Radio>
                  <Radio label="2">有</Radio>
                </RadioGroup>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="其他情况" prop="performanceQtqk">
                <Input v-model="formData.performanceQtqk" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写其他情况"></Input>
              </FormItem>
              </Col>
              <Col :span="24">
              <FormItem label="需要说明的情况" prop="needSituations">
                <Input v-model="formData.needSituations" type="textarea" :autosize="{ minRows: 2, maxRows: 5 }"
                  placeholder="请填写需要说明的情况"></Input>
              </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
      </Col>
    </Row>
    <div class="bsp-base-fotter">
      <Button @click="handleCancel">返 回</Button>
      <Button type="primary" :loading="loading" @click="handleSubmit">确 定</Button>
    </div>
  </div>
</template>

<script>
import api from "./api.js";
import { mapActions } from "vuex";
export default {
  components: {},
  data() {
    return {
      formData: {
        id: "",
        jgrybm: "",
        needSituations: "",
        performanceBlxwhqx: "1",
        performanceCjsnjyqk: "1",
        performanceLwygryqk: "1",
        performanceQtqk: "",
        performanceRchgqk: "1",
        performanceSgsjzd: "1",
        performanceYrshgl: "1",
        performanceZsxwhqx: "1"
      },
      ruleValidate: {
      },
      loading: false,
    };
  },
  watch: {
  },
  created() {
  },
  methods: {
    ...mapActions(["authGetRequest", "authPostRequest"]),
    selectUser(data) {
      this.formData.jgrybm = data.jgrybm;
      this.formData.id = data.id;
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$router.replace({ name: "behaviorIdentificationJlsList" });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid && this.formData.jgrybm) {
          this.loading = true;
          this.authPostRequest({
            url: api.create,
            params: this.formData,
          }).then((res) => {
            this.loading = false;
            if (res.success) {
              this.$Message.success("呈批成功");
              this.$refs.form.resetFields();
              this.$router.replace({ name: "behaviorIdentificationJlsList" });
            } else {
              this.errorModal({ content: res.msg || "保存失败" });
            }
          });
        } else {
          this.$Message.error(
            "表单验证失败，请确定选择了监管人员与填写了必填表单。"
          );
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.form-title {
  text-align: center;
  background: #f5f7fa;
  line-height: 2.2;
  border: 1px solid #d7d7d7;
  border-top-left-radius: 0.2em;
  border-top-right-radius: 0.2em;

  &.no-round {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.form-content {
  flex: 1;
  padding: 15px;
  border: 1px solid #d7d7d7;
  border-top: unset;
  border-bottom-left-radius: 0.2em;
  border-bottom-right-radius: 0.2em;

  &.no-round {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.fixStyle {
  /deep/ .ivu-form-item-label {
    float: none;
    display: inline-block;
  }

  /deep/ .ivu-form-item-content {
    margin-left: 0 !important;
  }

  /deep/ .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }

  /deep/ .el-tabs__content {
    background: #f5f9fc;
    padding: 10px;
    height: 100%;
  }
}
</style>
