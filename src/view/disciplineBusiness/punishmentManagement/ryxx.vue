<template>
            <div class='jgrySelect-info'>
             <p class="detail-title">被监管人员</p>
             <div class='jgrySelect-flex'>
                <img :src="formData.prison.frontPhoto? http+formData.prison.frontPhoto:defaultImg" style="width: 88px;height:110px;margin-right: 10px;" />
                <div>
                    <p><span class='xm'>{{formData.prison.xm?formData.prison.xm:'-'}}</span>&nbsp;&nbsp;<span  class='xm'>{{formData.prison.roomName}}</span></p>
                    <p><span>证件号码：</span><span>{{formData.prison.zjhm}}</span></p>
                    <p><span>出生日期：</span><span>{{formData.prison.csrq}}</span></p>
                    <p><span>籍  贯：</span><span>{{formData.prison.jgName}}</span></p>
                    <p><span>民  族：</span><span>{{formData.prison.mzName}}</span></p>
                </div>
             </div>
             <div class='jgrySelect-flex' style='margin-top:16px;'>
                <div style='width:10px;padding:16px 26px 0 16px;background:#e6e9f2;border-radius:6px;margin-right:16px'>案件名称</div>
                <div>
                    <p><span>涉嫌罪名：</span><span>{{formData.prison.xszm}}</span></p>
                    <p><span>案件编号：</span><span>{{formData.prison.ajbh}}</span></p>
                    <p><span>诉讼环节：</span><span>{{formData.prison.sshjName}}</span></p>
                    <p><span>入所时间：</span><span>{{formData.prison.rssj}}</span></p>
                    <p><span>关押期限：</span><span>{{formData.prison.gyqx}}</span></p>
                    <p><span>办案单位：</span><span>{{formData.prison.basj}}</span></p>
                </div>
             </div>
         </div>
</template>

<script>
import { fileUpload } from 'sd-minio-upfile'
import {normalizeObject}   from '@/libs/util'

export default {
    components:{fileUpload},
    props:{
     jgrybm:String
    },
    data(){
        return{
          formData:{
            prison:{}
          },
          defaultImg: require('@/assets/images/main.png'),
          http: serverConfig.severHttp,

        }
    },
    watch:{
      'jgrybm':{
         handler(n,o){
            if(n){
               this.getPrisonerSelectCompomenOne ()
            }
         },deep:true,immediate:true
      },
    },
    methods:{
     getPrisonerSelectCompomenOne () {
      let params = {
        jgrybm: this.jgrybm,
        ryzt:'ZS' //this.ryzt
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
            this.formData.prison=normalizeObject(resp.data)
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    }
}
</script>

<style lang='less' scoped>
.jgrySelect-info{
    border:1px solid #dcdee2;
    padding:16px 0 16px 16px;
    margin:0px 16px 0 0;
    border-radius:6px;
    .xm{
        font-size:18px;
        font-weight:700;
    }
}
.jgrySelect-flex{
    display:flex;
}
</style>