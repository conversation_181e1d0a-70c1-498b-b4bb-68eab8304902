<!--表单  -->
<template>
  <div class="fm-content-wrap" style="width: 100%;padding: 0;">
    <Form
      ref="formData"
      :model="formData"
      :label-colon="true"
      label-position="right"
      :hide-required-mark="false"
      inline
      :label-width="130"
    >
      <div class="fm-content-form">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />严管登记
        </p>
        <Row>
          <Col :span="24">
            <FormItem
              prop="executeSituation"
              label="严管期间表现"
              style="width: 100%"
              :rules="[
                {
                  trigger: 'blur',
                  message: '必填',
                  required: true,
                },
              ]"
            >
              <Input
                v-model="formData.executeSituation"
                type="textarea"
                :rows="3"
                placeholder="请输入"
                style="width: 60%"
              ></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        // sfyg: 1,
      },
    };
  },
  mounted() {
  },
  methods: {
  },
};
</script>