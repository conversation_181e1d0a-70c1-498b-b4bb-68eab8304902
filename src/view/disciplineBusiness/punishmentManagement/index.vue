<template>
    <!-- 惩罚管理 -->
     <div class="punishment-management">
        <div>
      <div class="bsp-base-content" v-if="!showData">
        <s-DataGrid
          ref="grid"
          funcMark="yggltzlb"
          :customFunc="true"
        >
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" @click.native="addEvent('add')" v-if="func.includes(globalAppCode + ':yggltzlb:ygcp')"
              >严管呈请</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:dj') && (row.status=='04')"
              style="margin-right: 5px"
              @click="Strictly(row, 'edit') "
              >严管登记</Button>
            <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:info')"
              style="margin-right: 5px"
              @click="infoEvent(row, 'info')"
              >详情</Button>
             <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:sp') && (row.status=='03' || row.status=='02' || row.status=='01')"
              style="margin-right: 5px"
              @click="approveEvent(row, 'info')"
              >审批</Button>
            <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:yccp') && (row.status=='05')"
              @click="extendDay(row) "
              >延长呈批</Button>
             <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:tqjccp') && (row.status=='05')"
              @click="earlyTermination(row,'earlyTermination') " style="margin-top: 10px;"
              >提前解除呈批</Button>
              <Button
              type="primary"
              v-if="func.includes(globalAppCode + ':yggltzlb:jcdj')&& (row.status=='06')"
              @click="earlyTermination(row,'tearlyermination') "
              >解除登记</Button>
          </template>
        </s-DataGrid>
      </div>
    </div>
     <component :is="component"  v-if="showData" :modalTitle="modalTitle"  :curId="curData.id" :curData="curData" @toback="toback" />
  </div>
</template>
  
  <script>
import { sDataGrid } from "sd-data-grid";
// import addModal from './addModal'
import addPunishment from "./add.vue";
import detail from "./detail.vue";
export default {

  components: {
    sDataGrid,addPunishment,detail
    // addModal,addLawyer,detail
  },
  data() {
    return {
	  component:null,
      openModal: false,
      showData: false,
      modalTitle: "严管呈批",
      loading: false,
      saveType: "add",
      loadingStates: [],
      curData: {},
    };
  },
  methods: {
    cancal(tag) {
      this.openModal = false;
      if (tag) {
        this.getData();
      }
    },
	toback(){
        this.showData=false
	},
    addEvent() {
      this.curData = {};
      this.showData=true
      this.component='addPunishment'
      this.modalTitle = "严管呈批";
    },
    extendDay(row,tag) {
		this.curData=row
        this.curData.type='extendDay'
     	this.showData=true
	    this.component='detail'
        this.modalTitle = "延长严管呈批";
    },
    earlyTermination(row,tag) {
        if(tag=='earlyTermination'){
            this.modalTitle = "提前解除严管";
        }else{
            this.modalTitle = "解除登记";
        }
		this.curData=row
        this.curData.type=tag//'earlyTermination'
     	this.showData=true
	    this.component='detail'

    },
    infoEvent(row,tag) {
		  this.curData=row
     	this.showData=true
	    this.component='detail'
      this.modalTitle = "严管呈批";
    },
    approveEvent(row,tag) {
		this.curData=row
        this.curData.type='approve'
     	this.showData=true
	    this.component='detail'
      this.modalTitle = "严管呈批审批";
    },
    Strictly(row,tag) {
		this.curData=row
        this.curData.type='strictly'
     	this.showData=true
	    this.component='detail'
      this.modalTitle = "严管登记";
    },
    handleDeleteBatch() {
      // 批量删除
      let ids = [];
      this.$refs.grid.batch_select.forEach((it) => {
        ids.push(it.id);
      });
      this.$Modal.confirm({
        title: "是否确认删除所选事件",
        loading: true,
        onOk: async () => {
          this.submitDel(ids.join(","));
        },
      });
    },
    handleDelete(row) {
      // 删除
      let _this = this;
      this.$Modal.confirm({
        title: "是否确认删除【" + row.xm + "】",
        loading: true,
        onOk: async () => {
          _this.submitDel(row.id);
        },
      });
    },
    submitDel(id) {
      this.$store.dispatch("getRequest", {
          url: this.$path.acp_lawyer_delete,
          params: {
            ids: id,
          },
        })
        .then((data) => {
          if (data.success) {
            this.$Notice.success({
              title: "成功提示",
              desc: data.msg,
            });
            this.$Modal.remove();
            this.on_refresh_table();
          } else {
            this.$Modal.remove();
            this.$Notice.error({
              title: "错误提示",
              desc: data.msg,
            });
          }
        });
    },
    on_refresh_table() {
      this.$refs.grid.query_grid_data(1);
    },
  },
  mounted() {},
};
</script>
  
  <style  scoped lang="less">
.ivu-form-item {
  margin-bottom: 10px !important;
}
</style>
<style>
.bsp_org_sel_box .ivu-modal {
  width: 930px !important;
}
.punishment-management .titleNav{
    display: flex;
    justify-content: space-around;
}
.punishment-management .titleNav .navleft{
  width: 20% !important;
}
.punishment-management .titleNav .navright{
  width: 80% !important;
}
.punishment-management .navlist{
    text-align: right;
}
</style>