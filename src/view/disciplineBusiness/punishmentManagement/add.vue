<template>
    <!-- 严管呈批 -->
     <div class="punishment-edit">
         <div class="punishment-edit-left">
            <personnelSelection ref="personnelSelection"  @selectUser="selectUser" />
            <record  />
         </div>
         <div class="punishment-edit-right">
              <editPunish style="width: 100%;margin: -20px 0 0 16px;" ref="editPunish" />
         </div>
    <div class='bsp-base-fotter'>
                 <Button @click='goBack'>返 回</Button>
                 <Button @click="saveData" type="primary"  :loading='loading'>保 存</Button>  
    </div>
     </div>
</template>

<script>
import personnelSelection from "@/components/personnel-selection/index.vue"
import record from "./record.vue";
import editPunish from "./editPunish.vue";
export default {
  components:{personnelSelection,record,editPunish},
  data(){
    return{
        loading:false,
        params:{}
     }
    },
  methods:{
      goBack(){
         this.$emit('toback')
      },
      selectUser(data){
        console.log(data,'data')
          this.$set(this.params,'jgrybm',data.jgrybm)
          this.$set(this.params,'jgryxm',data.xm)
      },
      saveData(){
        if(!this.params.jgrybm){
          this.$Message.error('请选择监管人员！')
        }else{
            this.$refs.editPunish.$refs.formData.validate((valid) => {
                    if (valid) {
                        Object.assign(this.params,this.$refs.editPunish.formData)
                        if(this.$refs.editPunish.formData.measures && this.$refs.editPunish.formData.measures.length>0){
                        this.$set(this.params,'measures',this.$refs.editPunish.formData.measures.join(','))
                        }                        
                        this.saveForm()
                    } else {
                        this.$Message.error('请填写完整!');
                    }
                })
        }
      },
      saveForm(){
              this.$store.dispatch("authPostRequest", {
                url: this.$path.acp_punishment_create,
                params:this.params
                },)
                .then((res) => {
                    if(res.success){
                        this.$Message.success('提交成功')
                        this.goBack()
                    }else{
                        this.$Message.error(res.msg || '提交失败')
                    }
                });
            },
  }

}
</script>

<style lang="less" scoped>
.punishment-edit{
      width: 100%;
      display: flex;
      margin-top: 10px;
    .punishment-edit-left{
      width: 340px;
    }
    .punishment-edit-right{
      width: calc(~'100% - 340px');
    }
}
</style>