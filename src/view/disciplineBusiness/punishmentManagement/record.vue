<template>
   <!--    严管记录           -->
     <div style="height: 70%;">
        <Tabs value="travel" style="height: 100%;">
          <TabPane label="流程轨迹" name="travel" style="height: 100%;">
            <div class="track-box">
              <approveTrack :showTitle="false" :curId="curId"   :path="$path.acp_punishment_getApproveTrack" />
            </div>
          </TabPane>
          <TabPane :label="'严管记录('+TimelineItemData.length+')'" name="name" style="height: 100%;">
              <div class="TimelineItemData">
                 <Timeline v-if="TimelineItemData.length>0">
                    <TimelineItem v-for="(item,index) in TimelineItemData" :key="index" >
                        <div class="card-time-line"> 
                            <!-- <p class="content"><span></span><span>{{item.addTime}}</span></p> -->
                            <p class="content "><span>严管时间：</span><span>{{item.startDate}}-{{item.endDate }}</span></p>
                            <p class="content"><span>严管原因：</span><span>{{item.specificReason}}</span></p>
                            <p class="content"><span>严管措施：</span><span>{{item.measuresName}}</span></p>
                        </div>
                    </TimelineItem>
                </Timeline>
                <noData v-else  />
              </div>
          </TabPane>
        </Tabs>
     </div>
</template>

<script>
import noData from "@/components/bsp-empty/index.vue"
import approveTrack from "@/components/track/approveTrack.vue"
export default {
    components:{noData,approveTrack},
    props:{
      jgrybm:String,
      curId:String
    },
    data(){
        return{
            total:0,
            TimelineItemData:[],
            page:{
                pageNo:1,
                pageSize:20
            }
        }
    },
    watch:{
        jgrybm:{
            handler(n,o){
                if(n){
                  this.getRecord()
                }
            },deep:true,immediate:true
        }
    },
    methods:{
            getNo (pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getRecord()
    },
    getSize (pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getRecord()
    },
      getRecord(){
          let params={jgrybm:this.jgrybm }
            Object.assign(params,this.page)
                       this.$store.dispatch('getRequest', {url: this.$path.acp_punishment_getPunishmentHistoryByJgrybm, params: params}).then(resp => {
                            if (resp.success) {
                                 this.TimelineItemData=resp.data.list?resp.data.list:[]
                                 this.total=resp.data.total?resp.data.total:0
                            } else {
                           
                            }
                        })
      }
    }
}
</script>

<style lang="less" scoped>
.card-time-line {
   padding: 10px 15px ;
   border: 1px solid #dcdee2 !important;
   margin-right: 16px;
   border-radius: 6px;
   box-shadow: 0px 4px 16px 1px rgba(0, 0, 0, 0.16);
   .content{
    line-height: 30px ;
    font-family: Source Han Sans CN, Source Han Sans CN;
   }
}
.TimelineItemData{
    height: 75%;
    margin-top: 16px;
    overflow-y: auto;
    padding-top: 16px;
}
/deep/ .ivu-tabs-content{
   height: 100% !important;
}
.flex-time{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.track-box{
  height: 86%;
  overflow: auto;
}
</style>