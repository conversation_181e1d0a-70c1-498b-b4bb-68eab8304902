<!--表单  -->
<template>
  <div class="fm-content-wrap" style="width: 100%;padding: 0;">
    <Form
      ref="formData"
      :model="formData"
      :label-colon="true"
      label-position="right"
      :hide-required-mark="false"
      inline
      :label-width="130"
    >
      <div class="fm-content-form">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />延长处罚呈批
        </p>
        <Row>
          <Col :span="12">
            <FormItem 
              prop="extendDay"
              label="延长天数"
              style="width: 100%"
              :rules="[
                {
                  trigger: 'blur',
                  message: '必填',
                  required: true,
                },
              ]"
            >
              <Input
                v-model="formData.extendDay"
                placeholder="请输入"
                style="width: 60%"
              ></Input>
            </FormItem>
          </Col>
                 <Col :span="12">
            <FormItem 
              prop="extendDay"
              label="延长至"
              style="width: 100%"
            >
            <span>{{ formData.curDate}}</span>
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem
              prop="reason"
              label="延长理由"
              style="width: 100%"
              :rules="[
                {
                  trigger: 'blur',
                  message: '必填',
                  required: true,
                },
              ]"
            >
              <Input
                v-model="formData.reason"
                type="textarea"
                :rows="3"
                placeholder="请输入"
                style="width: 60%"
              ></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
  </div>
</template>
<script>
import { getUserCache,formatDateparseTime  } from '@/libs/util'

export default {
  props:{
    curData:Object
  },
  data() {
    return {
      formData: {
        // sfyg: 1,
        curDate:this.curData.endDate
      },
    };
  },
  watch:{
    formData:{
      handler(n,o){
          if(n.extendDay>0){
            let date=new Date();
            let data=formatDateparseTime(new Date(new Date().setDate(new Date(this.curData.endDate).getDate() + Number(this.formData.extendDay && this.formData.extendDay>0?this.formData.extendDay:0)))); // 加上天数
            this.$set(this.formData,'curDate',data)
          }
      },deep:true,immediate:true
    }
  },
  mounted() {
  },
  methods: {
  },
};
</script>