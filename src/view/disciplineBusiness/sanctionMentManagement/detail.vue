<template>
  <!-- 处罚呈批 -->
   <div style="overflow: hidden;">
       <div class="small-title">{{ modalTitle }}</div>
  <div class="punishment-edit">
    <div class="punishment-edit-left">
      <ryxx ref="ryxx" :jgrybm="infoData.jgrybm" />
      <record :jgrybm="infoData.jgrybm" :curId="infoData.id" style="height: 50%;overflow: auto;" />
    </div>
    <div class="punishment-edit-right">
      <info :formData="infoData" />
      <spPunish
        style="width: 100%;"
        ref="spPunish"
        v-if="curData.type == 'approve'"
      />
      <strictly   style="width: 100%;"
        ref="strictly"
        v-if="curData.type == 'strictly'" />
        <!-- 延长 -->
        <ExtendedDays style="width: 100%;"
        ref="extendDay" :curData="infoData"
        v-if="curData.type == 'extendDay'" />
        <!-- 提前 -->
         <earlyTermination style="width: 100%;" :curData="curData"
          ref="earlyTermination"  :remainderDay="infoData.remainderDay"
        v-if="curData.type == 'earlyTermination' || curData.type == 'tearlyermination'" />
    </div>
    <div class="bsp-base-fotter">
      <Button @click="goBack">返 回</Button>
      <Button
        @click="saveData"
        type="primary"
        :loading="loading"
        v-if="curData.type == 'approve'  || curData.type == 'strictly' || curData.type == 'extendDay' || curData.type =='earlyTermination' || curData.type =='tearlyermination'"
        >保 存</Button>
    </div>
    </div>
  </div>
</template>

<script>
import personnelSelection from "@/components/personnel-selection/index.vue";
import record from "./record.vue";
import spPunish from "./sp.vue";
import ryxx from "./ryxx.vue";
import info from "./info.vue";
import strictly from "./strictly.vue";
import ExtendedDays from "./extendedDays.vue";
import earlyTermination from "./earlyTermination.vue";
import { Modal } from "view-design";
export default {
  components: { personnelSelection, record, spPunish, ryxx, info,strictly,ExtendedDays,earlyTermination },
  props: {
    curData: Object,
    modalTitle:String
  },
  data() {
    return {
      loading: false,
      params: {},
      infoData:{}
    };
  },
  mounted(){
    console.log(this.curData,'curData')
    this.getData()
  },
  methods: {
    goBack() {
      this.$emit("toback");
    },
    getData(){
      this.$store.dispatch("getRequest", {
          url:this.$path.acp_punishment_get ,
          params:{id:this.curData.id},
        })
        .then((res) => {
          if (res.success) {
            this.infoData=res.data
            console.log(this.infoData.remainderDay,'remainderDay')
          } else {
            this.$Message.error(res.msg);
          }
        });
    },
    saveData(){
         if(this.curData.type=='approve'){
             this.saveDataSp() 
         }else if(this.curData.type=='strictly'){
             this.saveStrictly() 
         }else if(this.curData.type=='extendDay'){
             this.saveExtendDay() 
         }else if(this.curData.type=='earlyTermination'){
             this.saveEarlyTermination(this.$path.acp_punishment_createRemove) 
         }else if(this.curData.type=='tearlyermination'){
             this.saveEarlyTermination(this.$path.acp_punishment_removeRegInfo)  
         }
    },
    saveExtendDay(url){
       this.$set(this.params, "id", this.curData.id);
        this.$refs.extendDay.$refs.formData.validate((valid) => {
          if (valid) {
            Object.assign(this.params, this.$refs.extendDay.formData);
            this.saveForm(this.$path.acp_punishment_createExtend);
          } else {
            this.$Message.error("请填写完整!");
          }
        });
    },
    saveEarlyTermination(url){
       this.$set(this.params, "id", this.curData.id);
        this.$refs.earlyTermination.$refs.formData.validate((valid) => {
          if (valid) {
            Object.assign(this.params, this.$refs.earlyTermination.formData);
            this.saveForm(url);
          } else {
            this.$Message.error("请填写完整!");
          }
        });
    },
    saveStrictly(){
        this.$set(this.params, "id", this.curData.id);
        this.$refs.strictly.$refs.formData.validate((valid) => {
          if (valid) {
            Object.assign(this.params, this.$refs.strictly.formData);
            this.saveForm(this.$path.acp_punishment_regInfo);
          } else {
            this.$Message.error("请填写完整!");
          }
        });
    },
    saveDataSp() {
        this.$set(this.params, "id", this.curData.id);
        this.$set(this.params, "jgrybm", this.curData.jgrybm);
        this.$set(this.params, "jgryxm", this.curData.xm);
        this.$refs.spPunish.$refs.formData.validate((valid) => {
          if (valid) {
            Object.assign(this.params, this.$refs.spPunish.formData);
            let url=this.$path.acp_punishment_approve
            if(this.curData.status=='01'){
              url=this.$path.acp_punishment_approve
            }else if(this.curData.status=='03'){
              url=this.$path.acp_punishment_apprRemove
            }else if(this.curData.status=='02'){
              url=this.$path.acp_punishment_approveExtend
            }
            this.saveForm(url);
          } else {
            this.$Message.error("请填写完整!");
          }
        });
    },
    saveForm(url) {
      this.$store
        .dispatch("authPostRequest", {
          url:url ,
          params: this.params,
        })
        .then((res) => {
          if (res.success) {
            this.$Message.success("提交成功");
            this.goBack();
          } else {
            this.$Message.error(res.msg || "提交失败");
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.small-title{
  padding-bottom: 6px;
  border-bottom :1px solid #dcdee2;
}
.punishment-edit {
  width: 100%;
  display: flex;
  margin-top: 10px;
  .punishment-edit-left {
    width: 440px;
  }
  .punishment-edit-right {
    width: calc(~"100% - 440px");
  }
}
</style>