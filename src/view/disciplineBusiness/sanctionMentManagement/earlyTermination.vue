<!--表单  -->
<template>
  <div class="fm-content-wrap" style="width: 100%; padding: 0">
    <Form
      ref="formData"
      :model="formData"
      :label-colon="true"
      label-position="right"
      :hide-required-mark="false"
      inline
      :label-width="130"
    >
      <div class="fm-content-form">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />{{
            curData.type == "earlyTermination"
              ? "提前解除处罚呈批"
              : "解除处罚登记"
          }}
        </p>
        <Row>
          <Col :span="24">
            <FormItem
              v-if="curData.type == 'earlyTermination'"
              prop="extendDay"
              label="剩余天数"
              style="width: 100%"
            >
              <span>{{ formData.remainderDay }}天</span>
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem
              prop="removeReason"
              :label="
                curData.type == 'earlyTermination' ? '提前解除理由' : '解除理由'
              "
              style="width: 100%"
              :rules="[
                {
                  trigger: 'blur',
                  message: '必填',
                  required: true,
                },
              ]"
            >
              <s-dicgrid
                v-model="formData.removeReason"
                style="width: 60%"
                dicName="ZD_YGGLTQJCYY"
              />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem
              v-if="curData.type == 'earlyTermination'"
              prop="specificRemoveReason"
              label="具体解除理由"
              style="width: 100%"
              :rules="[
                {
                  trigger: 'blur',
                  message: '必填',
                  required: formData.removeReason == '06' ? true : false,
                },
              ]"
            >
              <Input
                v-model="formData.specificRemoveReason"
                type="textarea"
                :rows="3"
                placeholder="请输入"
                style="width: 60%"
              ></Input>
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem prop="remark" label="备注" style="width: 100%">
              <Input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入"
                style="width: 60%"
              ></Input>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    remainderDay: {
      type: Number,
      default: 0,
    },
    curData: Object,
  },
  data() {
    return {
      formData: {
        remainderDay: this.remainderDay ? this.remainderDay : 0,
      },
    };
  },
  watch: {
    remainderDay: {
      handler(n, o) {
        this.$set(this.formData, "remainderDay", n);
        if (this.curData.type != "earlyTermination") {
          this.$set(this.formData, "removeReason", '99');
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    console.log(this.remainderDay, "this.remainderDay");
  },
  methods: {},
};
</script>