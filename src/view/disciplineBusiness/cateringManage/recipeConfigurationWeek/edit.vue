<template>
  <div class="recipe-edit-container">
    <!-- 编辑页面头部 -->
    <div class="edit-header">
      <div class="header-left">
        <div class="recipe-name-section">
          <Icon type="md-restaurant" color="#2b5fda" />
          <span class="recipe-label">菜谱名称</span>
          <Form ref="recipeTemform" :model="recipeTemform" class="inline-form">
            <FormItem prop="templateName"
              :rules="{ required: true, message: '菜谱名称不能为空', trigger: 'blur' }">
              <Input v-model="recipeTemform.templateName"
                     placeholder="请输入菜谱名称"
                     size="large"
                     class="recipe-name-input"></Input>
            </FormItem>
          </Form>
        </div>
      </div>

      <!-- 周期切换控件 -->
      <div class="header-right">
        <div class="week-navigation">
          <Icon type="md-calendar" color="#2b5fda" />
          <span class="week-label">周期切换</span>
          <Button type="default"
                  icon="md-arrow-back"
                  @click="goToPreviousWeek"
                  :disabled="!canGoPrevious"
                  class="week-nav-btn">
            上一周
          </Button>
          <div class="current-week-display">
            <Icon type="md-time" color="#2b5fda" />
            <span>{{ currentWeekDisplay }}</span>
          </div>
          <Button type="default"
                  icon="md-arrow-forward"
                  @click="goToNextWeek"
                  :disabled="!canGoNext"
                  class="week-nav-btn">
            下一周
          </Button>
        </div>
      </div>
    </div>

    <!-- 编辑内容区域 -->
    <div class="edit-content">
      <!-- 左侧选择区域 -->
      <div class="left-panel">
        <div class="panel-card">
          <!-- 功能切换按钮 -->
          <div class="function-selector">
            <div class="function-buttons">
              <Button :type="activeFunction === 'template' ? 'primary' : 'default'"
                      :class="{ 'active': activeFunction === 'template' }"
                      @click="switchFunction('template')">
                <Icon type="md-list" />
                菜谱模板
              </Button>
              <Button :type="activeFunction === 'dishes' ? 'primary' : 'default'"
                      :class="{ 'active': activeFunction === 'dishes' }"
                      @click="switchFunction('dishes')">
                <Icon type="md-nutrition" />
                菜品管理
              </Button>
            </div>
          </div>

          <!-- 菜谱模板选择区域 -->
          <div class="template-section" v-show="activeFunction === 'template'">
            <div class="section-header">
              <Icon type="md-list" color="#2b5fda" />
              <span>选择菜谱模板</span>
            </div>

            <div class="search-container">
              <Input v-model="templateSearchName"
                     placeholder="搜索菜谱模板"
                     size="large"
                     clearable
                     @on-enter="handleSearchTemplate"
                     @on-change="handleTemplateSearchChange">
                <Icon type="ios-search" slot="prefix" />
                <Button slot="append" icon="ios-search" @click="handleSearchTemplate">搜索</Button>
              </Input>
            </div>

            <div class="template-list">
              <div v-if="templateLoading" class="loading-state">
                <Spin size="large">
                  <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
                  <div>加载模板数据中...</div>
                </Spin>
              </div>

              <div v-else-if="filteredTemplateList.length === 0" class="empty-state">
                <Icon type="md-document" size="48" color="#c5c8ce" />
                <p>暂无菜谱模板</p>
                <p>请先创建菜谱模板</p>
              </div>

              <div v-else>
                <div class="template-card"
                     v-for="(item, index) in filteredTemplateList"
                     :key="index"
                     @click="multiplex111(item)">
                  <div class="template-icon">
                    <img :src="cookBook" alt="菜谱模板">
                  </div>
                  <div class="template-info">
                    <div class="template-name" :title="item.templateName">
                      {{ item.templateName }}
                    </div>
                    <div class="template-meta">
                      <Icon type="md-time" />
                      <span>模板</span>
                    </div>
                  </div>
                  <div class="template-action">
                    <Button type="primary"  icon="md-copy">
                      复用
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 菜品管理区域 -->
          <div class="dishes-section" v-show="activeFunction === 'dishes'">
            <!-- 搜索区域 -->
            <div class="search-section">
              <Input v-model="dashesName"
                     placeholder="搜索菜品名称"
                     size="large"
                     @on-enter="handleSearchDashes"
                     clearable>
                <Icon type="ios-search" slot="prefix" />
                <Button slot="append"
                        icon="ios-search"
                        @click="handleSearchDashes">搜索</Button>
              </Input>
            </div>

            <!-- 分类标签页 -->
            <Tabs v-model="currentTabName" @on-click="handleTabChange" class="dishes-tabs">
              <TabPane v-for="(itemFath) in caterClassifyList"
                       :key="itemFath.id"
                       :label="itemFath.cateName"
                       :name="itemFath.id.toString()">
              </TabPane>
            </Tabs>

            <!-- 菜品管理内容区域 -->
            <div class="dishes-content">
              <!-- 添加菜品区域 -->
              <div class="add-dish-section">
                <div class="add-dish-form">
                  <Input v-model="adddashesName"
                         size="large"
                         placeholder="请输入新菜品名称"
                         @on-enter="handleAddDashes">
                    <Icon type="md-add" slot="prefix" />
                  </Input>
                  <Button type="primary"
                          icon="ios-add"
                          size="large"
                          @click="handleAddDashes"
                          :disabled="!adddashesName.trim()">
                    添加菜品
                  </Button>
                </div>
              </div>

              <!-- 菜品列表 -->
              <div class="dishes-list-container">
                <div v-if="dishesLoading" class="loading-state">
                  <Spin size="large">
                    <Icon type="ios-loading" size="18" class="spin-icon-load"></Icon>
                    <div>加载菜品数据中...</div>
                  </Spin>
                </div>

                <div v-else-if="dashesList.length === 0" class="empty-state">
                  <Icon type="md-restaurant" size="48" color="#c5c8ce" />
                  <p>暂无菜品</p>
                  <p>请添加菜品或切换分类查看</p>
                </div>

                <draggable v-else
                           id="left-draggable"
                           :list="dashesList"
                           :group="{ name: 'cook', pull: 'clone', put: false }"
                           :move="checkMove"
                           drag-class="dragging-item"
                           ghost-class="ghost-item"
                           :touch-start-threshold="5"
                           animation="200">
                  <div v-for="(item) in dashesList"
                       :key="item.id"
                       class="dish-card">
                    <div class="dish-category" :style="{ backgroundColor: getCategoryColor(item.cateId, item.cookType) }">
                      {{ item.cookType }}
                    </div>
                    <div class="dish-name" :title="item.cookName">
                      {{ item.cookName }}
                    </div>
                    <div class="dish-actions">
                      <Tooltip content="编辑菜品" placement="top">
                        <Icon type="md-create"
                              class="action-icon edit-icon"
                              @click="handleUpateDahesName(item)" />
                      </Tooltip>
                      <Tooltip content="删除菜品" placement="top">
                        <Icon type="md-trash"
                              class="action-icon delete-icon"
                              @click="handleDeleteDahesName(item)" />
                      </Tooltip>
                    </div>
                    <div class="drag-handle">
                      <Icon type="md-menu" />
                    </div>
                  </div>
                </draggable>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧菜谱配置区域 -->
      <div class="right-panel">
        <div class="panel-card">
          <div class="recipe-table-header">
            <div class="table-title">
              <Icon type="md-grid" color="#2b5fda" />
              <span>一周菜谱配置</span>
            </div>
          </div>

          <div class="recipe-table-wrapper">
            <div class="table-container">
              <table class="recipe-config-table">
                <thead>
                  <tr class="table-header-row">
                    <th class="meal-header">
                      <div class="header-content">
                        <Icon type="md-time" />
                        <span>餐次</span>
                      </div>
                    </th>
                    <th v-for="(item, index) in weekList" :key="index" class="day-header">
                      <div class="day-content">
                        <div class="day-name">{{ item.name }}</div>
                        <Button v-if="index > 0"
                                :disabled="copyDayUpdateFlag(index)"
                                size="small"
                                type="dashed"
                                icon="md-copy"
                                @click="copyUpdateDay(index)">
                          复制昨日
                        </Button>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(menuitem, menuIndex) in cookMeals" :key="menuIndex" class="meal-row">
                    <td class="meal-cell">
                      <div class="meal-info">
                        <Icon :type="getMealIcon(menuitem.name)" color="#2b5fda" />
                        <span class="meal-name">{{ menuitem.name }}</span>
                      </div>
                    </td>
                    <td v-for="(item, index) in getMealWeekSubs(menuitem.name)"
                        :key="index"
                        class="dish-cell">
                      <draggable v-model="item.cooks"
                                 :group="{ name: 'cook', put: true }"
                                 :move="checkMove"
                                 animation="200"
                                 ghost-class="chosen-item"
                                 class="drop-zone">
                        <div class="dish-item" v-for="(info, idx) in item.cooks" :key="idx">
                          <!-- 保留分类指示器（左侧彩色条），只隐藏分类文本 -->
                          <div class="dish-category-indicator" :style="{ backgroundColor: getCategoryColor(info.cateId, info.cookType) }"></div>
                          <div class="dish-content">
                            <div class="dish-name" :title="info.name">{{ info.name }}</div>
                            <!-- 暂时注释掉分类文本，保持与菜谱模板一致 -->
                            <!-- <div class="dish-category-text">{{ info.cookType }}</div> -->
                          </div>
                          <div class="dish-remove" @click="delSubUpdate(menuIndex, index, idx)">
                            <Icon type="md-close" />
                          </div>
                        </div>
                        <div class="drop-placeholder" v-if="!item.cooks || item.cooks.length === 0">
                          <Icon type="md-add" />
                          <span>拖拽菜品到此处</span>
                        </div>
                      </draggable>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bsp-base-fotter">
      <Button type="primary"
              :loading="saving"
              :disabled="saving"
              @click="handleUpdateRecipeTem">
        {{ saving ? '保存中...' : '保存菜谱' }}
      </Button>
      <Button @click="goBack" :disabled="saving">返回</Button>
    </div>

    <!-- 菜品编辑模态框 -->
    <Modal v-model="isChangeDashesModal" title="编辑菜品" @on-cancel="handleDashesCancel">
      <Form ref="formDashes" :model="formDashes" :label-width="80">
        <FormItem label="菜品名称" prop="cookName" :rules="[{required: true, message: '请输入菜品名称', trigger: 'blur'}]">
          <Input v-model="formDashes.cookName" placeholder="请输入菜品名称" />
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="handleDashesCancel">取消</Button>
        <Button type="primary" @click="handleDashesSubmit">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import dayjs from 'dayjs'
import draggable from "vuedraggable";

export default {
  name: "recipeEdit",
  props: {
    // 从父组件传入的编辑信息
    editInfo: {
      type: Object,
      default: () => ({})
    },
    // 周次信息，用于生成默认菜谱名称
    weekInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeFunction: 'template', // 控制左侧功能区域的激活状态：'template' 或 'dishes'
      templateSearchName: "", // 模板搜索关键词
      cookBook: require("../../../../assets/images/cateringManage/cook_book.png"),
      templateList: [],
      recipeTemform: {
        templateName: "",
      },
      caterClassifyList: [],
      currentTabIndex: 0,
      currentTabName: "", // 当前选中的Tab名称
      dashesList: [],
      adddashesName: "",
      dashesName: "",
      cateId: null,
      cateName: "",
      isChangeDashesModal: false,
      dashesItem: {},
      formDashes: {
        cookName: ""
      },
      weekList: [],
      numTonList: [],
      cookMeals: [
        {
          name: "早餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 1
        },
        {
          name: "午餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 2
        },
        {
          name: "晚餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 3
        }
      ],
      cookSubSNew: [],
      recipeEditInfo: {
        id: ""
      },
      // 菜品分类颜色管理
      categoryColorMap: {}, // 存储每个分类的颜色
      colorPool: [
        '#3097ff', // 蓝色
        '#ed8433', // 橙色
        '#33bb83', // 绿色
        '#8e6bee', // 紫色
        '#f56c6c', // 红色
        '#409eff', // 天蓝色
        '#67c23a', // 草绿色
        '#e6a23c', // 金黄色
        '#f78989', // 粉红色
        '#9c88ff', // 淡紫色
        '#36cfc9', // 青色
        '#ff9c6e', // 橘色
        '#73d13d', // 亮绿色
        '#ff85c0', // 玫红色
        '#597ef7', // 靛蓝色
      ],
      // 当前周次信息
      currentWeekInfo: {},
      // 周期导航相关
      currentWeekDisplay: '',
      // 标记是否已经切换过周次（用于保存逻辑判断）
      hasPerformedWeekChange: false,
      // 加载状态
      dishesLoading: false,
      templateLoading: false,
      // 保存状态，防止重复提交
      saving: false
    }
  },

  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),

    // 返回主页面
    goBack() {
      this.$emit('close')
    },

    // 生成菜谱名称默认值
    generateDefaultRecipeName() {
      // 优先使用currentWeekInfo，如果没有则使用weekInfo
      const weekInfo = this.currentWeekInfo && Object.keys(this.currentWeekInfo).length > 0
        ? this.currentWeekInfo
        : this.weekInfo

      if (!weekInfo) {
        return '菜谱模板'
      }

      try {
        // 提取周次信息，优先使用weekNumStr
        let weekNumber = '第一周' // 默认值
        if (weekInfo.weekNumStr) {
          weekNumber = weekInfo.weekNumStr
        }

        // 处理日期信息
        if (weekInfo.weekDate && weekInfo.endDate) {
          // 如果有标准格式的开始和结束日期 (YYYY-MM-DD)
          if (weekInfo.weekDate.match(/^\d{4}-\d{2}-\d{2}$/) && weekInfo.endDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const startDate = dayjs(weekInfo.weekDate)
            const endDate = dayjs(weekInfo.endDate)

            if (startDate.isValid() && endDate.isValid()) {
              const startMonth = startDate.format('M月')
              const startDay = startDate.format('D日')
              const endMonth = endDate.format('M月')
              const endDay = endDate.format('D日')

              // 使用开始日期的月份作为菜谱名称的月份
              const monthName = startMonth

              const result = `${monthName}${weekNumber}菜谱（${startMonth}${startDay}-${endMonth}${endDay}）`
              return result
            }
          }
        } else if (weekInfo.weekDate) {
          // 处理单个日期或日期范围字符串
          const weekDate = weekInfo.weekDate

          // 如果是标准日期格式 (YYYY-MM-DD)
          if (weekDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const date = dayjs(weekDate)
            if (date.isValid()) {
              const monthName = date.format('M月')
              const result = `${monthName}${weekNumber}菜谱`
              return result
            }
          }
          // 如果是中文日期范围格式 "6月30日-7月6日"
          else if (weekDate.includes('-') && weekDate.includes('月') && weekDate.includes('日')) {
            const dateRange = weekDate.split('-')
            if (dateRange.length === 2) {
              const startDateStr = dateRange[0].trim() // "6月30日"
              const endDateStr = dateRange[1].trim()   // "7月6日"

              // 从日期字符串中提取月份和日期信息
              const startMonthMatch = startDateStr.match(/(\d+)月/)
              const startDayMatch = startDateStr.match(/(\d+)日/)
              const endMonthMatch = endDateStr.match(/(\d+)月/)
              const endDayMatch = endDateStr.match(/(\d+)日/)

              if (startMonthMatch && startDayMatch && endMonthMatch && endDayMatch) {
                const startMonth = `${startMonthMatch[1]}月`
                const startDay = `${startDayMatch[1]}日`
                const endMonth = `${endMonthMatch[1]}月`
                const endDay = `${endDayMatch[1]}日`

                // 使用开始日期的月份作为菜谱名称的月份
                const monthName = startMonth

                const result = `${monthName}${weekNumber}菜谱（${startMonth}${startDay}-${endMonth}${endDay}）`
                return result
              }
            }
          }
        }

        // 如果上面的解析失败，尝试使用weekNumStr生成基本名称
        if (weekInfo.weekNumStr) {
          const now = dayjs()
          const monthName = now.format('M月')
          const result = `${monthName}${weekInfo.weekNumStr}菜谱`
          return result
        }

        // 如果都没有，返回默认格式
        const now = dayjs()
        const monthName = now.format('M月')
        const result = `${monthName}第一周菜谱`
        return result

      } catch (error) {
        console.error('生成菜谱名称失败:', error)
        return '菜谱模板'
      }
    },

    // 将中文日期格式转换为标准的YYYY-MM-DD格式
    convertToStandardDate(dateStr, isStartDate = true) {
      if (!dateStr) return null

      try {
        // 如果已经是标准格式，直接返回
        if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
          return dateStr
        }

        // 处理中文日期格式，如 "7月14日-7月20日"
        if (dateStr.includes('月') && dateStr.includes('日')) {
          const currentYear = dayjs().year()

          // 如果是日期范围格式
          if (dateStr.includes('-')) {
            const dateRange = dateStr.split('-')
            if (dateRange.length === 2) {
              const targetDateStr = isStartDate ? dateRange[0].trim() : dateRange[1].trim()

              // 解析中文日期，如 "7月14日"
              const monthMatch = targetDateStr.match(/(\d+)月/)
              const dayMatch = targetDateStr.match(/(\d+)日/)

              if (monthMatch && dayMatch) {
                const month = parseInt(monthMatch[1])
                const day = parseInt(dayMatch[1])

                // 构建标准日期
                const standardDate = dayjs().year(currentYear).month(month - 1).date(day)
                if (standardDate.isValid()) {
                  return standardDate.format('YYYY-MM-DD')
                }
              }
            }
          } else {
            // 单个中文日期格式，如 "7月14日"
            const monthMatch = dateStr.match(/(\d+)月/)
            const dayMatch = dateStr.match(/(\d+)日/)

            if (monthMatch && dayMatch) {
              const month = parseInt(monthMatch[1])
              const day = parseInt(dayMatch[1])

              // 构建标准日期
              const standardDate = dayjs().year(currentYear).month(month - 1).date(day)
              if (standardDate.isValid()) {
                return standardDate.format('YYYY-MM-DD')
              }
            }
          }
        }

        return dateStr

      } catch (error) {
        return dateStr
      }
    },

    // 获取周数
    getWeekNumber(date) {
      const firstDayOfMonth = date.startOf('month')
      const firstWeekStart = firstDayOfMonth.startOf('week')
      const weeksDiff = date.diff(firstWeekStart, 'week')
      return Math.max(1, weeksDiff + 1)
    },

    // 上一周
    async goToPreviousWeek() {
      if (!this.canGoPrevious) {
        return
      }

      // 请求父组件提供上一周的信息
      this.$emit('request-week-change', 'previous', (newWeekInfo) => {
        if (newWeekInfo) {
          this.handleWeekChange('previous', newWeekInfo)
        } else {
          this.$Message.warning('无法切换到上一周')
        }
      })
    },

    // 下一周
    async goToNextWeek() {
      if (!this.canGoNext) {
        return
      }

      // 请求父组件提供下一周的信息
      this.$emit('request-week-change', 'next', (newWeekInfo) => {
        if (newWeekInfo) {
          this.handleWeekChange('next', newWeekInfo)
        } else {
          this.$Message.warning('无法切换到下一周')
        }
      })
    },

    // 统一的周次切换处理方法
    async handleWeekChange(direction, newWeekInfo) {
      try {
        // 检查新周次是否已过期（不能编辑）
        if (!this.canEditWeek(newWeekInfo)) {
          this.$Message.warning('该周次已过期，无法进行编辑')
          return
        }

        // 更新weekInfo（这是props传递的数据源）
        this.$emit('update:weekInfo', newWeekInfo)

        // 更新当前周次信息
        this.currentWeekInfo = { ...newWeekInfo }

        // 重要：标记已经进行了周次切换
        this.hasPerformedWeekChange = true

        // 重新生成菜谱名称
        this.recipeTemform.templateName = this.generateDefaultRecipeName()
        this.currentWeekDisplay = this.recipeTemform.templateName

        // 重要：完全重置编辑状态，根据目标周次是否有tempId来设置模式
        // 先重置为空，然后根据目标周次的tempId决定模式
        this.recipeEditInfo.id = ""

        if (newWeekInfo.tempId) {
          this.recipeEditInfo.id = newWeekInfo.tempId
        } else {
          this.recipeEditInfo.id = ""
        }

        // 加载新周次的菜谱数据
        await this.loadWeekRecipeData(newWeekInfo, true)

        // 通知父组件周次变化
        this.$emit('week-change', direction, newWeekInfo)

        this.$Message.success(`已切换到${direction === 'previous' ? '上一周' : '下一周'}`)
      } catch (error) {
        console.error('周次切换失败:', error)
        this.$Message.error('周次切换失败，请重试')
      }
    },

    // 计算周期变化
    calculateWeekChange(direction) {
      // 优先使用currentWeekInfo，如果没有则使用weekInfo
      const sourceWeekInfo = this.currentWeekInfo && Object.keys(this.currentWeekInfo).length > 0
        ? this.currentWeekInfo
        : this.weekInfo

      if (!sourceWeekInfo || !sourceWeekInfo.weekDate) {
        return null
      }

      try {
        let currentStartDate = null

        // 首先尝试解析 YYYY-MM-DD 格式的日期
        if (/^\d{4}-\d{2}-\d{2}$/.test(sourceWeekInfo.weekDate)) {
          // 如果是 YYYY-MM-DD 格式，直接解析
          currentStartDate = dayjs(sourceWeekInfo.weekDate)
        } else if (sourceWeekInfo.weekDate.includes('-')) {
          // 如果是中文日期格式 "6月23日-6月29日"，解析第一个日期
          const dateRange = sourceWeekInfo.weekDate.split('-')
          if (dateRange.length === 2) {
            const startDateStr = dateRange[0].trim() // "6月23日"

            // 提取开始日期的月份和日期
            const startMonthMatch = startDateStr.match(/(\d+)月/)
            const startDayMatch = startDateStr.match(/(\d+)日/)

            if (startMonthMatch && startDayMatch) {
              const currentYear = dayjs().year()
              const startMonth = parseInt(startMonthMatch[1])
              const startDay = parseInt(startDayMatch[1])

              // 构造当前周的开始日期
              currentStartDate = dayjs().year(currentYear).month(startMonth - 1).date(startDay)
            }
          }
        }

        if (currentStartDate && currentStartDate.isValid()) {
          // 计算新的周开始日期
          const newStartDate = direction === 'previous'
            ? currentStartDate.subtract(7, 'day')
            : currentStartDate.add(7, 'day')

          const newEndDate = newStartDate.add(6, 'day')

          // 正确计算新的周次数字 - 基于新日期在其所在月份中的周次
          const newWeekNumber = this.calculateWeekNumberInMonth(newStartDate)

          const newWeekNo = this.generateWeekNo(newStartDate)
          return {
            weekDate: newStartDate.format('YYYY-MM-DD'),
            endDate: newEndDate.format('YYYY-MM-DD'),
            weekNumStr: `第${newWeekNumber}周`,
            weekNo: newWeekNo,
            name: this.generateWeekName(newStartDate, newEndDate)
          }
        }
      } catch (error) {
        console.error('计算周次变化失败:', error)
      }

      return null
    },

    // 计算日期在其所在月份中的周次
    calculateWeekNumberInMonth(date) {
      const firstDayOfMonth = date.startOf('month')
      const firstMondayOfMonth = firstDayOfMonth.day() === 1
        ? firstDayOfMonth
        : firstDayOfMonth.add(8 - firstDayOfMonth.day(), 'day')

      const weeksDiff = date.diff(firstMondayOfMonth, 'week')
      return Math.max(1, weeksDiff + 1)
    },

    // 生成weekNo格式 - 现在主要依赖API返回的数据，这个方法作为备用
    generateWeekNo(date) {
      // 注意：weekNo应该从API获取，这个方法仅作为备用
      // weekNo格式：YYYYMM + 周次序号
      // 例如：7月第一周 = 20250701，7月第二周 = 20250702
      const year = date.year()
      const month = date.month() + 1
      const weekInMonth = this.calculateWeekNumberInMonth(date)
      return `${year}${month.toString().padStart(2, '0')}${weekInMonth.toString().padStart(2, '0')}`
    },

    // 判断周次是否可以编辑（未过期）
    canEditWeek(weekInfo) {
      if (!weekInfo || !weekInfo.weekDate) {
        return true // 如果没有日期信息，默认允许编辑
      }

      try {
        // 解析周次的结束日期
        let endDate = null

        if (weekInfo.weekDate.includes('-')) {
          // 格式：6月23日-6月29日
          const dateRange = weekInfo.weekDate.split('-')
          if (dateRange.length === 2) {
            const endDateStr = dateRange[1].trim() // "6月29日"
            const endMonthMatch = endDateStr.match(/(\d+)月/)
            const endDayMatch = endDateStr.match(/(\d+)日/)

            if (endMonthMatch && endDayMatch) {
              const currentYear = dayjs().year()
              const endMonth = parseInt(endMonthMatch[1])
              const endDay = parseInt(endDayMatch[1])

              endDate = dayjs().year(currentYear).month(endMonth - 1).date(endDay)
            }
          }
        }

        if (!endDate || !endDate.isValid()) {
          return true // 如果无法解析日期，默认允许编辑
        }

        // 获取当前日期（只比较日期，不比较时间）
        const today = dayjs().startOf('day')
        const weekEndDate = endDate.endOf('day')

        // 如果周次结束日期在今天或今天之后，则可以编辑
        return weekEndDate.isAfter(today) || weekEndDate.isSame(today)

      } catch (error) {
        return true // 出错时默认允许编辑
      }
    },

    // 生成周次名称
    generateWeekName(startDate, endDate) {
      const month = startDate.month() + 1
      const monthNames = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二']
      const monthName = monthNames[month - 1] || month

      const weekNumber = this.getWeekNumber(startDate)
      const weekNumbers = ['一', '二', '三', '四', '五']
      const weekName = weekNumbers[weekNumber - 1] || weekNumber

      return `${monthName}月第${weekName}周菜谱（${startDate.format('M月D日')}-${endDate.format('M月D日')}）`
    },

    // 获取餐次图标
    getMealIcon(mealName) {
      const iconMap = {
        '早餐': 'md-sunny',
        '午餐': 'md-partly-sunny',
        '晚餐': 'md-moon'
      }
      return iconMap[mealName] || 'md-restaurant'
    },

    // 获取指定餐次的周次数据
    getMealWeekSubs(mealName) {
      // 查找对应餐次的数据
      const mealData = this.cookSubSNew.find(item =>
        item.mealName === mealName || item.name === mealName
      )

      if (mealData && mealData.cookWeekSubs) {
        return mealData.cookWeekSubs
      }

      // 如果没有找到，返回空的7天数据结构
      return Array.from({ length: 7 }, (_, index) => ({
        weekId: index + 1,
        cooks: []
      }))
    },

    // 获取周次数据
    handleGetZdCPZS() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_CPZS.js" }).then(res => {
        let week = eval('(' + res + ')')
        this.weekList = week()
      })
    },

    // 获取餐次数据
    handleGetZdDSLX() {
      this.authGetRequest({ url: "/bsp-com/static/dic/pam/ZD_PCGL_DSLX.js" }).then(res => {
        let numTon = eval('(' + res + ')')
        this.numTonList = numTon()
      })
    },

    // 复制昨日菜谱
    copyDayUpdateFlag(day) {
      return this.hasSubListDTOSForWeekIdUpdate(day - 1);
    },

    copyUpdateDay(index) {
      for (let i = 0; i < this.cookSubSNew.length; i++) {
        const item = this.cookSubSNew[i];
        if (item.cookWeekSubs[index]) {
          item.cookWeekSubs[index].cooks = Object.assign([], item.cookWeekSubs[index - 1].cooks);
        }
      }
    },

    delSubUpdate(menuIndex, index, idx) {
      this.cookSubSNew[menuIndex].cookWeekSubs[index].cooks.splice(idx, 1);
    },

    hasSubListDTOSForWeekIdUpdate(index) {
      for (let i = 0; i < this.cookSubSNew.length; i++) {
        const item = this.cookSubSNew[i];
        if (item.cookWeekSubs[index] && item.cookWeekSubs[index].cooks.length > 0) {
          return false;
        }
      }
      return true;
    },

    // 菜谱模板详情
    async handleGetRecipeTemDetail() {
      try {
        const res = await this.authGetRequest({
          url: this.$path.catering_recipe_tem_get,
          params: { id: this.recipeEditInfo.id }
        })

        if (res.success) {
          this.cookMeals = res.data.cookMeals
          this.cookSubSNew = JSON.parse(JSON.stringify(res.data.cookSubs))
        }
      } catch (error) {
        // 静默处理错误
      }
    },

    // 加载周次菜谱数据
    async loadWeekRecipeData(weekInfo, isWeekChange = false) {
      try {
        // 初始化默认的餐次结构
        this.cookMeals = [
          {
            name: "早餐",
            weekId: "1,2,3,4,5,6,7",
            sortNo: 1
          },
          {
            name: "午餐",
            weekId: "1,2,3,4,5,6,7",
            sortNo: 2
          },
          {
            name: "晚餐",
            weekId: "1,2,3,4,5,6,7",
            sortNo: 3
          }
        ]

        // 初始化菜谱配置结构
        this.cookSubSNew = this.cookMeals.map(meal => ({
          mealName: meal.name,
          name: meal.name,
          sortNo: meal.sortNo,
          cookWeekSubs: Array.from({ length: 7 }, (_, index) => ({
            weekId: index + 1,
            cooks: []
          }))
        }))

        // 如果是周次切换且已经有tempId，直接加载菜谱详情
        if (isWeekChange && this.recipeEditInfo.id) {
          await this.handleGetRecipeTemDetail()
        } else if (weekInfo && (weekInfo.weekNo || weekInfo.weekDate)) {
          // 非周次切换的情况，或者周次切换但没有tempId的情况，通过API查询
          try {
            // 首先尝试通过API获取周次菜谱数据
            await this.loadWeekRecipeFromAPI(weekInfo)
          } catch (apiError) {
            // API失败时，尝试从本地存储加载
            await this.loadWeekRecipeFromLocalStorage(weekInfo)
          }
        }

        // 确保数据结构完整性
        this.$nextTick(() => {
          this.$forceUpdate()
        })

      } catch (error) {
        // 即使加载失败，也要确保有基本的数据结构
        this.initializeEmptyRecipeStructure()
      }
    },

    // 通过API加载周次菜谱数据
    async loadWeekRecipeFromAPI(weekInfo) {
      try {
        // 构建查询参数
        const params = {
          weekNo: weekInfo.weekNo,
          weekDate: weekInfo.weekDate
        }

        const res = await this.authGetRequest({
          url: this.$path.catering_recipe_tem_getIndexList,
          params
        })

        if (res.success && res.data) {
          let weekRecipeData = null

          Object.keys(res.data).forEach(monthKey => {
            const monthData = res.data[monthKey]
            if (Array.isArray(monthData)) {
              const foundWeek = monthData.find(week =>
                week.weekNo === weekInfo.weekNo ||
                week.weekDate === weekInfo.weekDate
              )
              if (foundWeek && foundWeek.tempId) {
                weekRecipeData = foundWeek
              }
            }
          })

          if (weekRecipeData && weekRecipeData.tempId) {
            this.recipeEditInfo.id = weekRecipeData.tempId
            await this.handleGetRecipeTemDetail()
          } else {
            this.recipeEditInfo.id = ""
          }
        }
      } catch (error) {
        throw error
      }
    },

    // 从本地存储加载周次菜谱数据
    async loadWeekRecipeFromLocalStorage(weekInfo) {
      try {
        const savedRecipeKey = `week_recipe_${weekInfo.weekNo}`
        const savedRecipe = localStorage.getItem(savedRecipeKey)

        if (savedRecipe) {
          try {
            const recipeData = JSON.parse(savedRecipe)

            if (recipeData.cookMeals) {
              this.cookMeals = recipeData.cookMeals
            }
            if (recipeData.cookSubs) {
              this.cookSubSNew = recipeData.cookSubs
            }
            if (recipeData.id) {
              this.recipeEditInfo.id = recipeData.id
            }
          } catch (parseError) {
            // 静默处理解析错误
          }
        }
      } catch (error) {
        // 静默处理加载错误
      }
    },

    // 初始化空的菜谱结构
    initializeEmptyRecipeStructure() {
      this.cookMeals = [
        {
          name: "早餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 1
        },
        {
          name: "午餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 2
        },
        {
          name: "晚餐",
          weekId: "1,2,3,4,5,6,7",
          sortNo: 3
        }
      ]

      this.cookSubSNew = this.cookMeals.map(meal => ({
        mealName: meal.name,
        name: meal.name,
        sortNo: meal.sortNo,
        cookWeekSubs: Array.from({ length: 7 }, (_, index) => ({
          weekId: index + 1,
          cooks: []
        }))
      }))
    },

    // 保存菜谱模板
    handleUpdateRecipeTem() {
      if (this.saving) {
        return
      }

      this.$refs['recipeTemform'].validate(async (valid) => {
        if (valid) {
          // 设置保存状态，防止重复提交
          this.saving = true

          // 先保存到本地存储（用于周次切换时加载）
          this.saveRecipeToLocalStorage()

          // 如果是新增模式，先检查是否已存在该周次的菜谱
          if (!this.recipeEditInfo.id && !this.editInfo?.id) {
            try {
              await this.checkWeekRecipeExists()
            } catch (error) {
              // 检查失败不影响保存流程
            }
          }

          // 判断是创建还是更新
          const hasRecipeId = this.recipeEditInfo.id && this.recipeEditInfo.id !== ""
          const hasWeekChanged = this.hasPerformedWeekChange
          const hasEditId = !hasWeekChanged && this.editInfo && this.editInfo.id && this.editInfo.id !== ""
          const isUpdate = hasRecipeId || hasEditId

          const url = isUpdate
            ? this.$path.catering_recipe_tem_update
            : this.$path.catering_recipe_tem_create

          const params = {
            cookMeals: this.cookMeals,
            cookSubs: this.cookSubSNew,
            templateName: this.recipeTemform.templateName
          }

          // 如果是更新，添加ID参数
          if (isUpdate) {
            params.id = hasRecipeId ? this.recipeEditInfo.id : this.editInfo.id
          }

          // 添加周次信息，确保唯一性
          const currentWeekInfo = this.currentWeekInfo && Object.keys(this.currentWeekInfo).length > 0
            ? this.currentWeekInfo
            : this.weekInfo

          if (currentWeekInfo) {
            params.weekNo = currentWeekInfo.weekNo
            params.weekDate = currentWeekInfo.weekDate
            params.weekNumStr = currentWeekInfo.weekNumStr

            // 添加startTime和endTime字段
            if (currentWeekInfo.weekDate) {
              params.startTime = this.convertToStandardDate(currentWeekInfo.weekDate, true)
              params.endTime = this.convertToStandardDate(currentWeekInfo.weekDate, false)
            }

            // 如果有单独的结束日期，优先使用它
            if (currentWeekInfo.endDate) {
              params.endDate = currentWeekInfo.endDate
              params.endTime = this.convertToStandardDate(currentWeekInfo.endDate, false)
            }
          }

          this.authPostRequest({
            url: url,
            params: params
          }).then(async (res) => {
            if (res.success) {
              const message = isUpdate ? "菜谱更新成功" : "菜谱保存成功"
              this.$Message.success(message)

              // 如果是新创建的，更新编辑信息ID
              if (!isUpdate && res.data && res.data.id) {
                this.recipeEditInfo.id = res.data.id
              }

              this.$emit('save-success')
            } else {
              this.$Message.error(res.message || '保存菜谱失败，请重试')
            }
          }).catch(() => {
            this.$Message.error('保存菜谱失败，请重试')
          }).finally(() => {
            this.saving = false
          })
        } else {
          // 验证失败时也要重置保存状态
          this.saving = false
          this.$Message.error('验证未通过');
        }
      })
    },

    // 检查周次菜谱是否已存在
    async checkWeekRecipeExists() {
      const currentWeekInfo = this.currentWeekInfo && Object.keys(this.currentWeekInfo).length > 0
        ? this.currentWeekInfo
        : this.weekInfo

      if (!currentWeekInfo) {
        return false
      }

      try {
        const params = {
          weekNo: currentWeekInfo.weekNo,
          weekDate: currentWeekInfo.weekDate
        }

        const res = await this.authGetRequest({
          url: this.$path.catering_recipe_tem_getIndexList,
          params
        })

        if (res.success && res.data) {
          // 查找是否已存在该周次的菜谱
          let existingRecipe = null
          Object.keys(res.data).forEach(monthKey => {
            const monthData = res.data[monthKey]
            if (Array.isArray(monthData)) {
              const foundWeek = monthData.find(week =>
                week.weekNo === currentWeekInfo.weekNo ||
                week.weekDate === currentWeekInfo.weekDate
              )
              if (foundWeek && foundWeek.tempId) {
                existingRecipe = foundWeek
              }
            }
          })

          if (existingRecipe) {
            this.recipeEditInfo.id = existingRecipe.tempId
            return true
          }
        }
        return false
      } catch (error) {
        console.error('检查周次菜谱存在性失败:', error)
        return false
      }
    },

    // 保存菜谱到本地存储
    saveRecipeToLocalStorage() {
      try {
        const currentWeekInfo = this.currentWeekInfo && Object.keys(this.currentWeekInfo).length > 0
          ? this.currentWeekInfo
          : this.weekInfo

        if (currentWeekInfo && currentWeekInfo.weekNo) {
          const recipeData = {
            id: this.recipeEditInfo.id,
            cookMeals: this.cookMeals,
            cookSubs: this.cookSubSNew,
            templateName: this.recipeTemform.templateName,
            weekInfo: currentWeekInfo,
            savedAt: new Date().toISOString()
          }

          const savedRecipeKey = `week_recipe_${currentWeekInfo.weekNo}`
          localStorage.setItem(savedRecipeKey, JSON.stringify(recipeData))
        }
      } catch (error) {
        // 静默处理保存错误
      }
    },

    // 获取分类列表（兼容原有调用，完整加载分类和第一个分类的菜品）
    async handleGetClassifyList() {
      try {
        await this.loadCategoriesOnly()

        if (this.caterClassifyList.length > 0) {
          this.cateId = this.caterClassifyList[0].id.toString()
          this.cateName = this.caterClassifyList[0].cateName
          this.currentTabIndex = 0
          this.currentTabName = this.cateId
          // 初始化分类颜色
          this.initializeCategoryColors()
          await this.handleDefaultDashesList()
        }
      } catch (error) {
        // 静默处理错误
      }
    },

    // 获取菜品名称列表
    async handleDefaultDashesList(cookName = "") {
      try {
        // 确保有分类数据
        if (!this.caterClassifyList || this.caterClassifyList.length === 0) {
          this.dashesList = []
          return
        }

        const currentCateId = this.cateId || this.caterClassifyList[0].id.toString()
        const currentCateName = this.cateName || this.caterClassifyList[0].cateName

        const res = await this.authPostRequest({
          url: this.$path.catering_dashes_list,
          params: { cateId: currentCateId, cookName: cookName }
        })

        if (res.success) {
          const processedList = (res.data || []).map((item) => {
            return {
              ...item,
              name: item.cookName,
              cateId: item.cateId || currentCateId,
              cookType: currentCateName
            }
          })

          this.$set(this, 'dashesList', processedList)
          this.dashesName = ""

          // 强制更新视图
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        }
      } catch (error) {
        this.dashesList = []
      }
    },

    // 检查拖拽移动
    checkMove(evt) {
      if (evt.to.id === "left-draggable") {
        return false;
      }
      let obj = evt.draggedContext.element;
      let arr = evt.relatedContext.list;
      let found = false;
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].cookType === obj.cookType && arr[i].name === obj.name) {
          found = true;
          break;
        }
      }
      // 移除数量限制，只检查重复项
      if (found) {
        return false;
      }
    },

    // 创建菜品
    async handleAddDashes() {
      if (!this.adddashesName.trim()) {
        this.$Message.warning('请输入菜品名称')
        return
      }

      this.dishesLoading = true
      try {
        const res = await this.authPostRequest({
          url: this.$path.catering_create_dashes,
          params: {
            cateId: this.cateId ? this.cateId : this.caterClassifyList[0].id,
            cookName: this.adddashesName,
            "sort": 0
          }
        })
        if (res.success) {
          this.$Message.success("创建菜品成功")
          this.adddashesName = ""
          await this.handleDefaultDashesList()
        }
      } catch (error) {
        console.error('添加菜品失败:', error)
        this.$Message.error('添加菜品失败，请重试')
      } finally {
        this.dishesLoading = false
      }
    },

    // 更新菜品
    handleUpateDahesName(item) {
      this.isChangeDashesModal = true
      this.dashesItem = item
      this.authGetRequest({ url: this.$path.catering_get_dashes, params: { id: item.id } }).then(res => {
        if (res.success) {
          this.formDashes.cookName = res.data.cookName
        }
      })
    },

    // 删除菜品
    handleDeleteDahesName(item) {
      this.$Modal.confirm({
        title: '温馨提示',
        content: '请确认是否删除？',
        onOk: async () => {
          this.dishesLoading = true
          try {
            const res = await this.authGetRequest({
              url: this.$path.catering_delete_dashes,
              params: { ids: item.id }
            })
            if (res.success) {
              this.$Message.success('菜品删除成功')
              await this.handleDefaultDashesList()
            }
          } catch (error) {
            console.error('删除菜品失败:', error)
            this.$Message.error('删除菜品失败，请重试')
          } finally {
            this.dishesLoading = false
          }
        }
      })
    },

    // 修改菜品模态框的提交按钮事件
    handleDashesSubmit() {
      this.$refs['formDashes'].validate(async (valid) => {
        if (valid) {
          this.dishesLoading = true
          try {
            const res = await this.authPostRequest({
              url: this.$path.catering_update_dashes,
              params: {
                cateId: this.cateId ? this.cateId : this.caterClassifyList[0].id,
                cookName: this.formDashes.cookName,
                id: this.dashesItem.id,
                sort: 0
              }
            })
            if (res.success) {
              this.$Message.success('菜品名称更新成功')
              this.isChangeDashesModal = false
              await this.handleDefaultDashesList()
            }
          } catch (error) {
            console.error('更新菜品失败:', error)
            this.$Message.error('更新菜品失败，请重试')
          } finally {
            this.dishesLoading = false
          }
        }
      })
    },

    // 修改菜品模态框的取消按钮事件
    handleDashesCancel() {
      this.isChangeDashesModal = false
      this.$refs['formDashes'].resetFields();
    },

    // 搜索菜品事件
    async handleSearchDashes() {
      this.dishesLoading = true
      try {
        await this.handleDefaultDashesList(this.dashesName)
      } catch (error) {
        console.error('搜索菜品失败:', error)
        this.$Message.error('搜索菜品失败，请重试')
      } finally {
        this.dishesLoading = false
      }
    },

    // 获取分类颜色
    getCategoryColor(cateId) {
      // 首先查找分类数据中是否有颜色信息
      const category = this.caterClassifyList.find(item => item.id === cateId)
      if (category && category.cateColor) {
        // 如果数据库中有颜色，直接使用并更新本地缓存
        this.$set(this.categoryColorMap, cateId, category.cateColor)
        return category.cateColor
      }

      // 如果本地缓存中有颜色，使用缓存的颜色
      if (this.categoryColorMap[cateId]) {
        return this.categoryColorMap[cateId]
      }

      // 如果都没有，分配一个默认颜色（但不保存到数据库）
      const usedColors = Object.values(this.categoryColorMap)
      const availableColors = this.colorPool.filter(color => !usedColors.includes(color))

      let assignedColor
      if (availableColors.length > 0) {
        assignedColor = availableColors[Math.floor(Math.random() * availableColors.length)]
      } else {
        assignedColor = this.colorPool[Math.floor(Math.random() * this.colorPool.length)]
      }

      // 只保存到本地缓存，不保存到数据库
      this.$set(this.categoryColorMap, cateId, assignedColor)
      this.saveCategoryColors()
      return assignedColor
    },

    // 初始化分类颜色
    initializeCategoryColors() {
      // 从localStorage加载已保存的颜色映射
      const savedColors = localStorage.getItem('categoryColorMap')
      if (savedColors) {
        try {
          this.categoryColorMap = JSON.parse(savedColors)
        } catch (e) {
          console.warn('加载颜色映射失败:', e)
        }
      }

      if (this.caterClassifyList && this.caterClassifyList.length > 0) {
        this.caterClassifyList.forEach((item, index) => {
          // 优先使用数据库中的颜色
          if (item.cateColor) {
            this.$set(this.categoryColorMap, item.id, item.cateColor)
          } else if (!this.categoryColorMap[item.id]) {
            // 如果数据库中没有颜色且本地缓存也没有，分配一个默认颜色
            const colorIndex = index % this.colorPool.length
            this.$set(this.categoryColorMap, item.id, this.colorPool[colorIndex])
          }
        })

        // 保存颜色映射到localStorage
        this.saveCategoryColors()
      }
    },

    // 保存颜色映射到localStorage
    saveCategoryColors() {
      try {
        localStorage.setItem('categoryColorMap', JSON.stringify(this.categoryColorMap))
      } catch (e) {
        console.warn('保存颜色映射失败:', e)
      }
    },

    // 功能切换事件
    async switchFunction(functionName) {
      this.activeFunction = functionName // 更新激活的功能

      if (functionName === 'dishes') { // 菜品管理功能
        // 首次切换到菜品管理时加载分类数据
        if (this.caterClassifyList.length === 0) {
          this.dishesLoading = true

          try {
            // 加载分类列表和第一个分类的菜品数据
            await this.loadDishCategoriesAndFirstCategory()
          } catch (error) {
            console.error('加载菜品数据失败:', error)
            this.$Message.error('加载菜品数据失败，请重试')
          } finally {
            this.dishesLoading = false
          }
        }
      } else if (functionName === 'template') {
        // 模板数据在初始化时已加载，无需重复加载
        // 如果模板列表为空，可能是初始加载失败，重新加载
        if (this.templateList.length === 0 && !this.templateLoading) {
          this.templateLoading = true
          try {
            await this.getTempListAsync()
          } catch (error) {
            console.error('加载模板数据失败:', error)
            this.$Message.error('加载模板数据失败，请重试')
          } finally {
            this.templateLoading = false
          }
        }
      }
    },

    // 加载菜品分类和第一个分类的菜品数据
    async loadDishCategoriesAndFirstCategory() {
      try {
        // 加载分类列表
        await this.loadCategoriesOnly()

        // 如果有分类数据，加载第一个分类的菜品
        if (this.caterClassifyList.length > 0) {
          const firstCategory = this.caterClassifyList[0]
          this.cateId = firstCategory.id.toString()
          this.cateName = firstCategory.cateName
          this.currentTabIndex = 0
          this.currentTabName = this.cateId

          // 初始化分类颜色
          this.initializeCategoryColors()

          // 加载第一个分类的菜品数据
          await this.handleDefaultDashesList()
        }
      } catch (error) {
        console.error('加载菜品分类和数据失败:', error)
        throw error
      }
    },

    // 只加载分类列表，不加载菜品数据
    async loadCategoriesOnly() {
      try {
        let params = {
          cateName: "",
          orgCode: "",
        }

        const res = await this.authPostRequest({
          url: this.$path.catering_list,
          params
        })

        if (res.success) {
          if (res.data && res.data.length > 0) {
            this.caterClassifyList = res.data
          } else {
            this.caterClassifyList = []
          }
        }
      } catch (error) {
        console.error('获取分类列表失败:', error)
        throw error
      }
    },

    // 模板搜索事件
    handleSearchTemplate() {
      // 搜索逻辑已通过计算属性 filteredTemplateList 实现
    },

    // 模板搜索输入变化事件
    handleTemplateSearchChange() {
      // 实时搜索，无需额外处理
    },

    // 菜品分类选项卡切换事件
    async handleTabChange(name) {
      const id = name.toString();

      // 如果切换到当前已选中的分类，无需重新加载
      if (this.cateId === id) {
        return
      }

      this.cateId = id

      // 找到对应的分类名称和索引
      const categoryIndex = this.caterClassifyList.findIndex(item => item.id.toString() === id)
      const category = this.caterClassifyList.find(item => item.id.toString() === id)

      if (category) {
        this.cateName = category.cateName
        this.currentTabIndex = categoryIndex >= 0 ? categoryIndex : 0
        this.currentTabName = id

        // 显示加载状态
        this.dishesLoading = true

        try {
          // 加载该分类下的菜品数据
          await this.handleDefaultDashesList()
        } catch (error) {
          console.error('切换分类失败:', error)
          this.$Message.error('加载菜品数据失败，请重试')
        } finally {
          this.dishesLoading = false
        }
      }
    },

    // 获取菜谱模板列表
    getTempList() {
      const params = {
        isCopy: "",
        orgCode: "",
        templateName: "",
        weekNo: ""
      }

      this.authPostRequest({
        url: this.$path.catering_recipe_tem_list,
        params
      }).then(res => {
        if (res.success) {
          this.templateList = res.data || [];
        }
      }).catch(() => {
        this.templateList = []
      })
    },

    // 异步版本的模板列表加载
    async getTempListAsync() {
      const params = {
        isCopy: "",
        orgCode: "",
        templateName: "",
        weekNo: ""
      }

      try {
        console.log('开始加载菜谱模板数据...')
        const res = await this.authPostRequest({
          url: this.$path.catering_recipe_tem_list,
          params
        })

        if (res.success) {
          this.templateList = res.data || [];
          console.log('菜谱模板数据加载成功，共', this.templateList.length, '条记录')
        } else {
          console.warn('菜谱模板数据加载失败:', res.message || '未知错误')
          this.templateList = []
        }
      } catch (error) {
        console.error('菜谱模板数据加载异常:', error)
        this.templateList = []
        throw error
      }
    },

    // 模板复用
    async multiplex111(item) {
      if (item.id) {
        try {
          console.log('=== 模板复用开始 ===')
          console.log('复用模板ID:', item.id)
          console.log('当前菜谱ID（保持不变）:', this.recipeEditInfo.id)

          // 保存当前菜谱的ID，不能被模板ID覆盖
          const currentRecipeId = this.recipeEditInfo.id
          const currentEditId = this.editInfo?.id

          // 获取模板数据
          const res = await this.authGetRequest({
            url: this.$path.catering_recipe_tem_get,
            params: { id: item.id }
          })

          if (res.success && res.data) {
            console.log('获取模板数据成功:', res.data)

            // 只复用菜谱配置数据，不改变当前菜谱的ID和名称
            this.cookMeals = res.data.cookMeals
            this.cookSubSNew = JSON.parse(JSON.stringify(res.data.cookSubs))

            // 恢复当前菜谱的ID，确保保存时不会创建新记录
            this.recipeEditInfo.id = currentRecipeId
            if (this.editInfo) {
              this.editInfo.id = currentEditId
            }

            console.log('模板复用完成，当前菜谱ID保持为:', this.recipeEditInfo.id)
            this.$Message.success(`已复用模板：${item.templateName}`)
          } else {
            console.error('获取模板数据失败:', res)
            this.$Message.error('复用模板失败，请重试')
          }
        } catch (error) {
          console.error('模板复用失败:', error)
          this.$Message.error('复用模板失败，请重试')
        }
      }
    },

    // 异步加载菜谱模板数据
    async loadTemplateDataAsync() {
      try {
        this.templateLoading = true
        await this.getTempListAsync()

        // 确保数据加载成功后强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      } catch (error) {
        console.warn('加载模板数据失败:', error)
        // 如果加载失败，设置空数组确保界面正常显示
        this.templateList = []
      } finally {
        this.templateLoading = false
      }
    },

    // 异步加载基础数据
    async loadBasicDataAsync() {
      try {
        // 并行加载周次和餐次数据
        const promises = [
          this.handleGetZdCPZS(),
          this.handleGetZdDSLX()
        ]

        // 等待基础数据加载完成
        await Promise.all(promises)

        // 菜品分类数据按需加载（当用户切换到菜品管理时再加载）
      } catch (error) {
        console.warn('加载基础数据失败:', error)
      }
    }
  },

  components: {
    draggable
  },

  async created() {
    try {
      // 初始化当前周次信息
      if (this.weekInfo) {
        this.currentWeekInfo = { ...this.weekInfo }
      }

      // 初始化时重置周次切换标记
      this.hasPerformedWeekChange = false

      // 生成默认菜谱名称
      const defaultRecipeName = this.generateDefaultRecipeName()
      this.recipeTemform.templateName = defaultRecipeName
      this.currentWeekDisplay = defaultRecipeName

      // 加载模板数据
      await this.loadTemplateDataAsync()

      // 如果有编辑信息，加载菜谱详情
      if (this.editInfo && this.editInfo.id) {
        this.recipeEditInfo.id = this.editInfo.id
        await this.handleGetRecipeTemDetail()
      }

      // 并行加载基础数据
      this.loadBasicDataAsync()

    } catch (error) {
      // 确保模板数据能够加载
      this.$nextTick(() => {
        this.loadTemplateDataAsync().catch(() => {
          // 静默处理错误
        })
      })
    }
  },

  async mounted() {
    // 如果模板数据没有加载成功，重新尝试
    if (this.templateList.length === 0 && !this.templateLoading) {
      try {
        await this.loadTemplateDataAsync()
      } catch (error) {
        // 静默处理错误
      }
    }
  },

  computed: {
    // 过滤后的模板列表
    filteredTemplateList() {
      if (!this.templateSearchName || !this.templateSearchName.trim()) {
        return this.templateList
      }
      const searchTerm = this.templateSearchName.toLowerCase().trim()
      return this.templateList.filter(template =>
        template.templateName && template.templateName.toLowerCase().includes(searchTerm)
      )
    },

    // 计算是否可以切换到上一周
    canGoPrevious() {
      const previousWeekInfo = this.calculateWeekChange('previous')
      return previousWeekInfo && this.canEditWeek(previousWeekInfo)
    },

    // 计算是否可以切换到下一周
    canGoNext() {
      const nextWeekInfo = this.calculateWeekChange('next')
      return nextWeekInfo && this.canEditWeek(nextWeekInfo)
    }
  },

  watch: {
    weekInfo: {
      handler(newVal, oldVal) {
        if (newVal) {
          const newDefaultName = this.generateDefaultRecipeName()
          this.currentWeekDisplay = newDefaultName

          // 如果是周期切换导致的变化，更新菜谱名称
          if (oldVal && oldVal.weekDate !== newVal.weekDate) {
            this.recipeTemform.templateName = newDefaultName
          }
          // 如果是初始化且没有名称，设置默认名称
          else if (!this.recipeTemform.templateName || this.recipeTemform.templateName === '') {
            this.recipeTemform.templateName = newDefaultName
          }
        }
      },
      deep: true,
      immediate: true
    },

    currentWeekInfo: {
      handler(newVal) {
        if (newVal) {
          this.currentWeekDisplay = this.generateDefaultRecipeName()
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="less" scoped>
.recipe-edit-container {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 编辑页面头部
  .edit-header {
    background: linear-gradient(135deg, #ffffff, #fafbfc);
    border: 2px solid #e9edf5;
    border-bottom: none;
    padding: 20px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .header-left {
      flex: 1;
      max-width: 600px;

      .recipe-name-section {
        display: flex;
        align-items: center;
        gap: 12px;

        .recipe-label {
          font-size: 16px;
          font-weight: 600;
          color: #2b3346;
          white-space: nowrap;
        }

        .inline-form {
          flex: 1;
          max-width: 400px;

          /deep/ .ivu-form-item {
            margin-bottom: 0;
          }

          .recipe-name-input {
            font-size: 16px;
            font-weight: 500;
            border: 2px solid #e9edf5;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:focus {
              border-color: #2b5fda;
              box-shadow: 0 0 0 3px rgba(43, 95, 218, 0.1);
            }

            /deep/ input {
              font-size: 16px;
              font-weight: 500;
              color: #2b3346;
            }
          }
        }
      }
    }

    .header-right {
      flex: 0 0 auto;
      margin-left: 24px;

      .week-navigation {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        background: #f8faff;
        border: 2px solid #e9edf5;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .week-label {
          font-size: 14px;
          font-weight: 600;
          color: #2b3346;
          white-space: nowrap;
        }

        .week-nav-btn {
          border-radius: 8px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            background: #2b5fda;
            color: white;
            border-color: #2b5fda;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(43, 95, 218, 0.3);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }

        .current-week-display {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: white;
          border: 1px solid #e9edf5;
          border-radius: 8px;
          font-size: 13px;
          font-weight: 600;
          color: #2b3346;
          white-space: nowrap;
          min-width: 220px;
          justify-content: center;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

          i {
            font-size: 14px;
          }
        }
      }
    }
  }

  // 编辑内容区域
  .edit-content {
    flex: 1;
    display: flex;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
    background: #f8f9fa;

    // 左侧面板
    .left-panel {
      width: 400px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .panel-card {
        background: #ffffff;
        border: 1px solid #e9edf5;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;

        // 功能选择器
        .function-selector {
          padding: 20px 20px 0;
          border-bottom: 1px solid #f0f2f5;

          .function-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;

            .ivu-btn {
              flex: 1;
              height: 40px;
              border-radius: 8px;
              font-weight: 500;
              transition: all 0.3s ease;

              &.active {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(43, 95, 218, 0.3);
              }
            }
          }
        }

        // 模板区域
        .template-section, .dishes-section {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          padding: 20px;

          .section-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 600;
            color: #2b3346;
          }

          .search-container, .search-section {
            margin-bottom: 16px;
          }

          .template-list, .dishes-content {
            flex: 1;
            overflow-y: auto;
          }

          .template-card {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e9edf5;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #2b5fda;
              box-shadow: 0 4px 12px rgba(43, 95, 218, 0.15);
              transform: translateY(-1px);
            }

            .template-icon {
              width: 32px;
              height: 32px;
              margin-right: 12px;
              display: flex;
              align-items: center;
              justify-content: center;

              img {
                width: 24px;
                height: 24px;
              }
            }

            .template-info {
              flex: 1;

              .template-name {
                font-size: 14px;
                font-weight: 500;
                color: #2b3346;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .template-meta {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: #6b7280;
              }
            }

            .template-action {
              margin-left: 8px;
            }
          }
        }

        // 菜品区域特有样式
        .dishes-section {
          .dishes-tabs {
            margin-bottom: 16px;

            /deep/ .ivu-tabs-bar {
              margin-bottom: 16px;
            }
          }

          .add-dish-section {
            margin-bottom: 16px;

            .add-dish-form {
              display: flex;
              gap: 8px;
            }
          }

          .dishes-list-container {
            flex: 1;
            overflow-y: auto;

            .dish-card {
              background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
              border: 1px solid #d1e7ff;
              border-radius: 6px;
              margin-bottom: 6px;
              position: relative;
              overflow: hidden;
              transition: all 0.3s ease;
              box-shadow: 0 1px 3px rgba(43, 95, 218, 0.1);
              cursor: grab;
              min-height: 48px;
              display: flex;
              align-items: center;

              &:hover {
                border-color: #2b5fda;
                box-shadow: 0 3px 8px rgba(43, 95, 218, 0.2);
                transform: translateY(-1px);

                .dish-actions {
                  opacity: 1;
                }
              }

              &:active {
                cursor: grabbing;
              }

              .dish-category {
                width: 60px;
                height: 48px;
                line-height: 48px;
                text-align: center;
                color: #fff;
                font-weight: 600;
                font-size: 12px;
                flex-shrink: 0;
              }

              .dish-name {
                flex: 1;
                font-size: 14px;
                font-weight: 500;
                color: #2b3346;
                padding: 0 12px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.4;
              }

              .dish-actions {
                padding: 0 12px;
                display: flex;
                gap: 8px;
                opacity: 0;
                transition: opacity 0.3s ease;

                .action-icon {
                  font-size: 16px;
                  cursor: pointer;
                  transition: color 0.3s ease;

                  &.edit-icon:hover {
                    color: #2b5fda;
                  }

                  &.delete-icon:hover {
                    color: #ed4014;
                  }
                }
              }

              .drag-handle {
                padding: 0 8px;
                color: #c5c8ce;
                cursor: grab;

                &:active {
                  cursor: grabbing;
                }
              }
            }
          }
        }

        .empty-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 200px;
          color: #9ca3af;
          font-size: 14px;
          font-weight: 500;
          background: #fafbfc;
          border: 2px dashed #e2e8f0;
          border-radius: 8px;
          margin: 20px 0;

          p {
            margin: 0;
          }
        }

        .loading-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 200px;
          color: #666;
          font-size: 14px;
          background: #fafbfc;
          border-radius: 8px;
          margin: 20px 0;

          .spin-icon-load {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        }
      }
    }

    // 右侧面板
    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .panel-card {
        background: #ffffff;
        border: 1px solid #e9edf5;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;

        .recipe-table-header {
          padding: 20px 24px;
          background: linear-gradient(135deg, #f8faff, #f0f6ff);
          border-bottom: 1px solid #e9edf5;

          .table-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 18px;
            font-weight: 600;
            color: #2b3346;
          }
        }

        .recipe-table-wrapper {
          flex: 1;
          overflow: hidden;
          padding: 20px;

          .table-container {
            height: 100%;
            overflow: auto;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            background: #ffffff;
          }
        }
      }
    }
  }



  // 菜谱配置表格样式
  .recipe-config-table {
    width: 100%;
    min-width: 1200px;
    border-collapse: collapse;
    background: #ffffff;

    .table-header-row {
      background: linear-gradient(135deg, #f8faff, #f0f6ff);

      .meal-header, .day-header {
        padding: 16px 12px;
        border: 1px solid #e2e8f0;
        text-align: center;
        font-weight: 600;
        color: #2b3346;
        background: linear-gradient(135deg, #f8faff, #f0f6ff);

        .header-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          font-size: 14px;
        }

        .day-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;

          .day-name {
            font-size: 14px;
            font-weight: 600;
            color: #2b3346;
          }
        }
      }

      .meal-header {
        width: 120px;
        min-width: 120px;
      }

      .day-header {
        width: 140px;
        min-width: 140px;
      }
    }

    .meal-row {
      &:nth-child(even) {
        background: #fafbfc;
      }

      .meal-cell {
        padding: 16px 12px;
        border: 1px solid #e2e8f0;
        text-align: center;
        background: linear-gradient(135deg, #f8faff, #f0f6ff);
        vertical-align: top;

        .meal-info {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-size: 14px;
          font-weight: 600;
          color: #2b3346;

          .meal-name {
            white-space: nowrap;
          }
        }
      }

      .dish-cell {
        padding: 12px;
        border: 1px solid #e2e8f0;
        vertical-align: top;
        position: relative;

        .drop-zone {
          min-height: 80px;
          border: 2px dashed #e2e8f0;
          border-radius: 8px;
          padding: 8px;
          transition: all 0.3s ease;
          background: #fafbfc;

          &:hover {
            border-color: #2b5fda;
            background: #f8faff;
          }

          .dish-item {
            display: flex;
            align-items: center;
            background: #ffffff;
            border: 1px solid #e9edf5;
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 6px;
            position: relative;
            transition: all 0.3s ease;

            &:last-child {
              margin-bottom: 0;
            }

            &:hover {
              border-color: #2b5fda;
              box-shadow: 0 2px 8px rgba(43, 95, 218, 0.15);
            }

            .dish-category-indicator {
              width: 3px;
              height: 100%;
              position: absolute;
              left: 0;
              top: 0;
              border-radius: 3px 0 0 3px;
            }

            .dish-content {
              flex: 1;
              margin-left: 8px;

              .dish-name {
                font-size: 13px;
                font-weight: 500;
                color: #2b3346;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 100px;
              }
            }

            .dish-remove {
              width: 20px;
              height: 20px;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #ed4014;
              color: #ffffff;
              border-radius: 50%;
              cursor: pointer;
              font-size: 12px;
              transition: all 0.2s ease;
              margin-left: 8px;

              &:hover {
                background: #c73e1d;
                transform: scale(1.1);
              }
            }
          }

          .drop-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            height: 60px;
            color: #9ca3af;
            font-size: 12px;
            font-weight: 500;

            span {
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  // 拖拽相关样式
  .dragging-item {
    opacity: 0.8;
    transform: rotate(5deg);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }

  .ghost-item {
    opacity: 0.4;
    background: #f0f6ff;
    border: 2px dashed #2b5fda;
  }

  .chosen-item {
    background: #e6f3ff;
    border-color: #2b5fda;
    transform: scale(1.02);
  }
}
</style>
