<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpsfcsksszsdjlbbr'  />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbz'  />
      <statisticalAnalysis mark='acpsfcsksszsdjlbby'  />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbjd'  />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbn'  />
      <statisticalAnalysis mark='acpsfcsksszsdjlbls'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="sfcs-kss-zsdjtz" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
             <span style="margin-left: 6px;font-size: 14px !important;cursor: pointer;color:#2D8cF0;" class="archive" type="text" size="small"  v-if="func.includes(globalAppCode + ':sfcs-kss-zsdjtz:detail')" @click="editEvent(row)"
             >详情</span>
<!--            <Button type="primary" v-if="func.includes(globalAppCode + ':sfcs-kss-zsdjtz:detail')" style="margin-right: 20px;" @click.native="editEvent(row)" >详情</Button>-->
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>

      <detail v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions} from "vuex";
  import detail from "./detail.vue"
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      detail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '转所登记详情',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      addEvent(row) {
        this.saveType='add'
        this.showData=true
      },
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
