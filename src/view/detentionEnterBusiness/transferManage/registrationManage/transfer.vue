<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData && !showDetail && !jgrybm">
      <statisticalAnalysis mark='acpsfcsksszsdjlbbr' />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbz' />
      <statisticalAnalysis mark='acpsfcsksszsdjlbby' />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbjd' />
      <statisticalAnalysis mark='acpsfcsksszsdjlbbn' />
      <statisticalAnalysis mark='acpsfcsksszsdjlbls' />
    </div>
    <div class="bsp-base-form" :class="!showData && !showDetail && !jgrybm ? 'data-content' : 'data-content2'"
      style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData || showDetail">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData && !showDetail">

        <s-DataGrid ref="grid" funcMark="sfcs-kss-zsdjlb" :customFunc="true" :params="params"
          :beforeRender="beforeRender">
          <template slot="customHeadFunc" slot-scope="{ func }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':sfcs-kss-zsdjlb:zsdj')"
              style="margin:0 0 0 15px" @click.native="editTransferForm()">转所登记</Button>
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode + ':sfcs-kss-zsdjlb:lzsdj') && row.status == '01'"
                  @click.native="editEvent(row)">
                  转所登记
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode + ':sfcs-kss-zsdjlb:xq') && row.status == '03'"
                  @click.native="showDetailEvent(row)">
                  详情
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <transferForm v-if="showData" @close="showData = false" :saveType="saveType" :selectRowData="selectRowData" />
      <detail v-if="showDetail" @close="showDetail = false" :rowData="rowData" />
    </div>
  </div>
</template>

<script>
import { sDataGrid } from 'sd-data-grid'
import transferForm from "./transferForm";
import detail from "../recordManage/detail"
import { statisticalAnalysis } from 'sd-statistical-analysis'
import { mapActions } from "vuex";
export default {
  components: {
    sDataGrid,
    statisticalAnalysis,
    transferForm,
    detail
  },
  props: {
    jgrybm: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      params: {
        jgrybm: this.jgrybm
      },
      showData: false,
      showDetail: false,
      modalTitle: '转所登记',
      saveType: 'add',
      selectRowData: [],
      rowData: {}
    }
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
    addEvent(row) {
      this.saveType = 'add'
      this.showData = true
    },
    oneManOneFile(row) {

    },
    editEvent(row) {
      this.selectRowData = []
      this.selectRowData.push(row)
      this.showDetail = false
      this.showData = true
      this.saveType = 'edit'
    },
    showDetailEvent(row) {
      this.rowData = row;
      this.showData = false
      this.showDetail = true
    },
    editTransferForm() {
      this.selectRowData = []
      this.selectRowData = this.$refs.grid.batch_select
      if (this.selectRowData.length == 0) {
        this.$Message.error('请选择转所人员!!');
        return;
      }
      this.showDetail = false
      this.showData = true
      this.saveType = 'edit'
    },
    beforeRender(data) {     //请求案件的数据
      return new Promise((resolve, reject) => {
        //模拟异步方法，可处理data数据、请求后台数据
        if (data.success && data.rows != undefined && data.rows.length > 0) {
          setTimeout(() => {
            data.rows = data.rows.map(item => {
              if (item.status == '03') {
                item._disabled = true; 
              }
              return item;
            })
            resolve(data)

          }, 500)
        } else {
          resolve(data);
        }
      });
    },
  }
}
</script>

<style scoped>
.detentionEnterBoxData {
  display: flex;
  align-content: center;
}
</style>
