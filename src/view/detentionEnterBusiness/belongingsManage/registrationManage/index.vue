<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpsyrsksssswpdjbr'  />
      <statisticalAnalysis mark='acpsyrsksssswpdjbz'  />
      <statisticalAnalysis mark='acpsyrsksssswpdjby'  />
      <statisticalAnalysis mark='acpsyrsksssswpdjbjd'  />
      <statisticalAnalysis mark='acpsyrsksssswpdjbn'  />
      <statisticalAnalysis mark='acpsyrsksssswpdjls'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="syrsksssswpdj" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':sswpdj')" style="margin-right: 20px;" @click.native="editEvent(row)" >随身物品登记</Button>
            <Button type="primary" v-if="func.includes(globalAppCode + ':wxdj')" style="margin-right: 20px;" @click.native="noNeedRegister(row)" >无需登记</Button>
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <addForm v-if="showData" @close="showData=false" :saveType="saveType" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import addForm from "./addForm.vue"
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      addForm,
      statisticalAnalysis
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '随身物品登记',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      addEvent(row) {
        this.saveType='add'
        this.showData=true
      },
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
      },
      noNeedRegister(row){
        this.$Modal.confirm({
          title: "确定被监管人员没有随身物品",
          loading: true,
          onOk: async () => {
            let params = [{
              wpmc:'',
              sl:'',
              zl:'',
              hbzl:'',
              wptz:'',
              wz:'',
              bz:'',
              wpzp:'',
              status: '04',
              rybh : row.rybh,
              ryxm: row.xm,
              businessType: "kss"
            }]
            this.$store.dispatch('authPostRequest',{
              url: this.$path.app_personalEffectsSubCreateBatch,
              params:params
            }).then(resp=>{
              this.$Modal.remove();
              if (resp.code==0){
                this.$Message.success('提交成功!');
                this.$refs.grid.query_grid_data(1);
              }else{

                this.$Notice.error({
                  title:'错误提示',
                  desc:resp.msg
                })
              }
            })


          },
        });
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
