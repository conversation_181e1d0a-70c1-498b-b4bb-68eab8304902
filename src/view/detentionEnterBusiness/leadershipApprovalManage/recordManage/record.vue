<template>
  <div>
    <div class="bsp-base-form" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>
      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="ksssyrssldsptz" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':ksssyrssldsptz:xq')" style="margin-right: 20px;" @click.native="showInfo(row)" >详情</Button>
          </template>
          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <recordDetail v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import recordDetail from "./recordDetail";
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      recordDetail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '领导审批详情',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      showInfo(row) {
        this.rowData = row;
        this.showData=true
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }
  .data-content{
    /*top: 28px;*/
  }
</style>
