<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData && !showDetail && !jgrybm">
      <statisticalAnalysis mark='acpsyrskssshdjbr'  />
      <statisticalAnalysis mark='acpsyrskssshdjbz'  />
      <statisticalAnalysis mark='acpsyrskssshdjby'  />
      <statisticalAnalysis mark='acpsyrskssshdjbjd'  />
      <statisticalAnalysis mark='acpsyrskssshdjbn'  />
      <statisticalAnalysis mark='acpsyrskssshdjls'  />
    </div>
    <div class="bsp-base-form" :class="!showData && !showDetail && !jgrybm ? 'data-content' : 'data-content2'" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData || showDetail">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData && !showDetail">

        <s-DataGrid ref="grid" funcMark="syrs-kss-shdj" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode +':syrs-kss-shdj:add') && row.status == '01'" @click.native="editEvent(row)">
                  收回登记
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode +':syrs-kss-shdj:xq') && row.status == '03'" @click.native="showDetailEvent(row)">
                  详情
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>


<!--            <span style="margin-left: 6px;font-size: 14px !important;cursor: pointer;color:#2D8cF0;" class="archive" type="text" size="small"  v-if="func.includes(globalAppCode+':syrs-kss-shdj:add')" @click="editEvent(row)"-->
<!--            >收回登记</span>-->
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <addForm v-if="showData" @close="showData=false" :saveType="saveType" :rowData="rowData"/>
      <detail v-if="showDetail" @close="showDetail=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import detail from "../recordManage/detail"
  import  {sDataGrid}  from  'sd-data-grid'
  import addForm from "./addForm.vue"
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      addForm,
      statisticalAnalysis,
      detail
    },
    props:{
      jgrybm: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        params:{
          jgrybm:this.jgrybm
        },
        showData: false,
        showDetail:false,
        modalTitle: '收回登记',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      addEvent(row) {
        this.saveType='add'
        this.showData=true
        this.showDetail = false
      },
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.showDetail = false
        this.saveType='edit'
      },
      showDetailEvent(row){
        this.rowData = row;
        this.showData=false
        this.showDetail = true
      },
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
