<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpkssjyywrsjkdjbrsys'  />
      <statisticalAnalysis mark='acpkssjyywrsjkdjbzsys'  />
      <statisticalAnalysis mark='acpkssjyywrsjkjcbysys'  />
      <statisticalAnalysis mark='acpkssjyywrsjkjcbjdsys'  />
      <statisticalAnalysis mark='acpkssjyywrsjkjcbnsys'  />
      <statisticalAnalysis mark='acpkssjyywrsjkjclssys'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="gjywksscsdj" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':gjywksscsdj:csdj')" style="margin-right: 20px;" @click.native="editEvent(row)" >财务交接</Button>
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <addForm v-if="showData" @close="showData=false" :saveType="saveType" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import addForm from "./addForm.vue"
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      addForm,
      statisticalAnalysis
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '财务交接',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      addEvent(row) {
        this.saveType='add'
        this.showData=true
      },
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
