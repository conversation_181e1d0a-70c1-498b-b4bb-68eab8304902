<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpgjywksscsdjbr'  />
      <statisticalAnalysis mark='acpgjywksscsdjbz'  />
      <statisticalAnalysis mark='acpgjywksscsdjby'  />
      <statisticalAnalysis mark='acpgjywksscsdjbjd'  />
      <statisticalAnalysis mark='acpgjywksscsdjbn'  />
      <statisticalAnalysis mark='acpgjywksscsdjls'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="sfcsksswsdylb" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">
<!--            <Button type="primary" v-if="func.includes('sfcs:dymd')" @click.native="print()">打印名单</Button>-->
          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode + ':sfcs:xq')" @click.native="editEvent(row)">
                  详情
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode + ':sfcs:wsdy')" @click.native="archive(row)">
                  文书打印
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>

<!--            <Button type="primary" v-if="func.includes('sfcs:xq')" style="margin-right: 20px;" @click.native="editEvent(row)" >详情</Button>-->
<!--            <Button type="primary" v-if="func.includes('sfcs:wsdy')" style="margin-right: 20px;" @click.native="archive(row)" >文书打印</Button>-->
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>

      <detail v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions, mapMutations} from "vuex";
  import detail from "./detail.vue"
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      detail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '释放出所登记详情',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      ...mapMutations([ 'setTagNavList',]),
      addEvent(row) {
        this.saveType='add'
        this.showData=true
      },
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
      },
      archive(row) {
        this.$router.push({
          path:'/archive',
          query: {
            id:row.id,
            dicCode:"ZD_SFCSWSDY",
            formId:"1934132292406611968"
          },
        })
        let res={
          path:'/archive',
          name: "archive",
          meta: {hideInMenu: true, title: "释放出所-文书打印", notCache: true, icon: ""}
        }
        let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
        arr.unshift(res)
        this.setTagNavList(arr)
      },
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
