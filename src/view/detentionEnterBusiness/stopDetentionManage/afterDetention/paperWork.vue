<template>


    <div>
        <Row style="margin-top: 10px;">
            <Col span="6">
                
            </Col>
            <Col span="18">
            <div>
                <SinglePDFViewer ref="pdfViewer" title="在押人员权利和义务告知书" :key="businessId" formId="1940312854863417344"
                    :businessId="businessId">

                </SinglePDFViewer>
            </div>
            </Col>
        </Row>

        <div class="bsp-base-fotter">
            <Button @click="onCancel">取消</Button>
            <Button @click="handlePrint" type="primary">打印</Button>
        </div>
    </div>
</template>

<script>
import { SinglePDFViewer } from "@/components/index.js";
export default {
    name: 'paperWork',
    components: {
        SinglePDFViewer
    },
    props: {

    },
    methods: {
        onCancel() {
            this.$emit('on_show_table')
        },
        handlePrint() {
            this.$refs.pdfViewer.print();
        }
    }
}



</script>