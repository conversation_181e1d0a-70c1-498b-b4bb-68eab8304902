<template>
    <div>
        <s-DataGrid ref="grid" funcMark="jyrs-syyw-jytjdjb-sjh" :customFunc="true" v-if="!showAdd">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" @click.native="handleAdd('doctorAdd')" size="small"
                    v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:dj')">建议停拘登记</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Dropdown trigger="click">
                    <a href="javascript:void(0)">
                        操作
                        <Icon type="ios-arrow-down"></Icon>
                    </a>
                    <DropdownMenu slot="list">
                        <DropdownItem
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:ys') && row.status == '0200'"
                            @click.native="handleAdd('doctorEdit', row)">
                            医生登记
                        </DropdownItem>
                        <DropdownItem @click.native="approval('police', row)"
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:mj') && row.status == '0201'">
                            民警审批
                        </DropdownItem>
                        <DropdownItem @click.native="approval('leader', row)"
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:ld') && row.status == '0203'">
                            领导审批
                        </DropdownItem>
                        <DropdownItem @click.native="approval('director', row)"
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:sz') && row.status == '0205'">
                            所长审批
                        </DropdownItem>
                        <DropdownItem v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:gl')"
                            @click.native="archive(row)">
                            文书管理
                        </DropdownItem>
                        <DropdownItem @click.native="handleDetails('details', row)"
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjbsjh:xq')">详情</DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </template>
        </s-DataGrid>
        <div v-if="showAdd" style="height: 100%" class="bsp-base-form">
            <component :is="component" @on_show_table="on_show_table" :dataMsg="dataMsg" :type="type"
                :isDetalis="isDetalis" />
        </div>


    </div>
</template>

<script>
import userApproval from './userApproval.vue'
import addStopAfter from './addStopAfter.vue'
import paperWork from './paperWork.vue'
import { mapMutations } from "vuex"
export default {
    name: 'afterdetention',
    components: {
        addStopAfter,
        userApproval,
        paperWork
    },
    data() {
        return {
            showAdd: false,
            component: null,
            dataMsg: {},
            type: null,
            isDetalis: false // 是否详情

        }
    },
    methods: {
        ...mapMutations(['setTagNavList',]),
        on_show_table() {
            this.showAdd = false
            this.component = null
            this.isDetalis = false
        },
        handleAdd(type, row) {
            this.component = 'addStopAfter'
            this.showAdd = true
            this.type = type
            console.log(row);
            if (type == 'doctorEdit') {
                this.dataMsg = row
            } else {
                this.dataMsg = {}
            }

        },
        handleDetails(isDetalis, row) {
            this.component = 'userApproval'
            this.showAdd = true
            this.dataMsg = row
            this.isDetalis = true
        },
        approval(type, row) {
            this.component = 'userApproval'
            this.showAdd = true
            console.log(row)
            this.dataMsg = row
            this.type = type || ''
        },
        handleDocument(row) {
            this.component = 'paperWork'
            this.showAdd = true
        },
        archive(row) {
            this.$router.push({
                path: '/archive',
                query: { id: row.id, dicCode: "ZD_JTWSDYZD", formId: "1942484769552076800"},
            })
            let res = {
                path: '/archive',
                name: "archive",
                meta: { hideInMenu: true, title: "文书管理", notCache: true, icon: "" }
            }
            let arr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
            arr.unshift(res)
            this.setTagNavList(arr)
        },
    },
    mounted() {

    }
}

</script>