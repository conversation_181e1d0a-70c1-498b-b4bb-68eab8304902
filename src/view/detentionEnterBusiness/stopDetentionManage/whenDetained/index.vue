<template>
    <div>
        <!-- <Button type="primary" @click.native="handleAdd()" size="small" v-if="!showAdd">接班登记</Button> -->

        <s-DataGrid ref="grid" funcMark="jyrs-syyw-jytjdjb" :customFunc="true" v-if="!showAdd">
            <template slot="customHeadFunc" slot-scope="{ func }">
                <Button type="primary" @click.native="handleAdd('policeAdd')" size="small"
                    v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:dj')">建议停拘登记</Button>
            </template>
            <template slot="customRowFunc" slot-scope="{ func,row,index }">
                <Dropdown trigger="click">
                    <a href="javascript:void(0)">
                        操作
                        <Icon type="ios-arrow-down"></Icon>
                    </a> 
                    <DropdownMenu slot="list">
                        <DropdownItem @click.native="handleAdd('policeEdit',row)"
                            v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:bj') && row.status == '0100'">
                            编辑
                        </DropdownItem>
                        <DropdownItem v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:ysdj') && (row.status == '0101' || row.status == '0103')"
                            @click.native="doctorEvent(row)">
                            医生登记
                        </DropdownItem>
                        <!-- <DropdownItem @click.native="approval(row)" v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:mjsp') && row.status == '0'">
                            民警审批
                        </DropdownItem> -->
                        <DropdownItem @click.native="approval('leader',row)" v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:ldsp') && row.status == '0102'">
                            领导审批
                        </DropdownItem>
                        <DropdownItem @click.native="approval('director',row)" v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:szsp') && row.status == '0105'">
                            所长审批
                        </DropdownItem>
                        <DropdownItem  v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:wsgl')" @click.native="archive(row)">
                            文书管理
                        </DropdownItem>
                        <DropdownItem @click.native="handleDetails('details',row)" v-if="func.includes(globalAppCode + ':jyrssyywjytjdjb:xq')">详情</DropdownItem>
                    </DropdownMenu>
                </Dropdown>
            </template>
        </s-DataGrid>
        <div v-if="showAdd" style="height: 100%" class="bsp-base-form">
            <component :is="component" @on_show_table="on_show_table" :dataMsg="dataMsg" :type="type" :isDetalis="isDetalis" />
        </div>
    </div>
</template>



<script>
import { sDataGrid } from 'sd-data-grid'
import addStop from './addStop.vue'
import doctorAdd from './doctorAdd.vue'
import userApproval from './userApproval.vue'
export default {
    name: 'whedetaned',
    components: {
        addStop,
        sDataGrid,
        doctorAdd,
        userApproval
    },
    data() {
        return {
            component: null,
            showAdd: false,
            dataMsg: {},
            type: null,
            isDetalis:false // 是否详情
        }
    },
    methods: {
        handleAdd(type,row) {
            this.component = 'addStop'
            this.showAdd = true
            this.type = type
            console.log(row);
            if (type == 'policeEdit') {
                this.dataMsg = row
            }else {
                this.dataMsg = {}
            }

        },
        doctorEvent(row) {
            this.component = 'doctorAdd'
            this.showAdd = true
            console.log(row);
            this.dataMsg = row
            if (row.status == '0103') {
                this.type = 'doctorEdit'
            }else {
                this.type = 'doctorAdd'
            }

        },
        on_show_table() {
            this.showAdd = false
            this.component = null
            this.type = ''
            // this.on_refresh_table()
        },
        on_refresh_table() {
            this.$refs.grid.query_grid_data(1)
        },
        approval(type,row) {
            this.component = 'userApproval'
            this.showAdd = true
            console.log(row)
            this.dataMsg = row
            this.type = type || ''
        },
        handleDetails(isDetalis,row) {
            this.component = 'userApproval'
            this.showAdd = true
            this.dataMsg = row
            this.isDetalis = true
        },
        archive(row) {
            this.$router.push({
                path: '/archive',
                query: { id: row.id, dicCode: "ZD_JTWSDYZD", formId: "1942484769552076800" },
            })
            let res = {
                path: '/archive',
                name: "archive",
                meta: { hideInMenu: true, title: "文书管理", notCache: true, icon: "" }
            }
            let arr = localStorage.getItem('tagNaveList') ? JSON.parse(localStorage.getItem('tagNaveList')) : []
            arr.unshift(res)
            this.setTagNavList(arr)
        },
        

    },
    mounted() {

    }
}

</script>
<style scoped></style>