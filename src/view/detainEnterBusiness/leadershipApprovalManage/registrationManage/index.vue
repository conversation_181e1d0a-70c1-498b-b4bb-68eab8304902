<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpbr'  />
      <statisticalAnalysis mark='acpsysldspbz'  />
      <statisticalAnalysis mark='acpsysldspby'  />
      <statisticalAnalysis mark='acpsysldspbjd'  />
      <statisticalAnalysis mark='acpsysldspbn'  />
      <statisticalAnalysis mark='acpsysldspls'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="ksssyrssldsplb" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':ksssyrssldsplb:ldsp')" style="margin-right: 20px;" @click.native="editEvent(row)" >领导审批</Button>
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <approvalForm v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import approvalForm from "./approvalForm.vue"
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      approvalForm,
      statisticalAnalysis
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '领导审批',
        saveType: 'add',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      editEvent(row){
        this.rowData = row;
        this.showData=true
        this.saveType='edit'
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
