<template>
    <div>
        <div class="search">
            <Form :model="formItem" :label-width="120" :lable-colon="true" ref="formItem">
                <Row>
                    <Col span="8">
                    <FormItem label="姓名" prop="jgryxm">
                        <Input v-model="formItem.jgryxm" readonly>
                        <template slot="append">
                            <Button @click="openPrison = true">选择</Button>
                        </template>
                        </Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="日期" prop="rq">
                        <DatePicker v-model="formItem.rq" type="datetimerange" style="width: 100%"
                            @on-change="changeData">
                        </DatePicker>
                    </FormItem>
                    </Col>
                    <!-- <Col span="8">
                    <FormItem label="同监室人员" prop="tjsry">
                        <Input v-model="formItem.tjsry"></Input>
                    </FormItem>
                    </Col> -->
                </Row>
                <!-- <Row>
                    <Col span="8">
                    <FormItem label="人员编号" prop="rybh">
                        <Input v-model="formItem.rybh"></Input>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="进入监室时间" prop="jrjssj">
                        <DatePicker v-model="formItem.jrjssj" type="date" style="width: 100%" format="yyyy-MM-dd">
                        </DatePicker>
                    </FormItem>
                    </Col>
                    <Col span="8">
                    <FormItem label="离开监室时间" prop="lkjssj">
                        <DatePicker v-model="formItem.lkjssj" type="date" style="width: 100%" format="yyyy-MM-dd">
                        </DatePicker>
                    </FormItem>
                    </Col>
                </Row> -->
                <!-- <Row type="flex" justify="center">
                    <Button @click="handleReset()">重置</Button>
                    <Button type="primary" style="margin-left: 15px;" @click="search()">查询</Button>
                </Row> -->
            </Form>
        </div>

        <div class="btn-box">
            <div class="name">{{ formItem.jgryxm }}-{{ formItem.roomName }}</div>
            <!-- <Button type="primary" style="padding: 0 30px;">导出</Button> -->
        </div>
        <Table border :columns="columns1" :data="tableData" :span-method="handleSpan">
            <!-- <template slot-scope="{ row }" slot="cz">
                <Button type="primary">查看</Button>
            </template> -->
        </Table>
        <!-- <div class="page_box">
            <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size="formItem.pageSize"
                @on-prev="getNo" @on-next="getNo" :current="formItem.pageNo" @on-change="getNo"
                @on-page-size-change="getSize" />
        </div> -->

        <Modal v-model="openPrison" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
            title="人员列表">
            <div class="select-use">
                <prisonSelect ref="prisonSelect" ryzt="ALL" :isMultiple='false' :selectUseIds="formItem.jgrybm" />
            </div>
            <div slot="footer">
                <Button type="primary" @click="useSelect" class="save">确 定</Button>
                <Button @click="openPrison = false" class="save">关 闭</Button>
            </div>
        </Modal>
    </div>
</template>


<script>
import { prisonSelect } from "sd-prison-select"
export default {
    name: 'dynamicData',
    components: {
        prisonSelect
    },
    data() {
        return {
            formItem: {
                pageSize: 10,
                pageNo: 1

            },
            total: 0,
            columns1: [
                {
                    title: '序号',
                    type: 'index',
                    width: 70,
                    align: 'center'

                },
                {
                    title: '共同在监室的结束时间',
                    key: 'cohabitationRange',
                    align: 'center',
                    width: 400
                },
                // {
                //     title: '结束时间',
                //     key: 'cohabitationEnd',
                //     align: 'center'
                // },

                {
                    title: '监室号',
                    key: 'roomName',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '同监室人员',
                    key: 'jgryxm',
                    align: 'center'
                },
                {
                    title: '人员编号',
                    key: 'jgrybm',
                    align: 'center'
                },
                {
                    title: '进入监室时间',
                    key: 'inRoomTime',
                    align: 'center'
                },
                {
                    title: '离开监室时间',
                    key: 'outRoomTime',
                    align: 'center'
                },
                // {
                //     title: '操作',
                //     slot: 'cz',
                //     align: 'center'
                // }

            ],
            tableData: [
            ],
            openPrison: false,
            dataRange: []
        }
    },
    mounted() {
        // this.getData()
    },
    methods: {
        handleSpan({ row, column, rowIndex, columnIndex }) {

            if (column.key === 'cohabitationRange') {
                const sameRows = this.tableData.filter(
                    item => item.cohabitationStart === row.cohabitationStart
                        && item.cohabitationEnd === row.cohabitationEnd
                );
                const isFirstRow = rowIndex === this.tableData.findIndex(
                    item => item.cohabitationStart === row.cohabitationStart
                        && item.cohabitationEnd === row.cohabitationEnd
                );
                return isFirstRow ? { rowspan: sameRows.length, colspan: 1 } : { rowspan: 0, colspan: 0 };
            }

        },
        handleReset() {
            console.log(this.$refs.formItem);
            this.$refs.formItem.resetFields()
            console.log(this.formItem, 'aaaaaa');
        },
        search() {
            console.log(this.formItem, 'ssssssssss');
        },
        getSize(pageSize) {
            //console.log(this.page,'search')
            this.$set(this.formItem, 'pageSize', pageSize)
            console.log(this.formItem);
            // this.getData()
        },
        getNo(pageNo) {
            //console.log(this.page,pageNo,'search')
            this.$set(this.formItem, 'pageNo', pageNo)
            console.log(this.formItem);

            // this.getData()
        },
        useSelect() {
            if (this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
                let people = this.$refs.prisonSelect.checkedUse
                console.log(people)
                this.$set(this.formItem, "jgryxm", people.map(item => item.xm).join(','))
                this.$set(this.formItem, "jgrybm", people.map(item => item.jgrybm).join(','))
                this.$set(this.formItem, "roomId", people.map(item => item.jsh).join(','))
                this.$set(this.formItem, "roomName", people.map(item => item.roomName).join(','))

                this.getData()
                this.openPrison = false
            } else {
                this.$Notice.warning({
                    title: '提示',
                    desc: '请选择人员!'
                })
            }
        },
        getData() {
            let params = {
                jgrybm: this.formItem.jgrybm,
                roomId: this.formItem.roomId,
                startTime: this.dataRange.length ? this.dataRange[0] : this.getLastDaysRange().startTime,
                endTime: this.dataRange.length ? this.dataRange[1] : this.getLastDaysRange().endTime,

            }
            this.$store.dispatch('authPostRequest', { url: this.$path.ds_roommates, params: params }).then(res => {
                console.log(res);
                if (res.success) {
                    this.tableData = res.data
                    if (res.data.length > 0) {
                        this.tableData = res.data.map(item => ({
                            ...item,
                            cohabitationRange: `${item.cohabitationStart} ~ ${item.cohabitationEnd}`
                        }));
                    }

                } else {
                    this.$Message.error(res.message)
                    this.tableData = []
                }

            })
        },
        changeData(data) {
            if (!this.formItem.jgrybm) {
                this.$Message.error('请先选择机构人员')
                return
            }
            this.dataRange = data
            this.getData()
        },
        getLastDaysRange() {
            const endTime = new Date();
            const startTime = new Date();
            startTime.setDate(endTime.getDate() - 6); // 7天

            return {
                startTime: this.dayjs(startTime).format('YYYY-MM-DD 00:00:00'),
                endTime: this.dayjs(endTime).format('YYYY-MM-DD 23:59:59'),
            };
        }
    }

}

</script>

<style scoped lang="less">
.search {
    background: #f7f9fc;
    padding: 20px 20px 10px;
    margin-bottom: 10px;
    // overflow: hidden;
    border: 1px solid #cee0f0;
}

.btn-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    align-items: center;

    .name {
        font-weight: bold;
        font-size: 18px;
    }
}

.page_box {
    display: flex;
    justify-content: end;
}

/deep/.ivu-input-group-append {
    color: #fff;
    background-color: #2b5fd9;
    border: 1px solid #2b5fd9;
}
</style>