<!-- 新增类型 -->
<template>
    <div>
        <Form ref="formType" :model="formValidate" :rules="ruleValidate" :label-width="120">
            <FormItem label="指标分类名称" prop="typeName">
                <Input v-model="formValidate.typeName" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="初始分值" prop="initScore">
                <Input v-model.number="formValidate.initScore" type="number" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="考评人" prop="assessorSfzh">
                <user-selector v-model="formValidate.assessorSfzh" tit="考评人选择" @onCancel="onCancel" @onClear="onClear"
                    @onSelect="onSelect" :text.sync="formValidate.assessorName" returnField="idCard" numExp='num>=1'
                    msg="至少选中1人">
                </user-selector>
            </FormItem>
            <FormItem label="考评对象" prop="assessedObjectId">
                <s-dicgrid v-model="formValidate.assessedObjectType" dicName="ZD_JXKH_BKHDXLX" />
                <s-dicgrid key="post" v-if="formValidate.assessedObjectType == '01'" placeholder="请选择"
                    v-model="formValidate.assessedObjectId" ref="dicGrid" @values="getvalue" dicName="ZD_POST" />

                <s-dicgrid v-model="formData.assessedObjectId" v-if="formValidate.assessedObjectType == '02' && showRole"
                    requestType="loopData" key="roleList" :loopData="roleList" showField="name" keyField="code"
                    @values="getvalue" style="width: 100%;" />
                <user-selector v-if="formValidate.assessedObjectType == '03'" v-model="formValidate.assessedObjectId"
                    tit="考评对象选择" @onCancel="onCancelDX" @onClear="onClearDX" @onSelect="onSelectDX"
                    :text.sync="formValidate.assessedObjectName" returnField="idCard" numExp='num>=1' msg="至少选中1人">
                </user-selector>
            </FormItem>
        </Form>
    </div>
</template>
<script>
import { userSelector } from 'sd-user-selector'
export default {
    components: { userSelector, },
    props: {
        formData: Object
    },
    data() {
        return {
            appId: serverConfig.APP_ID,
            roleList: [],
            showRole:false,
            formValidate: this.formData ? this.formData : {
                sortOrder: 0,
            },
            ruleValidate: {
                typeName: [
                    { required: true, message: '指标分类名称不能为空', trigger: 'blur,change' }
                ],
                initScore: [
                    { required: true, message: '初始分值不能为空', type: 'number', trigger: 'blur,change' }
                ],
            }
        }
    },
    mounted() {
        this.getQueryPageData()
    },
    methods: {
        onSelect(data) {
            console.log(data, 'onSelectonSelect')
            let dataArr = []
            data.forEach(ele => {
                let obj = {
                    assessorName: ele.name,
                    assessorSfzh: ele.idCard
                }
                dataArr.push(obj)

            });
            this.$set(this.formValidate, 'assessorList', dataArr)
        },
        onCancel() { },
        onClear() {
            this.$set(this.formValidate, 'assessorList', [])
        },
        getQueryPageData() {
            this.$store.dispatch('postRequest', {
                url: '/bsp-com/com/datagrid/getQueryPageData',
                params: {
                    // access_token: getToken(),
                    modelId: 'bsp:role:list',
                    pageNo: '1',
                    pageSize: '100',
                    browsePerm: true,
                    condis: `[{"name":"APP_ID","op":"=","value":"${this.appId}","valueType":"string"},{"name":"IS_PUBLIC","op":"=","value":"0","valueType":"string"},{"name":"ISDEL","op":"=","value":"0","valueType":"string"}]`
                }
            }).then(res => {
                console.log(res, '123');
                if (res.success) {
                    this.showRole=true
                    this.roleList = res.rows ? res.rows : []
                }else{
                     this.showRole=true
                }
            })
        },
        onSelectDX(data) {
            let dataArr = []
            data.forEach(ele => {
                let obj = {
                    assessedObjectName: ele.name,
                    assessedObjectId: ele.idCard,
                    assessedObjectType: this.formValidate.assessedObjectType,
                }
                dataArr.push(obj)

            });
            this.$set(this.formValidate, 'assessedList', dataArr)

        },
        onCancelDX() { },
        onClearDX() {
            this.$set(this.formValidate, 'assessedList', [])
        },
        getvalue(data, value) {
            let dataArr = []
            data.forEach(ele => {
                let obj = {
                    assessedObjectName: ele.name,
                    assessedObjectId: ele.code || ele.id,
                    assessedObjectType: this.formValidate.assessedObjectType,
                }
                dataArr.push(obj)

            });
            this.$set(this.formValidate, 'assessedList', dataArr)
        }
    }
}
</script>