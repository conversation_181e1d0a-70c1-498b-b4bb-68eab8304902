<template>
    <div class="ckywshgxgllb-wrap">
        <div class="ckywshgxgllb-wrap-left">
            <!-- 使用新的人员选择组件 -->
            <personnel-selector v-model="formData.jgrybm" title="被监管人员" placeholder="点击选择在押人员或扫码识别"
                :show-case-info="true" :enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
        </div>
        <div class="ckywshgxgllb-wrap-right">
            <div class="Inquiry-wrap-right" style="width: calc(100vw - 750px);">
                <div class='bary-flex' style='margin-top:10px;'>
                    <p class="detail-title ml-16-title">家属信息</p>
                    <p><Button type="primary" @click='addInvestigatorsCoop' style="cursor: pointer;"
                            v-if="!formData.type">添加会见家属</Button>&nbsp;&nbsp;&nbsp;&nbsp;</p>
                </div>
                <div class='bary-flex' style="margin-left: 16px;">
                    <Form :ref="'coopformBox-' + index" class='form-box' :key="index + 'b'"
                        v-for='(item, index) in formData.familyList' :model="item" :label-width="120"
                        :label-colon="true" style="margin:0 16px 0px 0;">
                        <Row style='padding:10px 0;background:#f5f7fa;margin-bottom: 16px;'>
                            <Col span="22" style="margin-bottom:0px;text-align:center;">会见家属（{{ Number(index + 1) }}）
                            </Col>
                            <Col span="2">
                            <Icon size="20" v-if="index > 0" @click="deleteCasePersonCoop(item, index)"
                                type="ios-close-circle" />&nbsp;&nbsp;&nbsp;</Col>
                        </Row>
                        <Row>
                            <Col span="23" style="margin-bottom:0px">
                            <FormItem label="照片采集" prop="imageUrl">
                                <RadioGroup v-model="item.isdisabled" size="large">
                                    <Radio label="5">本地上传</Radio>
                                    <Radio label="6">拍照上传</Radio>
                                </RadioGroup>
                                <br />
                                <div v-if="item.isdisabled && item.isdisabled == '6'" style="display: flex;">
                                    <div v-if="!item.imageUrl" @click="takePhoto(item, index)"
                                        style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                                        <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
                                    </div>
                                    <div v-if="item.imageUrl"
                                        style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                                        <img :src="item.imageUrl" style="width: 96px; height: 96px;" />
                                    </div>
                                </div>
                                <Tooltip v-else max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。"
                                    placement="top">
                                    <s-ImageUploadLocal v-model="item.imageUrl" :maxSize="5120" ref="imgUpload"
                                        :multiple="false"
                                        @getfile="(data) => getfile(data, index, 'familyList', 'imageUrl')"
                                        :defaultList="item.defaultList" :maxFiles="1" />

                                </Tooltip>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>

                            <Col span="11">
                            <FormItem label="姓名" prop="name" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <Input v-model="item.name" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="性别" prop="gender"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]"
                                style="width: 100%;">
                                <s-dicgrid v-model="item.gender" dicName="ZD_XB" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="证件类型" prop="idType" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <s-dicgrid v-model="item.idType" dicName="ZD_GABBZ_ZJZL" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="证件号码" prop="idNumber" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <Input v-model="item.idNumber" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="社会关系" prop="relationship" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <s-dicgrid v-model="item.relationship" dicName="ZD_GABBZ_SHGX" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="联系方式" prop="contact" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <Input v-model="item.contact" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>

                            <Col span="11">
                            <FormItem label="工作单位" prop="workUnit" style="width: 100%;">
                                <Input v-model="item.workUnit" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="居住地址" prop="address" style="width: 100%;">
                                <Input v-model="item.address" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="22">
                            <FormItem label="亲属关系证明" prop="zyzsUrl" style="width: 100%;">
                                <file-upload :defaultList="item.zyzsUrl ? JSON.parse(item.zyzsUrl) : []"
                                    :serviceMark="serviceMark" :bucketName="bucketName" />
                            </FormItem>
                            </Col>
                        </Row>

                    </Form>
                </div>
                <br />

            </div>
            <div class='bsp-base-fotter'>
                <Button @click='goBack'>返 回</Button>
                <Button @click="saveData" type="primary" :loading='loading'>保 存</Button>
            </div>

            <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
        </div>
    </div>
</template>
<script>
import personnelSelector from "@/components/personnel-selector/index.vue"
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import cameraModal from '@/components/camera/camera-modal.vue'
export default {
    components: { personnelSelector, fileUpload, sImageUploadLocal, cameraModal },
    props: {
        modalTitle: String,
        rowData: Object
    },
    data() {
        return {
            loading: false,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            formData: {
                status: false,
                familyList: [{ isdisabled: '5' }]
            },
            curBaryData: {}
        }
    },
    watch: {
        'rowData': {
            handler(n, o) {
                console.log(n, o, 'n,on,on,o')
                this.$set(this.formData, 'jgrybm', n.jgrybm)
                this.$set(this.formData, 'jgrybm', n.jgrybm)

                if (n.type && n.type == 'editType') {
                    this.$set(this.formData, 'type', n.type)
                    this.$set(this.formData, 'familyList', [n])
                    this.getInfo()
                }

            }, deep: true, immediate: true
        }
    },
    methods: {
        /**
 * 人员选择变化处理
 */
        handlePersonnelChange(personnelData, jgrybm) {
            console.log('选择的人员:', personnelData, jgrybm)
            this.limitNumber(personnelData, jgrybm)

        },
        limitNumber(personnelData, jgrybm) {
            this.$store.dispatch('getRequest', { url: this.$path.acp_familyMeetingVideo_limitNumber, params: { jgrybm: jgrybm } }).then(resp => {
                if (resp.success) {
                    this.$Message.error(resp.data.msg)
                    // if (resp.data.allow) {
                        if (personnelData && jgrybm) {
                            this.formData.prison = personnelData
                            this.formData.jgrybm = jgrybm
                            this.formData.jgryxm = personnelData.xm
                            this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
                        } else {
                            // 清空人员信息
                            this.formData.prison = null
                            this.formData.jgrybm = ''
                            this.formData.jgryxm = ''
                        }
                    // } else {
                    //         // 清空人员信息
                    //         this.formData.prison = null
                    //         this.formData.jgrybm = ''
                    //         this.formData.jgryxm = ''
                    // }

                } else {
                    // this.loading = false
                    // this.$Message.error(resp.msg || '保存失败')
                }
            }).catch(err => {
                // this.loading = false
            })
        },
        isValidPhoneNumber(phoneNumber) {
            // 定义中国大陆手机号码的正则表达式
            // 这个正则表达式涵盖了以13、14、15、16、17、18、19开头的号码
            const regex = /^(13[0-9]|14[01456789]|15[0-35-9]|16[2567]|17[235678]|18[0-9]|19[0-35-9])\d{8}$/;
            // 使用test方法测试手机号码是否符合正则表达式
            return regex.test(phoneNumber);
        },
        goBack() {
            this.$emit('on_show_table')
        },
        takePhoto(row, index) {
            this.curBaryData = row
            this.curBaryData.index = index
            this.$refs.cameraModal.open({});
        },
        takePhotoItem(imgUrl) {
            this.$set(this.curBaryData, 'imageUrl', imgUrl)
            this.$set(this.formData.familyList[this.curBaryData.index], 'imageUrl', imgUrl)
        },
        deleteCasePersonCoop(item, index) {
            this.formData.familyList.splice(index, 1)
        },
        getfile(file, index, fileName, fileUrl) {
            this.$set(this.formData[fileName][index], fileUrl, file.url)
            this.formData[fileName][index].defaultList = [file]
        },
        addInvestigatorsCoop() {
            let obj = { isdisabled: '5' }
            this.formData.familyList.push(obj)
        },
        saveData() {
            let arr = []
            if (this.formData.familyList && this.formData.familyList.length > 0) {
                this.formData.familyList.forEach((item, index) => {
                    arr.push(this.$refs['coopformBox-' + index][0].validate())
                })
            }
            console.log(arr, 'arr')
            // console.log(this.$refs.baseForm.formData,this.$refs.jlpz.formData,this.$refs.xnpz.formData,'.formData')
            // let arr= [this.$refs.taskForm.validate(),this.$refs.jlpz.$refs['formData'].validate(),this.$refs.xnpz.$refs['formDataXn'].validate()]
            if (!this.formData.jgrybm) {
                this.$Message.error('请选择被监管人员！')
                return
            }
            Promise.all(arr).then(data => {
                console.log(data, '121   2')
                console.log(this.formData, data, 'params')
                if (data.every(item => item)) {
                    if (this.formData.type) {
                        this.updateForm()
                    } else {
                        this.saveDataForm()
                    }

                } else {
                    this.$Message.error('请填写完整')
                    this.loading = false
                }

            })
        },
        saveDataForm() {

            this.$store.dispatch('authPostRequest', { url: this.$path.acp_familyMeetingVideo_create, params: this.formData }).then(resp => {
                if (resp.success) {
                    this.loading = false
                    this.goBack()
                    this.$Message.success('保存成功')
                } else {
                    this.loading = false
                    this.$Message.error(resp.msg || '保存失败')
                }
            }).catch(err => {
                this.loading = false
            })
        },
        updateForm() {
            this.$store.dispatch('authPostRequest', { url: this.$path.acp_socialRelations_update, params: this.formData.familyList[0] }).then(resp => {
                if (resp.success) {
                    this.loading = false
                    this.goBack()
                    this.$Message.success('社会关系保存成功')
                } else {
                    this.loading = false
                    this.$Message.error(resp.msg || '社会关系保存失败')
                }
            }).catch(err => {
                this.loading = false
            })
        },
        getInfo() {
            this.$store.dispatch('getRequest', { url: this.$path.acp_socialRelations_get, params: { id: this.formData.familyList[0].id } }).then(resp => {
                if (resp.success) {
                    resp.data.defaultList = [{ fileUrl: resp.data.imageUrl }]
                    this.formData.familyList = [resp.data]
                    if (resp.data.imageUrl) {

                    }

                } else {
                }
            }).catch(err => {
            })
        }

    }
}
</script>
<style scoped>
.ckywshgxgllb-wrap {
    width: 100%;
    display: flex;
}

.bary-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}
</style>