<!--表单  -->
<template>
  <div class="fm-content-wrap" style="width: 100%;padding: 0; ">
    <Form ref="formData" :model="formData" :label-colon="true" label-position="right" :hide-required-mark="false" inline
      :label-width="130">
      <div class="fm-content-form">
        <p class="fm-content-wrap-title">
          <Icon type="md-list-box" size="24" color="#2b5fda" />领导审批
        </p>
        <Row>
          <Col :span="24">
          <FormItem prop="approvalResult" style="width: 100%" label="审批结果"
            :rules="[{ trigger: 'blur', message: '必填', required: true }]">
            <RadioGroup v-model="formData.approvalResult" @on-change="getApprovalResult">
              <Radio label="1">同意</Radio>
              <Radio label="0">不同意</Radio>
            </RadioGroup>
          </FormItem>
          </Col>
          <Col :span="24">
          <FormItem prop="approvalComments" label="审批意见" style="width: 100%" :rules="[
            {
              trigger: 'blur',
              message: '必填',
              required: formData.reason == '0' ? true : false,
            },
          ]">
            <Input v-model="formData.approvalComments" type="textarea" :rows="3" placeholder="请输入"
              style="width: 60%"></Input>
          </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        approvalResult: '1',
        approvalComments: '同意',
        businessType: '6'
      },
    };
  },
  mounted() {
  },
  methods: {
    getApprovalResult(data) {
      switch (data) {
        case '5':
          this.$set(this.formData, 'approvalComments', '同意')
          break;
        case '6':
          this.$set(this.formData, 'approvalComments', '不同意')
          break;
      }
    }
  },
};
</script>