<template>
    <div style="width: 100%;">
        <div class="manage-flex" style="width: 100%;">
            <div>
                <personnel-selector :value="formData.jgrybm" mode="detail" title="被监管人员" :show-case-info="true" />
                <historyTrack :jgrybm="formData.jgrybm"
                    :path="this.$path.acp_familyMeetingVideo_getNewHistoryMeetingByJgrybm" title="家属单向视频会见记录" />
            </div>
            <div style="padding: 0 16px; width: calc(100% - 390px);">
                <informant :formData="formData"></informant>
                <taskInfo :formData="formData" />
                <approveTrack :curId="formData.id" :path="this.$path.acp_familyMeetingVideo_getTrajectory" />
                
            </div>
        </div>
        <div class='bsp-base-fotter'>
            <Button @click='goBack'>返 回</Button>
            <Button @click='submit' type="primary" v-if="saveType == 'approve'">提 交</Button>
        </div>
    </div>
</template>
<script>
import personnelSelector from "@/components/personnel-selector/index.vue"
import historyTrack from "@/components/track/historyTrack.vue"
import approve from "./sp.vue"
import informant from "./informant.vue"
import approveTrack from "@/components/track/approveTrack.vue"
import taskInfo from "./taskInfo.vue"
export default {
    props: {
        modalTitle: String,
        rowData: Object,
        saveType: String
    },
    components: { personnelSelector, historyTrack, approve, informant,approveTrack,taskInfo },
    data() {
        return {
            formData: this.rowData ? this.rowData : {},
        }
    },
    mounted() {
        this.getData()
    },
    methods: {
        goBack() {
            this.$emit('on_show_table')
        },
        getData() {
            this.$store.dispatch('getRequest', { url: this.$path.acp_familyMeetingVideo_get, params: { id: this.rowData.id } }).then(res => {
                if (res.status === 200) {
                    this.formData = res.data
                } else {
                }
            })
        },
        editItem(row, index) {
            let obj = row
            this.$set(obj, 'type', 'editType')
            this.$set(obj, 'jgrybm', this.formData.jgrybm)
            this.$emit('drupEdit', obj)
        },
        addItem() {
            let row = {
                jgrybm: this.formData.jgrybm,
            }
            this.$emit('drupEdit', row)
        },
        handleDelete(id) {
            this.confirmModal({ content: '是否确认删除？' }).then(async () => {
                this.$store.dispatch('getRequest', {
                    url: this.$path.acp_socialRelations_delete,
                    params: { ids: id }
                }).then(data => {
                    if (data.success) {
                        this.successModal({
                            title: '成功提示',
                            content: '删除成功'
                        }).then(async () => {
                            this.getData()
                        })
                    } else {
                        this.errorModal({
                            title: '错误提示',
                            content: '删除失败'
                        })
                    }
                })
            })
        },
        submit() {
            this.$refs.approve.$refs.formData.validate((valid) => {
                if (valid) {
                    Object.assign(this.formData, this.$refs.approve.formData);
                    this.saveForm();
                } else {
                    this.$Message.error("请填写完整!");
                }
            });
        },
        saveForm() {
            this.$store
                .dispatch("authPostRequest", {
                    url: this.$path.acp_familyMeeting_approve,
                    params: this.formData,
                })
                .then((res) => {
                    if (res.success) {
                        this.$Message.success("提交成功");
                        this.goBack();
                    } else {
                        this.$Message.error(res.msg || "提交失败");
                    }
                });
        },
    }

}
</script>
<style scoped lang="less">
@import url('./manage.less');
</style>