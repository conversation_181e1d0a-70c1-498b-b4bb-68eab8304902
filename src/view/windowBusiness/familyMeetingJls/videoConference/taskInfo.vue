<template>
  <!-- <div class="com-modal-container"> -->
  <div class="com-module-layout">
    <p class="detail-title ml-16-title">会见信息</p>
    <div class=" fm-content-box sdInfo"
      style="margin: 0 16px;border:none !important;border-top: none;border-right: none;">
      <Form ref="formData" :model="formData" inline>
        <div class="fm-content-box" style='border:none !important;border-left:1px solid #cee0f0!important'>
          <Row>
            <Col span="3"><span>状态</span></Col>
            <Col span="5"><span>{{ formData.statusName }}</span></Col>
            <Col span="3"><span>申请时间</span></Col>
            <Col span="5"><span>{{ formData.addTime }}</span></Col>
            <Col span="3"><span>备注</span></Col>
            <Col span="5"><span>{{ formData.remarks }}</span></Col>
          </Row>
          <Row>
            <Col span="3"><span>通知会见时间</span></Col>
            <Col span="5"><span>{{ formData.notificationMeetingDate }}</span></Col>
            <Col span="3"><span>会见时间</span></Col>
            <Col span="13"><span>{{ formData.meetingStartTime }}-{{ formData.meetingEndTime }}</span></Col>

          </Row>


        </div>
      </Form>
    </div>
  </div>
  <!-- </div> -->
</template>

<script>
import { fileUpload } from 'sd-minio-upfile'

export default {
  components: {
    fileUpload
  },
  props: {
    formData: {}
  },
  watch: {
    'formData': {
      handler(n, o) {

        console.log(this.fileList)
      }, deep: true, immediate: true
    }
  },
  data() {
    return {
      serviceMark: serverConfig.OSS_SERVICE_MARK,
      bucketName: serverConfig.bucketName,
      fileList: [],
      showFile: false
    };
  },
  methods: {

  },
  created() {
  }
};
</script>
<style scoped>
@import "~@/assets/style/formInfo.css";
</style>
