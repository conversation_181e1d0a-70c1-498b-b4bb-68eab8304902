<template>
  <!--  提解记录     -->
  <div style="height: 70%;">
    <Tabs value="name" style="height: 100%;">
      <TabPane label="当面会见记录" name="name" style="height: 100%;">
        <!-- <p class="detail-title">最近{{ total }}记录</p> -->

        <div class="TimelineItemData">
          <Timeline v-if="TimelineItemData.length > 0">
            <TimelineItem v-for="(item, index) in TimelineItemData" :key="index">
              <div class="card-time-line">
                <!-- flex-time -->
                <p class="content"></p>
                <Icon type="ios-alarm" color="#2B5FD9" size="16" />&nbsp;<span>{{ item.nodeCreateTime }}</span></p>
                <p class="content " v-for="(ele, i) in item.nodeInfo">
                  <span>&nbsp;{{ ele.key }}:
                  </span><span>{{ ele.val }}</span>
                </p>
              </div>
            </TimelineItem>
          </Timeline>
          <noData v-else />
        </div>


      </TabPane>
    </Tabs>
  </div>
</template>

<script>
import noData from "@/components/bsp-empty/index.vue"
export default {
  components: { noData },
  props: {
    jgrybm: String
  },
  data() {
    return {
      total: 0,
      TimelineItemData: [],
      page: {
        pageNo: 1,
        pageSize: 20
      }
    }
  },
  watch: {
    jgrybm: {
      handler(n, o) {
        if (n) {
          this.getRecord()
        }
      }, deep: true, immediate: true
    }
  },
  methods: {
    getNo(pageNo) {
      this.$set(this.page, 'pageNo', pageNo)
      this.getRecord()
    },
    getSize(pageSize) {
      this.$set(this.page, 'pageSize', pageSize)
      this.getRecord()
    },
    getRecord() {
      let params = { jgrybm: this.jgrybm }  //'JDS-440400131-4'
      Object.assign(params, this.page)
      console.log(params, 'params')
      this.$store.dispatch('getRequest', { url: this.$path.acp_familyMeeting_getNewHistoryMeetingByJgrybm, params: params }).then(resp => {
        if (resp.success) {
          this.TimelineItemData = resp.data.list ? resp.data.list : []
          this.total = resp.data.total ? resp.data.total : 0
        } else {

        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.card-time-line {
  padding: 10px 15px;
  border: 1px solid #dcdee2 !important;
  margin-right: 16px;
  border-radius: 6px;
  box-shadow: 0px 4px 16px 1px rgba(0, 0, 0, 0.16);

  .content {
    line-height: 30px;
    font-family: Source Han Sans CN, Source Han Sans CN;
  }
}

.TimelineItemData {
  height: 84%;
  // margin-top: 16px;
  overflow-y: auto;
  padding-top: 16px;
}

/deep/ .ivu-tabs-content {
  height: 100% !important;
}

.flex-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>