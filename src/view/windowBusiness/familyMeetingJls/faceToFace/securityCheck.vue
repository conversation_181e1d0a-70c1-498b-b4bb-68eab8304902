<template>
    <!--带回安检登记 -->
    <div>
       	<Form ref="checkForm" :model="formData" :label-width="130" :label-colon="true" style="margin-right: 16px;" >
             <Row>

                <Col span="12">
                    <FormItem label="检查时间" prop="returnInspectionTime" :rules="[{ trigger: 'blur,change', message: '请选择检查时间', required: true }]" style="width: 100%;">
                         <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.returnInspectionTime"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="检查人" prop="returnInspector" :rules="[{ trigger: 'blur,change', message: '请选择检查人', required: true }]" style="width: 100%;">
                        <Input v-model="formData.returnInspector" placeholder="" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>
                                <Col span="12">
                    <FormItem label="带回民警" prop="returnPolice" :rules="[{ trigger: 'blur,change', message: '请选择带回民警', required: true }]" style="width: 100%;">
                         <user-selector style="width: 500px" v-model="formData.returnPoliceSfzh" tit="带回民警选择"
                            :text.sync="formData.returnPolice" returnField="idCard" numExp='num==1' msg="至少选中1人">
                            </user-selector>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="带回监室时间" prop="returnTime" :rules="[{ trigger: 'blur,change', message: '请选择带回监室时间', required: true }]" style="width: 100%;">
                       <el-date-picker  format='yyyy-MM-dd HH:mm:ss'  value-format='yyyy-MM-dd HH:mm:ss'
                            v-model="formData.returnTime"  style="width: 100%;"
                            type="datetime" size='small'
                            placeholder="选择日期时间">
                            </el-date-picker>
                    </FormItem>
                </Col>
                <Col span="20">
                    <FormItem label="带回安检结果" prop="returnInspectionResult" :rules="[{ trigger: 'blur,change', message: '请选择带回安检结果', required: true }]" style="width: 100%;">
                       <RadioGroup v-model="formData.returnInspectionResult" >
                            <Radio label="0" >正常</Radio>
                            <Radio label="1">异常</Radio>
                        </RadioGroup>
                    </FormItem>
                </Col>

                 <Col span="24" v-if="formData.returnInspectionResult=='1'">
                    <FormItem label="违禁物品登记" prop="prohibitedItems"  style="width: 100%;">
                        <Input v-model="formData.prohibitedItems" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>
                 <Col span="24" v-if="formData.returnInspectionResult=='1'">
                    <FormItem label="体表检查登记" prop="physicalExam"  style="width: 100%;">
                        <Input v-model="formData.physicalExam" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>
                 <Col span="24" v-if="formData.returnInspectionResult=='1'">
                    <FormItem label="异常情况登记" prop="abnormalSituations"  style="width: 100%;">
                        <Input v-model="formData.abnormalSituations" type="textarea" placeholder="请填写" maxlength="" style="width: 100%;"></Input>
                    </FormItem>
                </Col>

             </Row>
        </Form>
    </div>
</template>

<script>
import { getUuid,removeNullFields,getUserCache,formatDateparseTime } from "@/libs/util.js";
import {userSelector} from 'sd-user-selector'
export default {
   components:{userSelector},
    props:{
        curId:String
    },
   data(){
    return{
        formData:{
            returnInspectionResult:'0'
        }
    }
   },
   methods:{
    	submitClick(){
		  this.$refs['checkForm'].validate((valid) => {
			  if(valid) {
				  if(this.formData.returnInspectionResult && this.formData.returnInspectionResult=='1'){
                      if(this.formData.prohibitedItems || this.formData.physicalExam || this.formData.abnormalSituations){
                        console.log(121212)
                        this.saveForm()
                      }else{
                        this.$Message.error('违禁物品登记、体表检查登记、异常情况登记请选择任一填写完整!!')
                      }
                  }else{
                    this.saveForm()
                  }
			  } else {
				//   this.loading = false
				  this.$Message.error('请填写完整!!')
			  }
		  })
	    },
        saveForm(){ 
                    let params={id: this.curId}
                            Object.assign(params,this.formData)
                            console.log(params,'params')
                            this.$store.dispatch('authPostRequest', {url: this.$path.acp_familyMeeting_returnInspection, params: params}).then(resp => {
                            if (resp.success) {
                            this.$emit('returnInspect',true)
                            this.$Message.success('会毕安检登记成功')
                            } else {
                            this.$emit('returnInspect',false)
                            this.$Message.error(resp.msg||'会毕安检登记失败')
                            }
                        })
        }
   },
   mounted(){
          if(!this.formData.returnTime){
            this.$set(this.formData,'returnTime',formatDateparseTime(new Date()))
          }
          if(!this.formData.returnInspectionTime){
            this.$set(this.formData,'returnInspectionTime',formatDateparseTime(new Date()))
          }
          if(!this.formData.returnInspector){
            this.$set(this.formData,'returnInspector',getUserCache.getUserName())
            this.$set(this.formData,'returnInspectorSfzh',getUserCache.getIdCard())
          }
   }
}
</script>

<style>
.ivu-modal-body{
    font-family: MicrosoftYaHei, MicrosoftYaHei;
    font-weight: normal;
}
</style>