<template>
    <div class="ckywshgxgllb-wrap">
        <div class="ckywshgxgllb-wrap-left">
            <!-- 使用新的人员选择组件 -->
            <personnel-selector v-model="formData.jgrybm" title="被监管人员" placeholder="点击选择在押人员或扫码识别"
                :show-case-info="true" :enable-scan="true" :show-scan-tip="true" @change="handlePersonnelChange" />
        </div>
        <div class="ckywshgxgllb-wrap-right">
            <div class="Inquiry-wrap-right" style="width: calc(100vw - 700px);">
                <div class='bary-flex' style='margin-top:10px;'>
                    <p class="detail-title ml-16-title">家属信息</p>
                    <p><Button type="primary" @click='addInvestigatorsCoop'
                            style="cursor: pointer;" v-if="!formData.type">添加家属</Button>&nbsp;&nbsp;</p>
                </div>
                <div class='bary-flex' style="margin-left: 16px;">
                    <Form :ref="'coopformBox-' + index" class='form-box' :key="index + 'b'"
                        v-for='(item, index) in formData.socialRelationList' :model="item" :label-width="120"
                        :label-colon="true" style="margin:0 16px 0px 0;">
                        <Row style='padding:10px 0;background:#f5f7fa;margin-bottom: 16px;'>
                            <Col span="22" style="margin-bottom:0px;text-align:center;">家属（{{ Number(index + 1) }}）
                            </Col>
                            <Col span="2">
                            <Icon size="20" v-if="index > 0" @click="deleteCasePersonCoop(item, index)"
                                type="ios-close-circle" />&nbsp;&nbsp;&nbsp;</Col>
                        </Row>
                        <Row>
                            <Col span="23" style="margin-bottom:0px">
                            <FormItem label="照片采集" prop="imageUrl">
                                <RadioGroup v-model="item.isdisabled" size="large">
                                    <Radio label="5">本地上传</Radio>
                                    <Radio label="6">拍照上传</Radio>
                                </RadioGroup>
                                <br />
                                <div v-if="item.isdisabled && item.isdisabled == '6'" style="display: flex;">
                                    <div v-if="!item.imageUrl" @click="takePhoto(item, index)"
                                        style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                                        <Icon type="ios-add" size="60" color="rgb(187 187 187)" />
                                    </div>
                                    <div v-if="item.imageUrl"
                                        style="width: 96px; height: 96px;border:1px dashed #dcdee2;border-radius: 6px;text-align: center;line-height: 116px;">
                                        <img :src="item.imageUrl" style="width: 96px; height: 96px;" />
                                    </div>
                                </div>
                                <Tooltip v-else max-width="600" :transfer="true" theme="light" content="图片大小不能超过5120K。"
                                    placement="top">
                                    <s-ImageUploadLocal v-model="item.imageUrl" :maxSize="5120" ref="imgUpload"
                                        :multiple="false"
                                        @getfile="(data) => getfile(data, index, 'socialRelationList', 'imageUrl')"
                                        :defaultList="item.defaultList" :maxFiles="1" />

                                </Tooltip>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>

                            <Col span="11">
                            <FormItem label="姓名" prop="name" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <Input v-model="item.name" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="性别" prop="gender"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]"
                                style="width: 100%;">
                                <s-dicgrid v-model="item.gender" dicName="ZD_XB" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="证件类型" prop="idType" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <s-dicgrid v-model="item.idType" dicName="ZD_GABBZ_ZJZL" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="证件号码" prop="idNumber" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <Input v-model="item.idNumber" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="社会关系" prop="relationship" style="width: 100%;"
                                :rules="[{ trigger: 'blur,change', message: '请选择', required: true }]">
                                <s-dicgrid v-model="item.relationship" dicName="ZD_GABBZ_SHGX" />
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="联系方式" prop="contact" style="width: 100%;">
                                <Input v-model="item.contact" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>

                            <Col span="11">
                            <FormItem label="工作单位" prop="workUnit" style="width: 100%;">
                                <Input v-model="item.workUnit" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="职业" prop="occupation" style="width: 100%;">
                                <Input v-model="item.occupation" placeholder="" maxlength=""
                                    style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="22">
                            <FormItem label="居住地址" prop="address" style="width: 100%;">
                                <Input v-model="item.address" placeholder="" maxlength="" style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="22">
                            <FormItem label="亲属关系证明" prop="zyzsUrl" style="width: 100%;">
                                <file-upload :defaultList="item.zyzsUrl ? JSON.parse(item.zyzsUrl) : []"
                                    :serviceMark="serviceMark" :bucketName="bucketName" />
                            </FormItem>
                            </Col>
                        </Row>

                    </Form>
                </div>
                <!-- <div class="fm-content-wrap" style="padding: 0;">
                <div class="fm-content-form">
                    <Form ref="taskForm" :model="formData" :label-width="140" :label-colon="true">
                        <p class="fm-content-wrap-title"
                            style='margin-bottom: 16px;padding:10px; border-bottom: 1px solid #cee0f0; background: #eff6ff;'>
                            <Icon type="md-list-box" size="24" color="#2b5fda" />业务信息
                        </p>
                        <Row>
                            <Col span="11">
                            <FormItem label="会见方式" prop="meetingMethod" style="width: 100%;">
                                <RadioGroup v-model="formData.meetingMethod" size="large">
                                    <Radio label="0">现场会见</Radio>
                                    <Radio label="1">快速会见</Radio>
                                    <Radio label="2">远程会见</Radio>
                                </RadioGroup>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="预约会见时间" prop="arraignmentTime" style="width: 100%;">
                                <div style="position: relative;" class="laywer-flex">
                                    <el-date-picker v-if="formData.meetingMethod == '0'" size='small'
                                        @change="selectDate" key="pick01" v-model="formData.appointmentTime"
                                        format='yyyy-MM-dd' value-format='yyyy-MM-dd' type="date" placeholder="请选择" />
                                    <Select v-model="formData.appointmentTimeSlot" style="width:200px"
                                        v-if="formData.meetingMethod == '0'">
                                        <Option :value="item.time" v-for="(item, index) in applyMeetingTimeSlotList"
                                            :key="index + 'meet'" :disabled="!item.select">{{ item.timeTag }}</Option>
                                    </Select>
                                    <el-date-picker v-if="formData.meetingMethod == '1'" size='small'
                                        @change="selectDateFirst" key="pick02" v-model="formData.applyMeetingStartTime"
                                        format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss' type="datetime"
                                        placeholder="请选择会见开始时间" style="width: 100%;" />
                                    <el-date-picker format='yyyy-MM-dd HH:mm:ss' value-format='yyyy-MM-dd HH:mm:ss'
                                        v-if="formData.meetingMethod == '2'" v-model="formData.arraignmentTime"
                                        @change="selectDateSecond" key="pick03" type="datetimerange" size='small'
                                        range-separator="-" style='width:100%' start-placeholder="开始日期"
                                        end-placeholder="结束日期">
                                    </el-date-picker>
                                </div>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="会见批准机关" prop="approvalAuthority" style="width: 100%;">
                                <Input v-model="formData.approvalAuthority" placeholder="" maxlength=""
                                    style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="许可决定文书号" prop="approvalDocumentNumber" style="width: 100%;">
                                <Input v-model="formData.approvalDocumentNumber" placeholder="" maxlength=""
                                    style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="法律援助公函号" prop="legalAidOfficialLetterNumber" style="width: 100%;">
                                <Input v-model="formData.legalAidOfficialLetterNumber" placeholder="" maxlength=""
                                    style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                            <Col span="11">
                            <FormItem label="备注" prop="remarks" style="width: 100%;">
                                <Input v-model="formData.remarks" placeholder="" maxlength=""
                                    style="width: 100%;"></Input>
                            </FormItem>
                            </Col>
                        </Row>
                        <Row>
                            <Col span="16">
                            <FormItem label="上传会见批准材料附件" prop="approvalAttachmentPath" style="width: 100%;">
                                <file-upload :defaultList="formData.meetingDocumentsUrl" :serviceMark="serviceMark"
                                    :bucketName="bucketName" @fileComplete="fileCompleteFileCertUrl" />
                            </FormItem>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </div> -->
                <!-- 同行人 -->
                <!-- <companions :formData="formData" ref="companions" /> -->
                <br />

            </div>
            <div class='bsp-base-fotter'>
                <Button @click='goBack'>返 回</Button>
                <Button @click="saveData" type="primary" :loading='loading'>保 存</Button>
            </div>

            <camera-modal ref="cameraModal" @takePhoto="takePhotoItem"></camera-modal>
        </div>
    </div>
</template>
<script>
import personnelSelector from "@/components/personnel-selector/index.vue"
import { sImageUploadLocal } from '@/components/upload/image'
import { fileUpload } from 'sd-minio-upfile'
import cameraModal from '@/components/camera/camera-modal.vue'
export default {
    components: { personnelSelector, fileUpload, sImageUploadLocal, cameraModal },
    props: {
        modalTitle: String,
        rowData: Object
    },
    data() {
        return {
            loading: false,
            serviceMark: serverConfig.OSS_SERVICE_MARK,
            bucketName: serverConfig.bucketName,
            formData: {
                socialRelationList: [{ isdisabled: '5' }]
            },
            curBaryData:{}
        }
    },
    watch:{
        'rowData':{
            handler(n,o){
                  console.log(n,o,'n,on,on,o')
                  this.$set(this.formData,'jgrybm',n.jgrybm)
                                    this.$set(this.formData,'jgrybm',n.jgrybm)

                  if(n.type && n.type=='editType'){
                    this.$set(this.formData,'type',n.type)
                    this.$set(this.formData,'socialRelationList',[n])
                    this.getInfo()
                  }
           
            },deep:true,immediate:true
        }
    },
    methods: {
        /**
 * 人员选择变化处理
 */
        handlePersonnelChange(personnelData, jgrybm) {
            console.log('选择的人员:', personnelData, jgrybm)
            if (personnelData && jgrybm) {
                this.formData.prison = personnelData
                this.formData.jgrybm = jgrybm
                this.formData.jgryxm = personnelData.xm
                this.$Message.success(`已选择人员: ${personnelData.xm || '未知'}`)
            } else {
                // 清空人员信息
                this.formData.prison = null
                this.formData.jgrybm = ''
                this.formData.jgryxm = ''
            }
        },
        isValidPhoneNumber(phoneNumber) {
            // 定义中国大陆手机号码的正则表达式
            // 这个正则表达式涵盖了以13、14、15、16、17、18、19开头的号码
            const regex = /^(13[0-9]|14[01456789]|15[0-35-9]|16[2567]|17[235678]|18[0-9]|19[0-35-9])\d{8}$/;
            // 使用test方法测试手机号码是否符合正则表达式
            return regex.test(phoneNumber);
        },
        goBack() {
            this.$emit('on_show_table')
        },
        takePhoto(row, index) {
            this.curBaryData=row
            this.curBaryData.index=index
            this.$refs.cameraModal.open({});
        },
        takePhotoItem(imgUrl) {
              this.$set( this.curBaryData,'imageUrl',imgUrl)
              this.$set( this.formData.socialRelationList[this.curBaryData.index],'imageUrl',imgUrl)
        },
        deleteCasePersonCoop(item, index) {
            this.formData.socialRelationList.splice(index, 1)
        },
        getfile(file, index, fileName, fileUrl) {
            this.$set(this.formData[fileName][index], fileUrl, file.url)
            this.formData[fileName][index].defaultList = [file]
        },
        addInvestigatorsCoop() {
            let obj = { isdisabled: '5' }
            this.formData.socialRelationList.push(obj)
        },
        saveData() {
            let arr = []
            if (this.formData.socialRelationList && this.formData.socialRelationList.length > 0) {
                this.formData.socialRelationList.forEach((item, index) => {
                    arr.push(this.$refs['coopformBox-' + index][0].validate())
                })
            }
            console.log(arr, 'arr')
            // console.log(this.$refs.baseForm.formData,this.$refs.jlpz.formData,this.$refs.xnpz.formData,'.formData')
            // let arr= [this.$refs.taskForm.validate(),this.$refs.jlpz.$refs['formData'].validate(),this.$refs.xnpz.$refs['formDataXn'].validate()]
            if (!this.formData.jgrybm) {
                this.$Message.error('请选择被监管人员！')
                return
            }
            Promise.all(arr).then(data => {
                console.log(data, '121   2')
                console.log(this.formData, data, 'params')
                if (data.every(item => item)) {
                    if(this.formData.type){
                       this.updateForm()
                    }else{
                       this.saveDataForm()
                    }

                } else {
                    this.$Message.error('请填写完整')
                    this.loading = false
                }

            })
        },
        saveDataForm() {

            this.$store.dispatch('authPostRequest', { url: this.$path.acp_socialRelations_create, params: this.formData }).then(resp => {
                if (resp.success) {
                    this.loading = false
                    this.goBack()
                    this.$Message.success('社会关系保存成功')
                } else {
                    this.loading = false
                    this.$Message.error(resp.msg || '社会关系保存失败')
                }
            }).catch(err => {
                this.loading = false
            })
        },
        updateForm(){
            this.$store.dispatch('authPostRequest', { url: this.$path.acp_socialRelations_update, params: this.formData.socialRelationList[0] }).then(resp => {
                            if (resp.success) {
                                this.loading = false
                                this.goBack()
                                this.$Message.success('社会关系保存成功')
                            } else {
                                this.loading = false
                                this.$Message.error(resp.msg || '社会关系保存失败')
                            }
                        }).catch(err => {
                            this.loading = false
                        })
        },
        getInfo(){
            this.$store.dispatch('getRequest', { url: this.$path.acp_socialRelations_get, params: {id:this.formData.socialRelationList[0].id} }).then(resp => {
                if (resp.success) {
                    resp.data.defaultList=[{fileUrl:resp.data.imageUrl}]
                  this.formData.socialRelationList=[resp.data]
                  if(resp.data.imageUrl){

                  }
                  
                } else {
                }
            }).catch(err => {
            })
        }
        
    }
}
</script>
<style scoped>
.ckywshgxgllb-wrap {
    width: 100%;
    display: flex;
}

.bary-flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}
</style>