<template>
  <div class="" style="height: 95%;overflow: hidden;">
    <div class="fm-content-info bsp-base-content" style="top:48px !important;">
      <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200" label-colon style="padding: 0 .625rem;">
        <div class="bsp-base-subtit" style="height: 100%;overflow-x: hidden;overflow-y: auto;">
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />巡视信息</p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="巡视时间" prop="xssj" :rules="[{trigger: 'blur,change',message: '巡视时间为必填',required: true,type: 'array',}]">
                  <el-date-picker type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" v-model="formData.xssj" size="small"  placeholder="请选择" style="width: 100%;" @change="xssjChange"/>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
                <FormItem label="巡视地点" prop="checkAreaGroup" >
                  <Row>
                    <Col span="5" class="formItemContentDiv">
                      <div class="labelDiv">按区域</div>
                      <Checkbox :indeterminate="indeterminateArea" :value="checkAllArea" @click.prevent.native="handleAreaCheckAll">全部监区</Checkbox>
                    </Col>
                    <Col span="19">
                      <CheckboxGroup v-model="formData.checkAreaGroup" @on-change="checkAreaAllGroupChange">
                        <Checkbox  v-for="item in areaList" :label="item.areaId" :key="item.areaId">{{ item.areaName }}</Checkbox>
                      </CheckboxGroup>
                    </Col>
                  </Row>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
                <FormItem label="" prop="checkTypeGroup" >
                  <Row>
                    <Col span="5" class="formItemContentDiv">
                      <div class="labelDiv">按类型</div>
                      <Checkbox :indeterminate="indeterminateType" :value="checkAllType" @click.prevent.native="handleTypeCheckAll">全部类型</Checkbox>
                    </Col>
                    <Col span="19">
                      <CheckboxGroup  v-model="formData.checkTypeGroup" @on-change="checkTypeAllGroupChange">
                        <Checkbox  v-for="item in roomTypeList" :label="item.code" :key="item.code">{{ item.name }}</Checkbox>
                      </CheckboxGroup >
                    </Col>
                  </Row>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16">
                <FormItem label="" prop="dummyField" style="height: 20px">
                  <!-- 仅用于触发验证 -->
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16>">
                <FormItem label="巡视内容" prop="xsnr" :rules="[{ trigger: 'change', message: '巡视内容为必填', required: true }]">
                  <Input v-model="formData.xsnr" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}" style="width: 820px"></Input>
                </FormItem>
              </Col>
            </Row>
          </div>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />巡视领导信息</p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="巡视领导" prop="xsr" :rules="[{ trigger: 'blur', message: '巡视领导为必填', required: true }]">
                  <Input type="text" v-model="formData.xsr" placeholder="系统自动带出信息"/>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="登记时间" prop="djsj">
                  <div class="ivu-form-item-label">{{formData.djsj}}</div>
                </FormItem>
              </Col>
            </Row>
          </div>
          <p class="fm-content-info-title"><Icon type="md-list-box" size="24" color="#2b5fda" />巡视结果信息</p>
          <div class="form">
            <Row>
              <Col span="8">
                <FormItem label="巡视是否发现问题" prop="xssffxwt" :rules="[{ trigger: 'blur,change', message: '巡视是否发现问题为必填', required: true }]">
                  <RadioGroup v-model="formData.xssffxwt" @on-change="xssffxwtChange">
                    <Radio label="1">是</Radio>
                    <Radio label="2">否</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>
              <Col span="8">
                <FormItem label="问题通知人" prop="wttzrName" :key="wttzrFormItemKey" :rules="wttzrNameRules" style="width: 100%;">
                  <user-selector style="width: 500px" v-model="formData.wttzr" tit="问题通知人" :text.sync="formData.wttzrName" returnField="idCard" numExp='num==1' msg="至少选中1人">
                  </user-selector>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span="16>">
                <FormItem label="巡视结果信息" prop="xsjgxx" :rules="[{ trigger: 'change', message: '巡视结果信息为必填', required: true }]">
                  <Input v-model="formData.xsjgxx" placeholder="请填写"  type="textarea" :autosize="{minRows: 2,maxRows: 5}" style="width: 820px"></Input>
                </FormItem>
              </Col>
            </Row>
          </div>
        </div>
      </Form>
    </div>


    <div class="bsp-base-fotter" >
      <Button style="margin: 0 20px" @click="handleClose()">返 回 </Button>
      <Button style="margin: 0 20px"  type="primary" :loading="loading" @click="handleSubmit(true)">提交</Button>
    </div>
  </div>
</template>

<script>
  import {mapActions} from "vuex";
  import { getUserCache } from '@/libs/util'
  import {userSelector} from 'sd-user-selector'

  export default {
    components:{
      userSelector
    },
    data(){
      const validateBothEmpty = (rule, value, callback) => {
        const { checkAreaGroup, checkTypeGroup } = this.formData;

        if ((!checkAreaGroup || checkAreaGroup.length === 0) && (!checkTypeGroup || checkTypeGroup.length === 0)) {
          callback(new Error('巡视地点为必填'));
        } else {
          callback(); // 验证通过
        }
      };
      return{
        ruleValidate: {
          dummyField: [
            {
              validator: validateBothEmpty,
              trigger: 'change'
            }
          ]
        },
        formData:{
          checkAreaGroup:[],
          checkTypeGroup:[],
        },
        wttzrFormItemKey:0,
        areaList: [],
        areaCodeList: [],
        roomTypeList: [],
        roomTypeCodeList: [],
        checkAllArea:false,
        indeterminateArea:false,
        checkAllType:false,
        indeterminateType:false,
        loading:false,
        isCheckwttzr:true
      }
    },
    computed:{
      wttzrNameRules() {
        const rules = [];

        if (this.formData.xssffxwt === '1') {
          rules.push({ trigger: 'blur,change', message: '请选择问题通知人', required: true });
        }
        console.log(rules,"rules---")
        return rules;
      },
    },
    methods:{
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      handleClose(){
        this.$emit('close',false)
      },
      getCurrentTimeFormatted() {
        const now = new Date();

        const year = now.getFullYear();             // 获取年份
        const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，要加1，并补零
        const day = String(now.getDate()).padStart(2, '0');        // 日期补零

        const hours = String(now.getHours()).padStart(2, '0');     // 小时补零
        const minutes = String(now.getMinutes()).padStart(2, '0'); // 分钟补零
        const seconds = String(now.getSeconds()).padStart(2, '0'); // 秒数补零

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      getAreaByOrgCode(){
        let params = {
        }
        let orgCode = getUserCache.getOrgCode()
        params.orgCode = orgCode
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_areaGetAreaByOrgCode,
          params:params
        }).then(resp=>{
          this.loading=false
          if (resp.code==0){
            if(resp.data && resp.data && resp.data.length > 0){
              this.areaList = resp.data.map(item => ({
                areaId: item.areaCode,
                areaName: item.areaName,
              }));
              this.areaCodeList = resp.data.map(item => item.areaCode);
            }
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      getRoomTypeList(){
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_getRoomTypeList,
          params: {}
        }).then(resp=>{
          this.loading=false
          if (resp.code==0){
            if(resp.data && resp.data && resp.data.length > 0){
              this.roomTypeList = resp.data.map(item => ({
                code: item.code,
                name: item.name,
              }));
              this.roomTypeCodeList = resp.data.map(item => item.code);
            }
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
      xssjChange(data){
        console.log("日期改变")
        this.formData.xskssj = data[0]
        this.formData.xsjssj = data[1]
        this.formData.xssj = data
      },
      xssffxwtChange(data){
        this.$nextTick(() => {
          this.wttzrFormItemKey += 1;
        });
      },
      handleAreaCheckAll () {
        console.log("------")
        if (this.indeterminateArea) {
          this.checkAllArea = false;
        } else {
          this.checkAllArea = !this.checkAllArea;
        }
        this.indeterminateArea = false;

        if (this.checkAllArea) {
          this.formData.checkAreaGroup = this.areaCodeList
        } else {
          this.formData.checkAreaGroup = [];
        }
      },
      checkAreaAllGroupChange (data) {
        if (data.length === this.areaCodeList.length) {
          this.indeterminateArea = false;
          this.checkAllArea = true;
        } else if (data.length > 0) {
          this.indeterminateArea = true;
          this.checkAllArea = false;
        } else {
          this.indeterminateArea = false;
          this.checkAllArea = false;
        }
      },
      handleTypeCheckAll(){
        console.log("------")
        if (this.indeterminateType) {
          this.checkAllType = false;
        } else {
          this.checkAllType = !this.checkAllType;
        }
        this.indeterminateType = false;

        if (this.checkAllType) {
          this.formData.checkTypeGroup = this.roomTypeCodeList
        } else {
          this.formData.checkTypeGroup = [];
        }
      },
      checkTypeAllGroupChange (data) {
        if (data.length === this.roomTypeCodeList.length) {
          this.indeterminateType = false;
          this.checkAllType = true;
        } else if (data.length > 0) {
          this.indeterminateType = true;
          this.checkAllType = false;
        } else {
          this.indeterminateType = false;
          this.checkAllType = false;
        }
      },
      mergeArrays(arr1, arr2) {
        const a = Array.isArray(arr1) ? arr1 : [];
        const b = Array.isArray(arr2) ? arr2 : [];

        return [...a, ...b];
      },
      handleSubmit(){
        this.loading= true
        this.$refs['formData'].validate((valid) => {
          if (valid) {
            this.saveData()
          } else {
            this.loading=false
            this.$Message.error('请填写完整!!');
          }
        })
      },
      saveData(){
        let areaNameList = []
        if(this.formData.checkAreaGroup){
          const idToAreaNameMap = new Map(this.areaList.map(item => [item.areaId, item.areaName]));
          areaNameList = this.formData.checkAreaGroup.map(areaId => idToAreaNameMap.get(areaId) || null).filter(areaName => areaName !== null);
        }
        let typeNameList = []
        if(this.formData.checkTypeGroup){
          const idToNameMap = new Map(this.roomTypeList.map(item => [item.code, item.name]));
          typeNameList = this.formData.checkTypeGroup.map(code => idToNameMap.get(code) || null).filter(name => name !== null);
        }

        let xsddCodeList = this.mergeArrays(this.formData.checkAreaGroup,this.formData.checkTypeGroup)

        let xsddNameList = this.mergeArrays(areaNameList,typeNameList)
        this.formData.xsddCodeList = xsddCodeList
        this.formData.xsddNameList = xsddNameList
        let params = this.formData
        params.xsddCode = this.formData.xsddCodeList.join(',')
        params.xsddName =  this.formData.xsddNameList.join(',')
        params.url = "/#/leaderWorkBusiness/inspection"

        let url = this.$path.app_leaderInspectionCreate
        this.$store.dispatch('authPostRequest',{
          url: url,
          params:params
        }).then(resp => {
          this.loading=false
          if (resp.code==0){
            this.$Message.success('提交成功!');
            this.handleClose();
          }else{
            this.$Notice.error({
              title:'错误提示',
              desc:resp.msg
            })
          }
        })
      },
    },
    mounted() {
      this.formData.djsj = this.getCurrentTimeFormatted();
      this.$set(this.formData,'xsr',getUserCache.getUserName())
      this.getAreaByOrgCode()
      this.getRoomTypeList()
    }
  }
</script>

<style scoped lang="less">
  .formItemContentDiv{
    display: flex;
    align-items: center;

  }
  .labelDiv{
    width: 70px;
    font-size: 16px;
  }
</style>
