<template>
    <div>
        <div class="bsp-base-form">
            <div class="bsp-base-tit">{{ modalTitle }}</div>
            <div class="bsp-base-content">
                <div class="bl-content">
                    <RyxxDetail :ryFormData="mzData"></RyxxDetail>
                    <XzdjDetail :formData="mzData"></XzdjDetail>
                    <yzdj @showBlModal="showBlModal" :formData="yzData"></yzdj>
                    <cfsy :formData="cfData" :doctorAdviceType="yzData.doctorAdviceType"></cfsy>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="returnBack">返 回</Button>
                <Button @click="sureSubmit">保 存</Button>
            </div>
        </div>
        <div v-if="modalShow">
            <Modal v-model="modalShow" title="调用模版" footer-hide width="1000">
                <mbModal :templateType="templateType" @useMb="useMb"></mbModal>
            </Modal>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex'
import RyxxDetail from '@/components/bl-form/ryxx-detail.vue'
import XzdjDetail from '@/components/bl-form/xzdj-detail.vue'
import yzdj from '@/components/bl-form/yzdj.vue'
import cfsy from '@/components/bl-form/cfsy.vue'
import mbModal from '@/components/bl-form/modal.vue'
import commonMethods from '@/view/snjy/snmz/common.js'
import { formatDateparseTime } from '@/libs/util'

export default {
  mixins: [commonMethods],
  components: {
    RyxxDetail,
    XzdjDetail,
    yzdj,
    cfsy,
    mbModal
  },
  props: {
    modalTitle: String,
    ryId: String,
    cfId: String,
    type: String
  },
  data () {
    return {
      modalShow: false,
      mzData: {},
      yzData: {},
      cfData: {},
      statusList: []
    }
  },
  watch: {
    statusList (value) {
      console.log(value, 'valuestatusList')
      const index = value.indexOf(false)
      if (index != -1) {
        let message = index == 0 ? '门诊登记' : index == 1 ? '医嘱登记' : '处方施药'
        this.$Message.error(message + '校验不通过')
      } else {
        this.create()
      }
    }
  },
  mounted () {
    this.getDetail()
    this.$bus.$on('sureSubmitValue', (data, type, status) => { // 监听事件
      console.log('create_visit_xc', data, type, status)
      if (type == 'yzdj') {
        this.$set(this.statusList, 1, status)
        this.yzData = data
      } else {
        this.$set(this.statusList, 2, status)
        this.cfData = data
      }
    })
  },
  beforeDestroy () {
    this.$bus.$off('sureSubmitValue') // 移除事件监听器
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    returnBack () {
      this.$emit('on_show_table')
    },
    sureSubmit () {
      // 注册监听事件
      this.$bus.$emit('sureSubmitYZDJ')
      this.$bus.$emit('sureSubmitCFSY')
      console.log('sureSubmit')
    },
    getDetail () {
      this.authGetRequest({
        url: this.$path.get_visit_xc,
        params: {
          id: this.ryId
        }
      }).then((res) => {
        if (res.success) {
          this.mzData = res.data
          //   this.yzData = res.data.prescribe;
          //   this.cfData = {
          //     ...res.data.prescribe,
          //     dataTable: this.yzData.medicineList,
          //   };
        }
      })

      if (this.type == 'xcsy') {
        this.authGetRequest({
          url: this.$path.get_visit_xc_list,
          params: {
            visitId: this.ryId
          }
        }).then((res) => {
          if (res.success) {
            // this.mzData = res.data;
            // this.yzData = res.data.prescribe;
            this.cfData = {
              //   ...res.data.prescribe,
              dataTable: res.data ? res.data : []
            }
          }
        })
      }
      if (this.type == 'kjcf') {
        this.authGetRequest({
          url: this.$path.get_cf_xq,
          params: {
            id: this.cfId
          }
        }).then((res) => {
          if (res.success) {
            // this.mzData = res.data
            this.yzData = res.data.prescribe
            this.cfData = {
              ...res.data.prescribe,
              dataTable: this.yzData.medicineList ? this.yzData.medicineList : [],
              date: [res.data.prescribe.startPrescribeDate, res.data.prescribe.endPrescribeDate]
            }
          }
        })
      }
    },
    getsysBh (url) {
      return this.authPostRequest({
        url: this.$path.get_ihc_cgsq + url,
        params: { formData: {} }
      })
        .then((resp) => {
          if (resp.code == 200) {
            return resp.data
          } else {

          }
        })
    },
    async create () {
      let doctorAdviceNum = await this.getsysBh('/ihc_yz_no')
      let prescribeNum = await this.getsysBh('/ihc_cf_no')
      if (this.cfData.dataTable.length > 0) {
        this.cfData.dataTable.forEach(e => {
          e.id = ''
        })
      }
      let params = {
        // mainComplaint: this.mzData.mainComplaint,
        // illnessResume: this.mzData.illnessResume,
        // visitProcessMethod:'2',
        // visitState:this.formData.visitState,
        // visitConclusion:this.formData.visitConclusion,
        // visitTime:this.formData.visitTime,
        // jgrybm:this.$refs.ryxxDetail?this.$refs.ryxxDetail.formData.jgry.jgrybm:this.$refs.ryxx.formData.jgrybm,
        // diseaseTime:this.$refs.ryxxDetail?formatDateparseTime(this.$refs.ryxxDetail.formData.jgry.diseaseTime):formatDateparseTime(this.$refs.ryxx.formData.diseaseTime),
        prescribe: {
          ...this.yzData,
          medicineList: this.cfData.dataTable,
          id: this.cfId,
          businessId: this.ryId,
          startPrescribeDate: formatDateparseTime(this.cfData.date[0]),
          endPrescribeDate: formatDateparseTime(this.cfData.date[1]),
          doctorAdvice: this.cfData.doctorAdvice,
          prescribeDescribe: this.cfData.prescribeDescribe,
          prescribeNum,
          doctorAdviceNum
        },
        id: this.ryId
      }
      delete this.mzData.id
      Object.assign(params, this.mzData)

      this.authPostRequest({
        url: this.$path.update_visit_xc_cf,
        params
      }).then(res => {
        if (res.success) {
          this.$Notice.success({
            title: '成功提示',
            desc: res.msg
          })
          this.returnBack()
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: res.msg
          })
        }
      })
    }
  }
}
</script>
