<template>
  <div>
    <div class="bsp-base-form">
      <div class="bsp-base-tit">{{ modalTitle }}</div>
      <div class="bsp-base-content">
        <div class="bl-content">
          <RyxxDetail  :ryFormData="mzData"></RyxxDetail>
          <XzdjDetail :formData="mzData"></XzdjDetail>
          <YzdjDetail :formData="yzData" v-if="mzData.visitProcessMethod == '2'"></YzdjDetail>
          <YpxxDetail :formData="cfData" v-if="mzData.visitProcessMethod == '1'"></YpxxDetail>
          <CfsyDetail :formData="cfData" v-if="mzData.visitProcessMethod == '2'"></CfsyDetail>
        </div>
      </div>
      <div class="bsp-base-fotter">
        <Button @click="returnBack">返 回</Button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import RyxxDetail from '@/components/bl-form/ryxx-detail.vue'
import XzdjDetail from '@/components/bl-form/xzdj-detail.vue'
import YzdjDetail from '@/components/bl-form/yzdj-detail.vue'
import CfsyDetail from '@/components/bl-form/cfsy-detail.vue'
import YpxxDetail from '@/components/bl-form/ypxx-detail.vue'

export default {
  components: {
    RyxxDetail,
    XzdjDetail,
    YzdjDetail,
    CfsyDetail,
    YpxxDetail
  },
  props: {
    modalTitle: String,
    ryId: String,
    cfId: String,
    type: String
  },
  data () {
    return {
      mzData: {},
      yzData: {},
      cfData: {}
    }
  },
  mounted () {
    this.getDetail()
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    returnBack () {
      this.$emit('on_show_table')
    },
    getDetail () {
      this.authGetRequest({
        url: this.$path.get_visit_xc,
        params: {
          id: this.ryId
        }
      }).then((res) => {
        if (res.success) {
          this.mzData = res.data
          //   this.yzData = res.data.visitMedicines;
          this.cfData = {
            ...res.data.prescribe,
            dataTable: res.data.visitMedicines ? res.data.visitMedicines : []
          }
        }
      })

      //   if (this.type == "xcsy") {
      //     this.authGetRequest({
      //       url: this.$path.get_visit_xc_list,
      //       params: {
      //         visitId: this.ryId,
      //       },
      //     }).then((res) => {
      //       if (res.success) {
      //         // this.mzData = res.data;
      //         // this.yzData = res.data.prescribe;
      //         this.cfData = {
      //         //   ...res.data.prescribe,
      //           dataTable: res.data,
      //         };
      //       }
      //     });
      //   }
      if (this.type == 'kjcf') {
        this.authGetRequest({
          url: this.$path.get_cf_xq,
          params: {
            id: this.cfId
          }
        }).then((res) => {
          if (res.success) {
            // this.mzData = res.data;
            this.yzData = res.data.prescribe
            this.cfData = {
              ...res.data.prescribe,
              dataTable: this.yzData.medicineList
            }
          }
        })
      }
    }
  }
}
</script>
