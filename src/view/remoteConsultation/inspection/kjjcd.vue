<template>
    <div>
        <div class="bsp-base-form">
            <div class="bsp-base-tit">{{ modalTitle }}</div>
            <div class="bsp-base-content">
                <div class="bl-content">
                    <RyxxDetail :ryId="ryId"></RyxxDetail>
                    <xzdj @showBlModal="showBlModal" :formData="mzData"></xzdj>
                    <yzdj @showBlModal="showBlModal" :formData="yzData"></yzdj>
                    <!-- <jcxm></jcxm> -->
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="returnBack">返 回</Button>
            </div>
        </div>
        <div v-if="modalShow">
            <Modal v-model="modalShow" title="调用模版" footer-hide width="1000">
                <mbModal :templateType="templateType" @useMb="useMb"></mbModal>
            </Modal>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex'
import RyxxDetail from '@/components/bl-form/ryxx.vue'
import xzdj from '@/components/bl-form/xzdj.vue'
import jcxm from '@/components/bl-form/jcxm.vue'
import mbModal from '@/components/bl-form/modal.vue'
import commonMethods from '@/view/snjy/snmz/common.js'
import yzdj from '@/components/bl-form/yzdj.vue'

export default {
  mixins: [commonMethods],
  components: {
    RyxxDetail,
    xzdj,
    jcxm,
    mbModal,
    yzdj
  },
  props: {
    modalTitle: String,
    ryId: String,
    cfId: String
  },
  data () {
    return {
      modalShow: false,
      mzData: {
        visitProcessMethod: 1
      }
    }
  },
  mounted () {
    this.$dicKit.getDataRows('ZD_OUTPATIENT_CHECKLIST_CATEGORY').then(res => {
      console.log(res)
    })
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    returnBack () {
      this.$emit('on_show_table')
    }
  }
}
</script>
