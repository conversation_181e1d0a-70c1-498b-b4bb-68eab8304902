<template>
    <div>
        <div class="bsp-base-form">
            <div class="bsp-base-tit">{{ modalTitle }}</div>
            <div class="bsp-base-content">
                <div class="bl-content">
                    <ryxxDetail :ryId="ryId" v-if="ryId" ref="ryxxDetail"></ryxxDetail>
                    <ryxx v-else ref="ryxx" :bbyyRequire="false"></ryxx>
                    <xzdj @showBlModal="showBlModal" ref="xzdj" :formData="mzData" @getClfs="getClfs"></xzdj>
                    <yzdj @showBlModal="showBlModal" :formData="yzData" v-if="clfs == '2'"></yzdj>
                    <cfsy :formData="cfData" v-if="clfs == '2'" :doctorAdviceType="yzData.doctorAdviceType"></cfsy>
                    <ypxx :formData="cfData" v-if="clfs == '1'" :xcsy="false"></ypxx>
                    <div v-if="modalShow">
                        <Modal v-model="modalShow" title="调用模版" footer-hide width="1000">
                            <mbModal :templateType="templateType" @useMb="useMb"></mbModal>
                        </Modal>
                    </div>
                </div>
            </div>
            <div class="bsp-base-fotter">
                <Button @click="returnBack">返 回</Button>
                <Button @click="sureSubmit" :loading="loading" style="background:#2d8cf0 ;color: #fff;">提 交</Button>
            </div>
        </div>

    </div>
</template>

<script>
import { mapActions } from 'vuex'
import { formatDateparseTime } from '@/libs/util'
import commonMethods from '@/view/snjy/snmz/common.js'
import ryxxDetail from '@/components/bl-form/ryxx-detail.vue'
import ryxx from '@/components/bl-form/ryxx.vue'
import xzdj from '@/components/bl-form/xzdj.vue'
import yzdj from '@/components/bl-form/yzdj.vue'
import cfsy from '@/components/bl-form/cfsy.vue'
import ypxx from '@/components/bl-form/ypxx.vue'
import mbModal from '@/components/bl-form/modal.vue'

export default {
  mixins: [commonMethods],
  components: {
    ryxxDetail,
    ryxx,
    xzdj,
    yzdj,
    cfsy,
    mbModal,
    ypxx
  },
  props: {
    modalTitle: String,
    ryId: String
  },
  data () {
    return {
      loading: false,
      clfs: 1,
      modalShow: false,
      templateType: null,
      mzData: {
        templateName: '',
        illnessResume: '',
        auxiliaryCheck: '',
        mainComplaint: '',
        medicalHistory: '',
        physicalCheck: '',
        primaryDiagnosis: '',
        suggestion: '',
        visitConclusion: '',
        visitState: '',
        visitProcessMethod: '1'
      },
      yzData: {
        templateName: '',
        illnessResume: '',
        auxiliaryCheck: '',
        mainComplaint: '',
        medicalHistory: '',
        physicalCheck: '',
        primaryDiagnosis: '',
        suggestion: '',
        visitConclusion: '',
        visitState: '',
        doctorAdviceType: '1'
      },
      cfData: {
        date: [],
        doctorAdvice: '',
        dataTable: [{}],
        templateName: '',
        illnessResume: '',
        auxiliaryCheck: '',
        mainComplaint: '',
        medicalHistory: '',
        physicalCheck: '',
        primaryDiagnosis: '',
        suggestion: '',
        visitConclusion: '',
        visitState: ''
      },
      statusList: [],
      formData: {}
    }
  },
  watch: {
    statusList (value) {
      const index = value.indexOf(false)
      // this.formData.visitTime?'':this.$set(this.formData,'visitTime',formatDateparseTime(new Date()));
      // console.log(this.formData.visitTime,'this.formData.visitTime')
      if (index != -1) {
        let message = index == 0 ? '巡诊登记' : index == 1 ? '医嘱登记' : index == 2 && this.clfs == 1 ? '药品信息' : '处方施药'
        this.$Message.error(message + '校验不通过')
      } else {
        this.$refs.ryxx.$refs.formData.validate((valid) => {
          if (valid) {
            this.create()
          } else {
            this.$Message.error('人员基本信息校验不通过')
          }
        })
      }
    }
  },
  mounted () {
    this.$bus.$on('sureSubmitValue', (data, type, status) => { // 监听事件
      console.log('create_visit_xc', data, type, status)
      this.formData = this.$refs.xzdj.formData
      if (type == 'mzdj') {
        this.$set(this.statusList, 0, status)
        this.mzData = data
      } else if (type == 'yzdj') {
        this.$set(this.statusList, 1, status)
        this.yzData = data
      } else {
        this.$set(this.statusList, 2, status)
        this.cfData = data
      }
    })
  },
  beforeDestroy () {
    this.$bus.$off('sureSubmitValue') // 移除事件监听器
  },
  methods: {
    ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest']),
    getClfs (data) {
      if (data == 1 || data == 2) {
        this.cfData = {
          date: [],
          doctorAdvice: '',
          dataTable: [{}],
          templateName: '',
          illnessResume: '',
          auxiliaryCheck: '',
          mainComplaint: '',
          medicalHistory: '',
          physicalCheck: '',
          primaryDiagnosis: '',
          suggestion: '',
          visitConclusion: '',
          visitState: ''
        }
      }
      this.clfs = data
    },
    returnBack () {
      this.$emit('on_show_table')
    },
    sureSubmit () {
      // 注册监听事件
      this.$bus.$emit('sureSubmitXZDJ')
      this.$bus.$emit('sureSubmitYZDJ')
      this.$bus.$emit('sureSubmitYPXX')
      this.$bus.$emit('sureSubmitCFSY')
    },
    getsysBh (url) {
      return this.authPostRequest({
        url: this.$path.get_ihc_cgsq + url,
        params: { formData: {} }
      })
        .then((resp) => {
          if (resp.code == 200) {
            return resp.data
          } else {

          }
        })
    },
    async create () {
      this.loading = true
      let doctorAdviceNum = await this.getsysBh('/ihc_yz_no')
      let prescribeNum = await this.getsysBh('/ihc_cf_no')
      if (this.cfData.dataTable.length && this.cfData.dataTable.length > 0) {
        this.cfData.dataTable.forEach(e => {
          e.id = ''
        })
      }
      this.formData.visitTime ? '' : this.$set(this.formData, 'visitTime', formatDateparseTime(new Date()))
      let params = {
        mainComplaint: this.mzData.mainComplaint,
        // illnessResume: this.mzData.illnessResume,
        visitProcessMethod: this.clfs,
        visitState: this.formData.visitState,
        visitConclusion: this.formData.visitConclusion,
        visitTime: this.formData.visitTime,
        jgrybm: this.$refs.ryxxDetail ? this.$refs.ryxxDetail.formData.jgry.jgrybm : this.$refs.ryxx.formData.jgrybm,
        diseaseTime: this.$refs.ryxxDetail ? formatDateparseTime(this.$refs.ryxxDetail.formData.jgry.diseaseTime) : formatDateparseTime(this.$refs.ryxx.formData.diseaseTime),
        diseaseReason: this.$refs.ryxxDetail ? this.$refs.ryxxDetail.formData.jgry.diseaseReason : this.$refs.ryxx.formData.diseaseReason
      }
      let url = ''// this.$path.create_visit_xc
      this.$set(this.formData, 'visitProcessMethod', this.clfs)
      switch (Number(this.clfs)) {
        case 1:
          params.visitMedicines = this.cfData.dataTable
          url = this.$path.create_visit_xc
          break
        case 2:
          params.prescribe = {
            ...this.yzData,
            medicineList: this.cfData.dataTable,
            id: '',
            businessId: this.ryId,
            startPrescribeDate: formatDateparseTime(this.cfData.date[0]),
            endPrescribeDate: formatDateparseTime(this.cfData.date[1]),
            doctorAdvice: this.cfData.doctorAdvice,
            prescribeDescribe: this.cfData.prescribeDescribe,
            prescribeNum,
            doctorAdviceNum
          },
          url = this.$path.create_visit_xc_cf
          break
        case 3:
          url = this.$path.create_visit_xc_wxyy
          break
      }
      this.authPostRequest({
        url: url,
        params
      }).then(res => {
        if (res.success) {
          this.loading = false
          this.$Notice.success({
            title: '成功提示',
            desc: res.msg
          })
          this.returnBack()
        } else {
          this.loading = false
          this.$Notice.error({
            title: '错误提示',
            desc: res.msg
          })
        }
      })
        .catch(error => {
          this.loading = false
        })
    }
  }
}
</script>
