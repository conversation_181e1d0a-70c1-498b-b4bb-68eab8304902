# 病情管理表单组件

这是一个基于Vue2开发的病情管理表单组件，符合项目开发规范，提供完整的病情信息录入、编辑和查看功能。

## 组件结构

```
src/view/sick/
├── manage/
│   ├── index.vue          # 主页面（列表页）
│   ├── addSick.vue        # 新增病情页面
│   ├── addSickCondition.vue # 病情登记页面
│   ├── addSickPerson.vue  # 病情人员选择组件
│   └── record.vue         # 病情记录组件
├── demo.vue               # 组件演示页面
└── README.md              # 说明文档
```

## 字段说明

### 表单字段

| 字段名 | 中文名称 | 类型 | 是否必填 | 说明 |
|--------|----------|------|----------|------|
| sickInfo | 病情情况 | string | 是 | 详细的病情描述，支持500字符 |
| sickType | 病号类别 | string | 是 | 通过字典选择病号类别 |
| status | 状态 | integer | 是 | 0-未解除, 1-解除 |
| treatmentInfo | 治疗情况 | string | 是 | 详细的治疗方案和情况描述，支持500字符 |

## 组件功能

### 1. 主页面 (index.vue)
- 病情记录列表展示
- 支持新增、病情登记操作
- 集成数据表格组件
- 动态组件切换

### 2. 新增页面 (addSick.vue)
- 左侧人员选择区域
- 右侧表单编辑区域
- 响应式布局设计

### 3. 病情登记页面 (addSickCondition.vue)
- 病情登记表单
- 人员信息展示
- 病情记录管理

### 4. 病情人员选择组件 (addSickPerson.vue)
- 人员选择功能
- 表单验证
- 数据提交处理

### 5. 病情记录组件 (record.vue)
- 病情记录列表
- 记录查看功能

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <!-- 病情管理主页面 -->
    <sick-manage />
  </div>
</template>

<script>
import SickManage from '@/view/sick/manage/index.vue'

export default {
  components: {
    SickManage
  }
}
</script>
```

### 单独使用病情登记组件

```vue
<template>
  <div>
    <!-- 病情登记 -->
    <addSickCondition
      :curData="formData"
      :curId="currentId"
      @toback="handleBack"
    />
  </div>
</template>

<script>
import AddSickCondition from '@/view/sick/manage/addSickCondition.vue'

export default {
  components: {
    AddSickCondition
  },
  data() {
    return {
      formData: {},
      currentId: ''
    }
  },
  methods: {
    handleBack() {
      // 处理返回逻辑
    }
  }
}
</script>
```

## API接口

组件使用以下API接口：

```javascript
// 病情管理相关接口
export default {
  // 创建病情记录
  sick_create: '/ihc-com/ihc/sick/manage/create',

  // 更新病情记录
  sick_update: '/ihc-com/ihc/sick/manage/update',

  // 获取病情详情
  sick_get: '/ihc-com/ihc/sick/manage/get',

  // 删除病情记录
  sick_delete: '/ihc-com/ihc/sick/manage/delete',

  // 病情列表分页
  sick_page: '/ihc-com/ihc/sick/manage/page',

  // 病号类别字典
  sick_type_dic: '/ihc-com/ihc/sick/manage/sick-type/dic'
}
```

## 字典配置

需要配置以下字典：

- `ZD_SICK_TYPE`: 病号类别字典

## 样式说明

组件使用Less预处理器，遵循以下样式规范：

- 使用scoped样式隔离
- 遵循BEM命名规范
- 响应式设计
- 统一的色彩主题 (#2b5fda)

## 注意事项

1. 确保已正确安装和配置以下依赖：
   - Vue 2.6.10
   - View UI (iView)
   - sd-dic-grid
   - sd-data-grid

2. 确保API接口路径配置正确

3. 确保字典数据已正确配置

4. 组件依赖Vuex进行状态管理

## 演示页面

访问 `/sick/demo` 路由可以查看组件的完整演示效果。

## 开发规范

本组件严格遵循项目Vue2开发规范：

- 使用选项式API
- 组件命名使用PascalCase
- 事件命名使用kebab-case
- 合理的组件拆分
- 完整的错误处理
- 规范的代码注释
