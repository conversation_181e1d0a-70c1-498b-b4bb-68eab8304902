<template>
  <div>
    <Input v-model="formData.roomName" :placeholder="placeholder" search enter-button="选择"
           @on-search="openModal = true"/>
    <!-- 被监管人员选择组件 -->
    <Modal v-model="openModal" :mask-closable="false" :closable="true" class-name="select-use-modal" width="1360"
           title="人员列表">
      <div class="select-use">
        <roomSelect v-if="openModal" ref="roomSelect" ryzt="ALL" :isMultiple='false'
                    :selectUseIds="formData.jgrybm"/>
      </div>
      <div slot="footer">
        <Button type="primary" @click="useSelect" class="save">确 定</Button>
        <Button @click="openModal = false" class="save">关 闭</Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import {roomSelect} from "sd-room-select"

export default {
  components: {roomSelect},
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      jgrybm: '',
      openModal: false,
      formData: {
        roomName: '',
        roomCode: this.value
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal !== this.formData.roomCode) {
          this.formData.roomCode = newVal
        }
      },
      immediate: true
    }
  },
  methods: {
    useSelect() {
      if (this.$refs.roomSelect.checkedRoom && this.$refs.roomSelect.checkedRoom.length > 0) {
        this.formData = this.$refs.roomSelect.checkedRoom[0]
        console.log(this.formData, 'this.formData')
        this.$emit('on-select-success', this.formData, this.formData.roomCode)
        this.openModal = false
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
    }
  }
}
</script>
