<template>
  <div>
    <div class="detainEnterBoxData" v-if="!showData && !showHistory && !jgrybm">
      <statisticalAnalysis mark='bjsdsksscsjybr'  />
      <statisticalAnalysis mark='bjsdsksscsjybz'  />
      <statisticalAnalysis mark='bjsdsksscsjyby'  />
      <statisticalAnalysis mark='bjsdsksscsjybjd'  />
      <statisticalAnalysis mark='bjsdsksscsjybn'  />
      <statisticalAnalysis mark='bjsdsksscsjyls'  />
    </div>
    <div class="bsp-base-form" :class="!showData && !showHistory && !jgrybm ? 'data-content' : 'data-content2'" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData || showHistory">
        {{ modalTitle }}
      </div>

      <div class="bsp-base-content" v-if="!showData && !showHistory">

        <s-DataGrid ref="grid" funcMark="csjydjlb" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode + ':csjydjlb:csjy')" @click.native="editEvent(row,false)">
                  {{getStepName(row)}}
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode+':csjydjlb:csjy') && row.cnt > 0" @click.native="showHistoryEvent(row)">
                  就医记录
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>


<!--             <span style="margin-left: 6px;font-size: 14px !important;cursor: pointer;color:#2D8cF0;" class="archive" type="text" size="small"  v-if="func.includes(globalAppCode + ':csjydjlb:csjy')" @click="editEvent(row,false)"-->
<!--             >出所就医</span>-->
<!--            <Button type="primary" v-if="func.includes(globalAppCode + ':csjydjlb:csjy')" style="margin-right: 20px;" @click.native="editEvent(row,false)" >出所就医</Button>-->
          </template>

          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <outForTreatmentMain v-if="showData" @close="close()" :saveType="saveType" :quickRegistration="quickRegistration" :rowData="rowData"/>

      <history  v-if="showHistory" :rowData="rowData" @close="close()"></history>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import outForTreatmentMain from '../outForTreatmentMain.vue'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import history from '../recordManage/index'
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      outForTreatmentMain,
      history
    },
     props:{
      jgrybm: {
        type: String,
        default: ''
      },
    },
    data() {
      return {
        params:{},
        showData: false,
        showHistory:false,
        modalTitle: '出所就医登记',
        saveType: 'add',
        quickRegistration:false,
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      close(){
        this.showData = false
        this.showHistory = false
      },
      addEvent(quickRegistration) {
        this.saveType='add'
        this.quickRegistration = quickRegistration
        this.showData=true
        this.showHistory = false
        this.rowData = {}
      },
      editEvent(row,quickRegistration){
        this.rowData = row;
        this.rowData.rybh = this.rowData.jgrybm
        this.showData=true
        this.showHistory = false
        this.saveType='edit'
        this.quickRegistration = quickRegistration
      },
      getStepName(row){

        if(row.current_step == "01"){
          if(row.status == "01"){
            return "医生呈批"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "02"){
          return "主任意见"
        }else if(row.current_step == "03"){
          if(row.status == "01"){
            return "勤务安排"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "04"){
          return "审批"
        }else if(row.current_step == "05"){
          if(row.status == "01"){
            return "确认"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "06"){
          if(row.status == "01"){
            return "勤务安排"
          }else{
            return "编辑"
          }
        }else if(row.current_step == "07"){
          return "审批"
        }else if(row.current_step == "08"){
          if(row.status == "01"){
            return "确认"
          }else{
            return "编辑"
          }
        }else{
          return "医生呈批"
        }
      },
      showHistoryEvent(row){
        this.rowData = row;
        this.rowData.rybh = this.rowData.jgrybm
        this.showData=false
        this.showHistory = true
      },
      getDetail(){
        let jgrybm = this.$route.query.eventCode;
        let step = this.$route.query.step;
        this.rowData = {}
        this.rowData.current_step = step
        this.rowData.rybh = jgrybm
        this.rowData.jgrybm = jgrybm
        this.showData=true
        this.showHistory = false
        this.saveType='edit'
        this.quickRegistration = false
        this.$store.dispatch('authGetRequest',{
          url: this.$path.app_outHospitalGetByJgrybm,
          params: {
            jgrybm:jgrybm
          }
        }).then(resp => {
          if(resp.code == 0){
            if(resp.data){
              this.rowData =  resp.data
              this.rowData.rybh = this.rowData.jgrybm
              this.rowData.current_step = this.rowData.currentStep
              this.rowData.act_inst_id = this.rowData.actInstId
              this.rowData.hs_act_inst_id= this.rowData.hsActInstId
              this.showData=true
              this.showHistory = false
              this.saveType='edit'
              this.quickRegistration = false
            }
          }else{
            this.$Modal.error({
              title: '温馨提示',
              content: resp.msg || '操作失败'
            })
          }
        })
      },
      isToApproval(){
        const eventCode = this.$route.query.eventCode;
        if (eventCode) {
          return true
        } else {
          return false
        }
      },
    },
    mounted(){
      console.log("页面加载----");
      if(this.isToApproval()){
        this.getDetail()
      }
    }
  }
</script>

<style scoped>
  .detainEnterBoxData{
    display: flex;
    align-content: center;
  }

.bsp-base-tit {
  position: relative;
  top: 10px;
}
</style>
