<template>
  <s-general-history
    v-if="actInstId"
    :key="timer"
    :showModifyBtn="false"
    :showRevokeBtn="false"
    :actInstId="actInstId">
  </s-general-history>
</template>

<script>
  import { sGeneralHistory } from 'sd-general-history'
  export default {
    components:{
      sGeneralHistory
    },
    props:{
      actInstId:String,
    },
    data(){
      return{
        timer: ''
      }
    },
    watch: {
      actInstId () {
        if (this.actInstId) {
          this.timer = new Date().getTime()
        }
      },
    },

  }
</script>

<style scoped>

</style>
