<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpkssjyywrsbrsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbzsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbysys'  />
      <statisticalAnalysis mark='acpkssjyywrsbjdsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbnsys'  />
      <statisticalAnalysis mark='acpkssjyywrslssys'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>
      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="syrsksssytz" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Dropdown>
              <a href="javascript:void(0)">
                操作
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <DropdownItem v-if="func.includes(globalAppCode + ':syrsksssytz:xq')" @click.native="showInfo(row)">
                  详情
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode + ':syrsksssytz:xxbu')" @click.native="supplementInfo(row)">
                  信息补录
                </DropdownItem>
                <DropdownItem v-if="func.includes(globalAppCode + ':syrsksssytz:wsdy')" @click.native="archive(row)">
                  文书打印
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </template>
          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
<!--      <detail v-if="showData" @close="showData=false" :rowData="rowData"/>-->
      <allDetail v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import detail from "./detail.vue"
  import allDetail from "./allDetail"
  import {mapActions, mapMutations} from "vuex";
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      detail,
      allDetail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '收押台账详情',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      ...mapMutations([ 'setTagNavList',]),
      showInfo(row) {
        this.rowData = row;
        this.showData=true
      },
      supplementInfo(row){
        this.rowData = row;
      },
      archive(row) {
        this.$router.push({
          path:'/archive',
          query: {
            id:row.id,
            dicCode:"ZD_SYRSWSDY",
            formId:"1933800910857834496"
          },
        })
        let res={
          path:'/archive',
          name: "archive",
          meta: {hideInMenu: true, title: "收押台账-文书打印", notCache: true, icon: ""}
        }
        let arr=localStorage.getItem('tagNaveList')?JSON.parse(localStorage.getItem('tagNaveList')):[]
        arr.unshift(res)
        this.setTagNavList(arr)
      },
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
