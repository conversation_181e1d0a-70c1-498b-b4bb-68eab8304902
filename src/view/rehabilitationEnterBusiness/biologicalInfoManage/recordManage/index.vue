<template>
  <div>
    <div class="detentionEnterBoxData" v-if="!showData">
      <statisticalAnalysis mark='acpkssjyywrsbrsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbzsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbysys'  />
      <statisticalAnalysis mark='acpkssjyywrsbjdsys'  />
      <statisticalAnalysis mark='acpkssjyywrsbnsys'  />
      <statisticalAnalysis mark='acpkssjyywrslssys'  />
    </div>
    <div class="bsp-base-form" :class="!showData ? 'data-content' : ''" style="overflow: hidden;">
      <div class="bsp-base-tit" v-if="showData">
        {{ modalTitle }}
      </div>
      <div class="bsp-base-content" v-if="!showData">

        <s-DataGrid ref="grid" funcMark="syrs-kss-swxxcjtzlb" :customFunc="true" :params="params" >
          <template slot="customHeadFunc" slot-scope="{ func }">

          </template>
          <template slot="customRowFunc" slot-scope="{ func, row, index }">
            <Button type="primary" v-if="func.includes(globalAppCode + ':syrs-kss-swxxcjtzlb:xq')" style="margin-right: 20px;" @click.native="showInfo(row)" >详情</Button>
          </template>
          <template slot="slot_status" slot-scope="{ row, index }">

          </template>

        </s-DataGrid>
      </div>
      <detail v-if="showData" @close="showData=false" :rowData="rowData"/>
    </div>
  </div>
</template>

<script>
  import  {sDataGrid}  from  'sd-data-grid'
  import {statisticalAnalysis }  from 'sd-statistical-analysis'
  import detail from "./detail.vue"
  import {mapActions} from "vuex";
  export default {
    components: {
      sDataGrid,
      statisticalAnalysis,
      detail
    },
    data() {
      return {
        params:{},
        showData: false,
        modalTitle: '生物信息采集台账详情',
        rowData:{}
      }
    },
    methods: {
      ...mapActions(['postRequest', 'authGetRequest', 'authPostRequest', 'getRequest']),
      showInfo(row) {
        this.rowData = row;
        this.showData=true
      }
    }
  }
</script>

<style scoped>
  .detentionEnterBoxData{
    display: flex;
    align-content: center;
  }

</style>
